[Licensing::Module] Trying to connect to existing licensing client channel...
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-ilias" at "2025-08-15T19:51:23.3669237Z"
Built from '6000.2/respin/6000.2.0f1-517f89d850d1' branch; Version is '6000.2.0f1 (eed1c594c913) revision 15651269'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 65462 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-08-15T19:51:23Z

COMMAND LINE ARGUMENTS:
C:\Unity\Editors\6000.2.0f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
C:/Unity/BLAME/BLAME
-logFile
Logs/AssetImportWorker1.log
-srvPort
55728
-licensingIpc
LicenseClient-ilias
-job-worker-count
5
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Unity/BLAME/BLAME
C:/Unity/BLAME/BLAME
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 18644, path: "C:/Program Files/Unity Hub/UnityLicensingClient_V1/Unity.Licensing.Client.exe")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Error: HandshakeResponse reported an error:
	ResponseCode: 505
	ResponseStatus: Unsupported protocol version '1.17.1'.
[Licensing::Module] Error: Failed to handshake to channel: "LicenseClient-ilias"
[Licensing::IpcConnector] LicenseClient-ilias channel disconnected successfully.
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-ilias-6000.2.0" at "2025-08-15T19:51:23.4700958Z"
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 290512, path: "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/Licensing/Client/Unity.Licensing.Client.exe")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.17.1+ae67fbc
  Session Id:              7f149f8ab84648ff8061d2235cde6f08
  Correlation Id:          5f91f71ccf6c9acad3d19ab3e1610c0c
  External correlation Id: 1619475618451599135
  Machine Id:              /GaE4A2aXaDfTh/RbExLPR8H29M=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-ilias-6000.2.0" (connect: 0.00s, validation: 0.00s, handshake: 0.02s)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-ilias-6000.2.0-notifications" at "2025-08-15T19:51:23.4937286Z"
[Licensing::Module] Licensing Background thread has ended after 0.13s
Player connection [287248]  Target information:

Player connection [287248]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] ********** [EditorId] ********** [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [287248]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] ********** [EditorId] ********** [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [287248]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] ********** [EditorId] ********** [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [287248] Host joined multi-casting on [***********:54997]...
Player connection [287248] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 5
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Active build profile path: `Assets/Settings/Build Profiles/New Windows Profile.asset`
Got setting from build profile YAML, name `m_BuildTarget`, value `19`
Got setting from build profile YAML, name `m_Subtarget`, value `2`
Refreshing native plugins compatible for Editor in 1453.75 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.2.0f1 (eed1c594c913)
[Subsystems] Discovering subsystems at path C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Unity/BLAME/BLAME/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3060 Ti (ID=0x2489)
    Vendor:   NVIDIA
    VRAM:     8024 MB
    Driver:   32.0.15.8088
Initialize mono
Mono path[0] = 'C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed'
Mono path[1] = 'C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56792
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.019689 seconds.
- Loaded All Assemblies, in  3.424 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.812 seconds
Domain Reload Profiling: 5151ms
	BeginReloadAssembly (1704ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (154ms)
	RebuildNativeTypeToScriptingClass (153ms)
	initialDomainReloadingComplete (430ms)
	LoadAllAssembliesAndSetupDomain (897ms)
		LoadAssemblies (1672ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (887ms)
			TypeCache.Refresh (884ms)
				TypeCache.ScanAssembly (827ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1813ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1618ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (190ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (496ms)
			ProcessInitializeOnLoadAttributes (514ms)
			ProcessInitializeOnLoadMethodAttributes (408ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Got setting from build profile YAML, name `m_Development`, value `0`
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  9.417 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 38.34 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-Persistent Object
Launched and connected shader compiler UnityShaderCompiler.exe after 2.56 seconds
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 44.940 seconds
Domain Reload Profiling: 54274ms
	BeginReloadAssembly (2161ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (292ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (111ms)
	RebuildCommonClasses (282ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (347ms)
	LoadAllAssembliesAndSetupDomain (6520ms)
		LoadAssemblies (5820ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (2174ms)
			TypeCache.Refresh (1860ms)
				TypeCache.ScanAssembly (1796ms)
			BuildScriptInfoCaches (273ms)
			ResolveRequiredComponents (31ms)
	FinalizeReload (44941ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (29700ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (81ms)
			BeforeProcessingInitializeOnLoad (1339ms)
			ProcessInitializeOnLoadAttributes (1723ms)
			ProcessInitializeOnLoadMethodAttributes (26515ms)
			AfterProcessingInitializeOnLoad (28ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14611ms)
Refreshing native plugins compatible for Editor in 229.37 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7983 unused Assets / (5.7 MB). Loaded Objects now: 9030.
Memory consumption went from 300.1 MB to 294.4 MB.
Total: 27.683500 ms (FindLiveObjects: 4.291100 ms CreateObjectMapping: 1.815600 ms MarkObjects: 16.627800 ms  DeleteObjects: 4.947500 ms)

========================================================================
Received Import Request.
  Time since last request: 507203.259941 seconds.
  path: Assets/GameObject.fbx
  artifactKey: Guid(4758b9100a3723b4bbf43bdfb57320dd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameObject.fbx using Guid(4758b9100a3723b4bbf43bdfb57320dd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Launched and connected shader compiler UnityShaderCompiler.exe after 0.05 seconds
Launched and connected shader compiler UnityShaderCompiler.exe after 0.11 seconds
Launched and connected shader compiler UnityShaderCompiler.exe after 0.05 seconds
Launched and connected shader compiler UnityShaderCompiler.exe after 0.34 seconds
Launched and connected shader compiler UnityShaderCompiler.exe after 0.20 seconds
 -> (artifact id: '5dcbfbde94129e0a25e2b9a6bf204036') in 252.97114049999999 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 43

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/GameObject.prefab
  artifactKey: Guid(7e1992961888daa4c9e10b6ed188fd28) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameObject.prefab using Guid(7e1992961888daa4c9e10b6ed188fd28) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '729f9758839ec643e99f52d3510840e8') in 0.4825283 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 92

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Leak Detected : Persistent allocates 8 individual allocations. To find out more please enable 'Preferences > Jobs > Leak Detection Level > Enabled With Stack Trace' and reproduce the leak again.
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.769 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 32.28 ms, found 35 plugins.
Native extension for WindowsStandalone target not found
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <6460d52f11fa4eaeabfca1cf2e9eca8f>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  6.355 seconds
Domain Reload Profiling: 9094ms
	BeginReloadAssembly (640ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (105ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (110ms)
	RebuildCommonClasses (74ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (78ms)
	LoadAllAssembliesAndSetupDomain (1926ms)
		LoadAssemblies (1401ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (749ms)
			TypeCache.Refresh (425ms)
				TypeCache.ScanAssembly (402ms)
			BuildScriptInfoCaches (282ms)
			ResolveRequiredComponents (23ms)
	FinalizeReload (6355ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (5666ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (3259ms)
			ProcessInitializeOnLoadAttributes (1784ms)
			ProcessInitializeOnLoadMethodAttributes (557ms)
			AfterProcessingInitializeOnLoad (51ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (119ms)
Refreshing native plugins compatible for Editor in 337.04 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 3.96 ms.
Unloading 146 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10655 unused Assets / (27.4 MB). Loaded Objects now: 11706.
Memory consumption went from 379.5 MB to 352.1 MB.
Total: 127.816500 ms (FindLiveObjects: 3.341300 ms CreateObjectMapping: 4.272100 ms MarkObjects: 30.160100 ms  DeleteObjects: 90.041100 ms)

Prepare: number of updated asset objects reloaded= 1
========================================================================
Received Import Request.
  Time since last request: 1159.902488 seconds.
  path: Assets/Settings/HDRPDefaultResources/HDRenderPipelineGlobalSettings.asset
  artifactKey: Guid(ac0316ca287ba459492b669ff1317a6f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Settings/HDRPDefaultResources/HDRenderPipelineGlobalSettings.asset using Guid(ac0316ca287ba459492b669ff1317a6f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ea250f8604bcb615170230d2703595c8') in 0.0230296 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 15.877223 seconds.
  path: Assets/_Game/Scenes/Main/Gold.mat
  artifactKey: Guid(ccd64db7074208049b6eefbaaf741e25) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/Gold.mat using Guid(ccd64db7074208049b6eefbaaf741e25) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9f7a2c73128ee31f60f126c9d0cd4899') in 0.9798228 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/_Game/Scenes/Main/Ground.mat
  artifactKey: Guid(79b21e146fb4da848825e3737288c8ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/Ground.mat using Guid(79b21e146fb4da848825e3737288c8ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ba53213f5ce61bbd5b94b30e76689c23') in 0.0204351 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 951.133502 seconds.
  path: Assets/_Game/Models/catch.prefab
  artifactKey: Guid(5bfa8dd4d78ce304d9e1d15eebb7d79e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/catch.prefab using Guid(5bfa8dd4d78ce304d9e1d15eebb7d79e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e1a178e7ed7eced0203d0eef289e2551') in 1.7568876 seconds
Number of updated asset objects reloaded before import = 45Number of asset objects unloaded after import = 33

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0