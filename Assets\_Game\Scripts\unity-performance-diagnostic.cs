using UnityEngine;
using UnityEngine.Profiling;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using System.IO;
using UnityEngine.Rendering;
using UnityEngine.Rendering.HighDefinition;

public class PerformanceDiagnosticManager : MonoBehaviour
{
    [Header("Diagnostic Settings")]
    [SerializeField] private bool enableDiagnostics = true;
    [SerializeField] private bool logToFile = true;
    [SerializeField] private float sampleInterval = 0.5f;
    
    private StringBuilder diagnosticLog = new StringBuilder();
    private float lastSampleTime;
    private int frameCount;
    private float deltaTimeAccumulator;
    private List<float> frameTimes = new List<float>();
    private bool hasLoggedStartup = false;
    
    // Performance thresholds
    private const float CRITICAL_FRAMETIME = 100f; // ms
    private const float WARNING_FRAMETIME = 33f; // ms
    
    private void Awake()
    {
        // Persist through scene loads to track the issue
        DontDestroyOnLoad(gameObject);
        
        // Log initial state
        LogDiagnostic("=== PERFORMANCE DIAGNOSTIC START ===");
        LogDiagnostic($"Scene: {UnityEngine.SceneManagement.SceneManager.GetActiveScene().name}");
        LogDiagnostic($"Time since startup: {Time.realtimeSinceStartup}s");
        LogDiagnostic($"Target Frame Rate: {Application.targetFrameRate}");
        LogDiagnostic($"VSync Count: {QualitySettings.vSyncCount}");
        LogDiagnostic($"Quality Level: {QualitySettings.names[QualitySettings.GetQualityLevel()]}");
        
        // Check for common issues
        CheckForCommonIssues();

        // Log detailed state including fog diagnostic
        LogDetailedState();

        // Start monitoring
        StartCoroutine(MonitorPerformance());
        StartCoroutine(DelayedStartupCheck());
    }
    
    private void CheckForCommonIssues()
    {
        // Check shader compilation - this is often the culprit
        // NOTE: Shader.WarmupAllShaders() is NOT used as it causes GPU driver crashes
        LogDiagnostic("Checking for shader issues...");
        if (Shader.Find("Standard") == null)
        {
            LogDiagnostic("WARNING: Standard shader not found - possible shader stripping issue");
        }
        
        // Check memory usage
        long totalMemory = Profiler.GetTotalReservedMemoryLong();
        long allocatedMemory = Profiler.GetTotalAllocatedMemoryLong();
        LogDiagnostic($"Memory - Reserved: {totalMemory / 1024 / 1024}MB, Allocated: {allocatedMemory / 1024 / 1024}MB");
        
        // Check for missing references in persistence manager if it exists
        CheckPersistenceManager();
    }
    
    private void CheckPersistenceManager()
    {
        // AI Note: Looking for the persistence manager the user mentioned
        GameObject[] allObjects = FindObjectsByType<GameObject>(FindObjectsSortMode.None);
        foreach (var obj in allObjects)
        {
            if (obj.name.ToLower().Contains("persist") || obj.name.ToLower().Contains("manager"))
            {
                LogDiagnostic($"Found potential persistence object: {obj.name}");
                
                // Check for heavy operations in Awake/Start
                MonoBehaviour[] scripts = obj.GetComponents<MonoBehaviour>();
                foreach (var script in scripts)
                {
                    if (script != null)
                    {
                        LogDiagnostic($"  - Script: {script.GetType().Name}");
                    }
                    else
                    {
                        LogDiagnostic($"  - WARNING: Null script reference found!");
                    }
                }
            }
        }
    }
    
    private IEnumerator DelayedStartupCheck()
    {
        // Wait a bit to see if performance improves after initial load
        yield return new WaitForSeconds(3f);
        
        if (!hasLoggedStartup)
        {
            hasLoggedStartup = true;
            LogDiagnostic("=== 3 SECOND STARTUP CHECK ===");
            
            float avgFrameTime = CalculateAverageFrameTime();
            if (avgFrameTime > CRITICAL_FRAMETIME)
            {
                LogDiagnostic($"CRITICAL: Still experiencing severe lag after 3s (avg: {avgFrameTime}ms)");
                AttemptEmergencyFixes();
            }
            else if (avgFrameTime > WARNING_FRAMETIME)
            {
                LogDiagnostic($"WARNING: Performance suboptimal after 3s (avg: {avgFrameTime}ms)");
            }
            else
            {
                LogDiagnostic($"Performance normalized after startup (avg: {avgFrameTime}ms)");
            }
        }
    }
    
    private void AttemptEmergencyFixes()
    {
        LogDiagnostic("Attempting emergency performance fixes...");

        // REMOVED: Shader.WarmupAllShaders() - causes GPU device removal crashes!
        // DO NOT USE shader warmup in builds - it's unstable on many drivers
        LogDiagnostic("- Skipped shader warmup (prevents crashes)");

        // Force garbage collection
        System.GC.Collect();
        LogDiagnostic("- Forced garbage collection");

        // Unload unused assets
        Resources.UnloadUnusedAssets();
        LogDiagnostic("- Unloaded unused assets");

        // Check if specific to main menu
        if (UnityEngine.SceneManagement.SceneManager.GetActiveScene().name.ToLower().Contains("main"))
        {
            LogDiagnostic("- Issue detected in main menu scene specifically");
            // Could trigger a scene reload here if needed
        }
    }
    
    private IEnumerator MonitorPerformance()
    {
        while (enableDiagnostics)
        {
            yield return new WaitForSeconds(sampleInterval);
            
            float currentFrameTime = Time.deltaTime * 1000f; // Convert to ms
            frameTimes.Add(currentFrameTime);
            
            // Keep only last 100 samples
            if (frameTimes.Count > 100)
            {
                frameTimes.RemoveAt(0);
            }
            
            // Log spikes
            if (currentFrameTime > CRITICAL_FRAMETIME)
            {
                LogDiagnostic($"CRITICAL FRAME SPIKE: {currentFrameTime}ms at {Time.realtimeSinceStartup}s");
                LogDetailedState();
            }
            else if (currentFrameTime > WARNING_FRAMETIME)
            {
                LogDiagnostic($"Frame spike: {currentFrameTime}ms");
            }
        }
    }
    
    private void LogDetailedState()
    {
        // Log detailed state when spike occurs
        LogDiagnostic("=== DETAILED STATE DUMP ===");
        LogDiagnostic($"Active Scene: {UnityEngine.SceneManagement.SceneManager.GetActiveScene().name}");
        LogDiagnostic($"Object Count: {FindObjectsByType<GameObject>(FindObjectsSortMode.None).Length}");
        // Note: UnityStats not available in all Unity versions, using alternatives
        LogDiagnostic($"Renderer Count: {FindObjectsByType<Renderer>(FindObjectsSortMode.None).Length}");
        LogDiagnostic($"Light Count: {FindObjectsByType<Light>(FindObjectsSortMode.None).Length}");
        LogDiagnostic($"Camera Count: {FindObjectsByType<Camera>(FindObjectsSortMode.None).Length}");
        
        // Check for coroutines or async operations
        LogDiagnostic($"Time.timeScale: {Time.timeScale}");
        LogDiagnostic($"Time.maximumDeltaTime: {Time.maximumDeltaTime}");

        // Check fog settings
        CheckFogSettings();
    }
    
    private float CalculateAverageFrameTime()
    {
        if (frameTimes.Count == 0) return 0;
        
        float sum = 0;
        foreach (float time in frameTimes)
        {
            sum += time;
        }
        return sum / frameTimes.Count;
    }
    
    private void LogDiagnostic(string message)
    {
        string timestampedMessage = $"[{Time.realtimeSinceStartup:F2}s] {message}";
        Debug.Log(timestampedMessage);
        diagnosticLog.AppendLine(timestampedMessage);
        
        // Write to file periodically in builds
        if (logToFile && Application.isEditor == false)
        {
            WriteLogToFile();
        }
    }
    
    private void WriteLogToFile()
    {
        string path = Path.Combine(Application.persistentDataPath, "performance_diagnostic.txt");
        try
        {
            File.WriteAllText(path, diagnosticLog.ToString());
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to write diagnostic log: {e.Message}");
        }
    }
    
    private void OnDestroy()
    {
        if (logToFile)
        {
            LogDiagnostic("=== DIAGNOSTIC END ===");
            WriteLogToFile();
            Debug.Log($"Diagnostic log saved to: {Path.Combine(Application.persistentDataPath, "performance_diagnostic.txt")}");
        }
    }
    
    // Expose this for external scripts to report issues
    public void ReportPerformanceEvent(string eventName)
    {
        LogDiagnostic($"Performance Event: {eventName}");
        LogDetailedState();
    }

    private void CheckFogSettings()
    {
        LogDiagnostic("=== FOG DIAGNOSTIC ===");

        var rp = GraphicsSettings.currentRenderPipeline as HDRenderPipelineAsset;
        LogDiagnostic($"[HDRP] Quality='{QualitySettings.names[QualitySettings.GetQualityLevel()]}' RPAsset='{(rp ? rp.name : "null")}'");

        var cam = Camera.main;
        var hd = cam ? cam.GetComponent<HDAdditionalCameraData>() : null;
        if (hd != null)
        {
            // Get frame settings from the camera's render data
            var fs = hd.renderingPathCustomFrameSettings;
            LogDiagnostic($"[HDRP] FrameSettings Volumetrics={fs.IsEnabled(FrameSettingsField.Volumetrics)} " +
                          $"AtmosphericScattering={fs.IsEnabled(FrameSettingsField.AtmosphericScattering)} " +
                          $"VolumeLayerMask={hd.volumeLayerMask}");
            LogDiagnostic($"[HDRP] Camera customRenderingSettings={hd.customRenderingSettings}");
        }
        var fog = VolumeManager.instance.stack.GetComponent<Fog>();
        LogDiagnostic($"[HDRP] Fog override present={(fog != null)} active={(fog != null && fog.active)}");

        if (fog != null)
        {
            LogDiagnostic($"[HDRP] Fog enabled={fog.enabled.value} colorMode={fog.colorMode.value}");
            LogDiagnostic($"[HDRP] Fog baseHeight={fog.baseHeight.value} maximumHeight={fog.maximumHeight.value}");
            LogDiagnostic($"[HDRP] Fog meanFreePath={fog.meanFreePath.value}");
            if (fog.albedo.overrideState) LogDiagnostic($"[HDRP] Fog albedo={fog.albedo.value}");
        }

        if (fog != null && fog.active)
        {
            LogDiagnostic($"[HDRP] Fog enabled={fog.enabled.value} colorMode={fog.colorMode.value}");
            LogDiagnostic($"[HDRP] Fog baseHeight={fog.baseHeight.value} maximumHeight={fog.maximumHeight.value}");
            LogDiagnostic($"[HDRP] Fog meanFreePath={fog.meanFreePath.value} albedo={fog.albedo.value}");
            LogDiagnostic($"[HDRP] Fog anisotropy={fog.anisotropy.value} globalLightProbeDimmer={fog.globalLightProbeDimmer.value}");
        }
    }
}


