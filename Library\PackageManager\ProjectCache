m_ProjectFiles:
  m_ManifestFileStatus:
    m_FilePath: C:/Unity/BLAME/BLAME/Packages/manifest.json
    m_PathExists: 1
    m_ContentTrackingEnabled: 1
    m_ModificationDate:
      serializedVersion: 2
      ticks: 638908856317592882
    m_Hash: 2129634594
  m_LockFileStatus:
    m_FilePath: C:/Unity/BLAME/BLAME/Packages/packages-lock.json
    m_PathExists: 1
    m_ContentTrackingEnabled: 1
    m_ModificationDate:
      serializedVersion: 2
      ticks: 638908856318181345
    m_Hash: 547454109
m_EmbeddedPackageManifests:
  m_ManifestsStatus: {}
m_LocalPackages:
  m_LocalFileStatus: []
m_ProjectPath: C:/Unity/BLAME/BLAME/Packages
m_EditorVersion: 6000.2.0f1 (eed1c594c913)
m_ResolvedPackages:
- packageId: com.unity.2d.enhancers@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.2d.enhancers@1df0eb7756ab
  assetPath: Packages/com.unity.2d.enhancers
  name: com.unity.2d.enhancers
  displayName: 2D Enhancers
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: 2D Enhancers provides an integration of AI tools in the 2D suite.
  errors: []
  versions:
    all:
    - 1.0.0-pre.1
    - 1.0.0
    compatible:
    - 1.0.0-pre.1
    - 1.0.0
    recommended: 
    deprecated: []
  dependencies:
  - name: com.unity.2d.common
    version: 11.0.1
  - name: com.unity.ai.generators
    version: 1.0.0-pre.12
  - name: com.unity.settings-manager
    version: 2.0.1
  - name: com.unity.modules.uielements
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.2d.common
    version: 11.0.1
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.burst
    version: 1.8.23
  - name: com.unity.ai.generators
    version: 1.0.0-pre.19
  - name: com.unity.ai.toolkit
    version: 1.0.0-pre.19
  - name: com.unity.nuget.newtonsoft-json
    version: 3.2.1
  - name: com.unity.settings-manager
    version: 2.1.0
  keywords:
  - 2d
  - AI
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638863413378290000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.2d.enhancers@1.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/2d.git
    revision: b0c25ab984f22def61409df195e4e0b210863e0d
    path: 
  unityLifecycle:
    version: 
    nextVersion: 1.0.0-pre.1
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n- Brush settings overlay displayed despite
    no active brush. (case DANB-983)\n- Prevent a new Generate window from opening
    after asset promotion. (case DANB-975)\n- Misaligned slider in the Results tray.
    (case DANB-973)\n- Broken undo / redo in the Inpaint mode. (case DANB-981)\n-
    Fixed internal namespace. (case DANB-1017)\n- Sprite scene preview doesn''t work
    if result is not selected (case DANB-1033)"}'
  assetStore:
    productId: 
  fingerprint: 1df0eb7756ab7f6eaea96cdc2885716e0714ea21
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0-pre.1
    minimumUnityVersion: 6000.2.0a9
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.2d.sprite@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb
  assetPath: Packages/com.unity.2d.sprite
  name: com.unity.2d.sprite
  displayName: 2D Sprite
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: Use Unity Sprite Editor Window to create and edit Sprite asset properties
    like pivot, borders and Physics shape
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - 2d
  - sprite
  - sprite editor window
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 28296e5d02fbf3ac81cc11ab1b39667f16a80778
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 2019.2.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.ai.assistant@1.0.0-pre.12
  testable: 0
  isDirectDependency: 1
  version: 1.0.0-pre.12
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.ai.assistant@91c166a13c3b
  assetPath: Packages/com.unity.ai.assistant
  name: com.unity.ai.assistant
  displayName: Assistant
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: 'Use AI for project-aware information retrieval, troubleshooting,
    code generation, assistance, and scene editing.


    Ask

    Solve hard problems
    or get ideas for your project. Assistant provides you with relevant answers based
    on the context of your project.


    Run

    Automate actions in the Editor
    to tackle repetitive tasks and streamline workflows. Use natural language to
    scatter objects across terrain, adjust lighting, rename assets, restructure project
    files, and more.


    Code

    Reduce the time and effort required to structure
    gameplay mechanics by using Assistant to create pre-compiled scripts.'
  errors: []
  versions:
    all:
    - 1.0.0-pre.2
    - 1.0.0-pre.3
    - 1.0.0-pre.4
    - 1.0.0-pre.5
    - 1.0.0-pre.6
    - 1.0.0-pre.7
    - 1.0.0-pre.8
    - 1.0.0-pre.9
    - 1.0.0-pre.10
    - 1.0.0-pre.11
    - 1.0.0-pre.12
    compatible:
    - 1.0.0-pre.2
    - 1.0.0-pre.3
    - 1.0.0-pre.4
    - 1.0.0-pre.5
    - 1.0.0-pre.6
    - 1.0.0-pre.7
    - 1.0.0-pre.8
    - 1.0.0-pre.9
    - 1.0.0-pre.10
    - 1.0.0-pre.11
    - 1.0.0-pre.12
    recommended: 1.0.0-pre.12
    deprecated: []
  dependencies:
  - name: com.unity.ai.toolkit
    version: 1.0.0-pre.18
  - name: com.unity.serialization
    version: 3.1.1
  - name: com.unity.nuget.newtonsoft-json
    version: 3.2.1
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.ai.toolkit
    version: 1.0.0-pre.19
  - name: com.unity.nuget.newtonsoft-json
    version: 3.2.1
  - name: com.unity.serialization
    version: 3.1.2
  - name: com.unity.burst
    version: 1.8.23
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.7
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.nuget.mono-cecil
    version: 1.11.5
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638906224390580000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ai.assistant@1.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/muse-editor.git
    revision: 81800939ecf2017f2b6b1afd61a107665d743008
    path: 
  unityLifecycle:
    version: 
    nextVersion: 1.0.0-pre.7
    recommendedVersion: 1.0.0-pre.5
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"- Update AI-Toolkit to 1.0.0-pre.18"}'
  assetStore:
    productId: 
  fingerprint: 91c166a13c3bef5814c5a13a69fa442a657dd539
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0-pre.2
    minimumUnityVersion: 6000.2.0b4
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.ai.generators@1.0.0-pre.19
  testable: 0
  isDirectDependency: 1
  version: 1.0.0-pre.19
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.ai.generators@49eb4eaccb48
  assetPath: Packages/com.unity.ai.generators
  name: com.unity.ai.generators
  displayName: Generators
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Unity AI Generators is a package that provides tools for adding generative
    AI to Unity asset workflows.
  errors: []
  versions:
    all:
    - 1.0.0-pre.2
    - 1.0.0-pre.3
    - 1.0.0-pre.4
    - 1.0.0-pre.5
    - 1.0.0-pre.6
    - 1.0.0-pre.7
    - 1.0.0-pre.8
    - 1.0.0-pre.9
    - 1.0.0-pre.10
    - 1.0.0-pre.11
    - 1.0.0-pre.12
    - 1.0.0-pre.13
    - 1.0.0-pre.14
    - 1.0.0-pre.15
    - 1.0.0-pre.16
    - 1.0.0-pre.17
    - 1.0.0-pre.18
    - 1.0.0-pre.19
    compatible:
    - 1.0.0-pre.2
    - 1.0.0-pre.3
    - 1.0.0-pre.4
    - 1.0.0-pre.5
    - 1.0.0-pre.6
    - 1.0.0-pre.7
    - 1.0.0-pre.8
    - 1.0.0-pre.9
    - 1.0.0-pre.10
    - 1.0.0-pre.11
    - 1.0.0-pre.12
    - 1.0.0-pre.13
    - 1.0.0-pre.14
    - 1.0.0-pre.15
    - 1.0.0-pre.16
    - 1.0.0-pre.17
    - 1.0.0-pre.18
    - 1.0.0-pre.19
    recommended: 1.0.0-pre.19
    deprecated: []
  dependencies:
  - name: com.unity.ai.toolkit
    version: 1.0.0-pre.19
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.nuget.newtonsoft-json
    version: 3.2.1
  resolvedDependencies:
  - name: com.unity.ai.toolkit
    version: 1.0.0-pre.19
  - name: com.unity.nuget.newtonsoft-json
    version: 3.2.1
  - name: com.unity.mathematics
    version: 1.3.2
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638907945053580000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ai.generators@1.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/UnityAIWorkflows.git
    revision: 55389365acd56d9d9c807df4560c5a77a6af63c4
    path: 
  unityLifecycle:
    version: 
    nextVersion: 1.0.0-pre.8
    recommendedVersion: 1.0.0-pre.8
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Added\n\n- Added Asset Reference field in Generation
    Data for sprite generators.\n\n### Fixed\n\n- Fixed badge visibility on non-upscaled
    generations.\n- Fixed Asset Reference not being cleared correctly in Image references."}'
  assetStore:
    productId: 
  fingerprint: 49eb4eaccb48853a1ca7a7f6853ae94c01f07ff3
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0-pre.2
    minimumUnityVersion: 6000.2.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.ai.inference@2.3.0
  testable: 0
  isDirectDependency: 1
  version: 2.3.0
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.ai.inference@4ac711cab9a3
  assetPath: Packages/com.unity.ai.inference
  name: com.unity.ai.inference
  displayName: Inference Engine
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: 'Inference Engine is a neural network inference library. It enables
    you to import trained neural network models, connect the network inputs and outputs
    to your game code, and then run them locally in your end-user app. Use cases
    include capabilities like natural language processing, object recognition, automated
    game opponents, sensor data classification, and many more.


    Inference Engine
    automatically optimizes your network for real-time use to speed up inference.
    It also allows you to tune your implementation further with tools like frame
    slicing, quantization, and custom backend (i.e. compute type) dispatching.


    Visit
    https://unity.com/ai for more resources.'
  errors: []
  versions:
    all:
    - 2.2.0
    - 2.2.1
    - 2.3.0
    compatible:
    - 2.2.1
    - 2.3.0
    recommended: 2.2.1
    deprecated: []
  dependencies:
  - name: com.unity.burst
    version: 1.8.17
  - name: com.unity.dt.app-ui
    version: 1.3.1
  - name: com.unity.collections
    version: 2.4.3
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.burst
    version: 1.8.23
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.dt.app-ui
    version: 1.3.1
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.androidjni
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  - name: com.unity.modules.screencapture
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.7
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.5
  - name: com.unity.test-framework.performance
    version: 3.1.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638894670194790000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ai.inference@2.3/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/UnityInferenceEngine.git
    revision: 1465860dffef8a5bc07551b6d40b5f53c3739e3f
    path: 
  unityLifecycle:
    version: 2.2.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Added\n- Model Visualizer for inspecting models
    as node-based graphs inside the Unity Editor\n- Support for `Tensor<int>` input
    for `GatherND` operator on `GPUPixel` backend\n- Support for `Tensor<int>` input
    for the base of the `Pow` operator on all backends\n- Support for the `group`
    and `dilations` arguments for the `ConvTranspose` operator on all backends\n-
    Support for `value_float`, `value_floats`, `value_int` and `value_ints` values
    in ONNX `Constant` operators\n\n### Changed\n- Optimized single-argument operators
    on `CPU` backend\n- Optimized deserialization of models to avoid reflection at
    runtime\n\n### Fixed\n- Einsum operator now works correctly on fallback path"}'
  assetStore:
    productId: 
  fingerprint: 4ac711cab9a36baa41c7c65b01461f91c0537337
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.2.1
    minimumUnityVersion: 6000.0.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.collab-proxy@2.8.2
  testable: 0
  isDirectDependency: 1
  version: 2.8.2
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f
  assetPath: Packages/com.unity.collab-proxy
  name: com.unity.collab-proxy
  displayName: Version Control
  author:
    name: 
    email: 
    url: 
  category: Editor
  type: 
  description: The package gives you the ability to use Unity Version Control in
    the Unity editor. To use Unity Version Control, a subscription is required. Learn
    more about how you can get started for free by visiting https://unity.com/solutions/version-control
  errors: []
  versions:
    all:
    - 1.2.3-preview
    - 1.2.4-preview
    - 1.2.6
    - 1.2.7
    - 1.2.9
    - 1.2.11
    - 1.2.15
    - 1.2.16
    - 1.2.17-preview.3
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.6
    - 1.3.7
    - 1.3.8
    - 1.3.9
    - 1.4.9
    - 1.5.7
    - 1.6.0
    - 1.7.1
    - 1.8.0
    - 1.9.0
    - 1.10.2
    - 1.11.2
    - 1.12.5
    - 1.13.5
    - 1.14.1
    - 1.14.4
    - 1.14.7
    - 1.14.9
    - 1.14.12
    - 1.14.13
    - 1.14.15
    - 1.14.16
    - 1.14.17
    - 1.14.18
    - 1.15.1
    - 1.15.4
    - 1.15.7
    - 1.15.9
    - 1.15.12
    - 1.15.13
    - 1.15.15
    - 1.15.16
    - 1.15.17
    - 1.15.18
    - 1.17.0
    - 1.17.1
    - 1.17.2
    - 1.17.6
    - 1.17.7
    - 2.0.0-preview.6
    - 2.0.0-preview.8
    - 2.0.0-preview.15
    - 2.0.0-preview.17
    - 2.0.0-preview.20
    - 2.0.0-preview.21
    - 2.0.0-preview.22
    - 2.0.0
    - 2.0.1
    - 2.0.3
    - 2.0.4
    - 2.0.5
    - 2.0.7
    - 2.1.0-preview.3
    - 2.1.0-preview.5
    - 2.1.0-preview.6
    - 2.1.0
    - 2.2.0
    - 2.3.1
    - 2.4.3
    - 2.4.4
    - 2.5.1
    - 2.5.2
    - 2.6.0
    - 2.7.1
    - 2.8.1
    - 2.8.2
    - 2.9.1
    compatible:
    - 2.8.2
    - 2.9.1
    recommended: 2.8.2
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - backup
  - cloud
  - collab
  - collaborate
  - collaboration
  - control
  - devops
  - plastic
  - plasticscm
  - source
  - team
  - teams
  - version
  - vcs
  - uvcs
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638822079721750000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.collab-proxy@2.8/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.cloud.collaborate.git
    revision: c1fbc35e6bbae1ac0e9a3481e441dfdb3602b5ef
    path: 
  unityLifecycle:
    version: 2.8.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"- Fixed false positive error showing in console if
    creating a workspace from the Hub with a version of the Unity Editor shipping
    with a default Version Control package older than version 2.7.1."}'
  assetStore:
    productId: 
  fingerprint: c854d1f7d97fbe1905f3e3591ded6fe77d96e654
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.8.2
    minimumUnityVersion: 2021.3.0f1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.feature.development@1.0.2
  testable: 0
  isDirectDependency: 1
  version: 1.0.2
  source: 2
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.feature.development@767aadbc6eb7
  assetPath: Packages/com.unity.feature.development
  name: com.unity.feature.development
  displayName: Engineering
  author:
    name: 
    email: 
    url: 
  category: 
  type: feature
  description: "Optimize your development experience in Unity with the Dev Tools
    feature set. Enable support for multiple integrated development environments
    (IDE) for editing your Unity code. Get access to development tools to help you
    test and analyze your project\u2019s performance."
  errors: []
  versions:
    all:
    - 1.0.2
    compatible:
    - 1.0.2
    recommended: 1.0.2
    deprecated: []
  dependencies:
  - name: com.unity.ide.visualstudio
    version: default
  - name: com.unity.ide.rider
    version: default
  - name: com.unity.editorcoroutines
    version: default
  - name: com.unity.performance.profile-analyzer
    version: default
  - name: com.unity.test-framework
    version: default
  - name: com.unity.testtools.codecoverage
    version: default
  resolvedDependencies:
  - name: com.unity.ide.visualstudio
    version: 2.0.23
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ide.rider
    version: 3.0.36
  - name: com.unity.editorcoroutines
    version: 1.0.0
  - name: com.unity.performance.profile-analyzer
    version: 1.2.3
  - name: com.unity.testtools.codecoverage
    version: 1.2.6
  - name: com.unity.settings-manager
    version: 2.1.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"quickstart":"https://docs.unity3d.com/Documentation/Manual/DeveloperToolsFeature.html"}'
  assetStore:
    productId: 
  fingerprint: 767aadbc6eb72681a4ca807c8fa248e0230a0cef
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.2
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.formats.fbx@5.1.3
  testable: 0
  isDirectDependency: 1
  version: 5.1.3
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.formats.fbx@db39de05b0db
  assetPath: Packages/com.unity.formats.fbx
  name: com.unity.formats.fbx
  displayName: FBX Exporter
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: "The FBX Exporter package enables round-trip workflows between Unity
    and 3D modeling software. Send geometry, Lights, Cameras, and animation from
    Unity to Autodesk\xAE Maya\xAE, Autodesk\xAE Maya LT\u2122, or Autodesk\xAE 3ds
    Max\xAE, and back to Unity again, with minimal effort."
  errors: []
  versions:
    all:
    - 2.0.1-preview.2
    - 2.0.1-preview.3
    - 2.0.1-preview.4
    - 2.0.1-preview.5
    - 2.0.1-preview.11
    - 2.0.2-preview.1
    - 2.0.3-preview.3
    - 3.0.0-preview.2
    - 3.0.1-preview.2
    - 3.1.0-preview.1
    - 3.2.0-preview.2
    - 3.2.1-preview.2
    - 4.0.0-pre.1
    - 4.0.0-pre.4
    - 4.0.1
    - 4.1.0-pre.2
    - 4.1.0
    - 4.1.1
    - 4.1.2
    - 4.1.3
    - 4.2.0-pre.1
    - 4.2.0
    - 4.2.1
    - 5.0.0-pre.1
    - 5.0.0
    - 5.1.0-pre.1
    - 5.1.0
    - 5.1.1
    - 5.1.2
    - 5.1.3
    compatible:
    - 5.1.1
    - 5.1.2
    - 5.1.3
    recommended: 5.1.3
    deprecated: []
  dependencies:
  - name: com.autodesk.fbx
    version: 5.1.1
  - name: com.unity.timeline
    version: 1.7.1
  resolvedDependencies:
  - name: com.autodesk.fbx
    version: 5.1.1
  - name: com.unity.timeline
    version: 1.8.7
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.director
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.particlesystem
    version: 1.0.0
  keywords:
  - fbx
  - animation
  - modeling
  - maya
  - max
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638732425792020000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.formats.fbx@5.1/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.formats.fbx.git
    revision: 1dc43fa4b17f9e2d73204dbdd2463d060ddeb36c
    path: 
  unityLifecycle:
    version: 5.1.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n- Fixed an indentation issue in FBX Exporter
    Project Settings that was preventing straightforward toggling of checkboxes."}'
  assetStore:
    productId: 
  fingerprint: db39de05b0dbacefb3aa3035d97d497649e2e711
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 5.1.1
    minimumUnityVersion: 2020.3.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.multiplayer.center@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.multiplayer.center@f3fb577b3546
  assetPath: Packages/com.unity.multiplayer.center
  name: com.unity.multiplayer.center
  displayName: Multiplayer Center
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The multiplayer center provides a starting point to create multiplayer
    games. It will recommend specific packages and enable you to easily access integrations,
    samples and documentation.
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.uielements
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  keywords:
  - Multiplayer
  - Netcode
  - Services
  - Tools
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: f3fb577b3546594b97b8cc34307cd621f60f1c73
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 6000.0.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.probuilder@6.0.6
  testable: 0
  isDirectDependency: 1
  version: 6.0.6
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.probuilder@c19679f07bae
  assetPath: Packages/com.unity.probuilder
  name: com.unity.probuilder
  displayName: ProBuilder
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: 'Build, edit, and texture custom geometry in Unity. Use ProBuilder
    for in-scene level design, prototyping, collision meshes, all with on-the-fly
    play-testing.


    Advanced features include UV editing, vertex colors, parametric
    shapes, and texture blending. With ProBuilder''s model export feature it''s easy
    to tweak your levels in any external 3D modelling suite.'
  errors: []
  versions:
    all:
    - 3.0.0
    - 3.0.1
    - 3.0.2
    - 3.0.3
    - 3.0.6
    - 3.0.8
    - 3.0.9
    - 3.10.1
    - 4.0.2
    - 4.0.3
    - 4.0.4
    - 4.0.5
    - 4.1.0
    - 4.1.2
    - 4.2.1
    - 4.2.2-preview.1
    - 4.2.2-preview.2
    - 4.2.3
    - 4.2.4-preview.0
    - 4.3.0-preview.0
    - 4.3.0-preview.1
    - 4.3.0-preview.2
    - 4.3.0-preview.4
    - 4.3.0-preview.6
    - 4.3.0-preview.7
    - 4.3.0-preview.8
    - 4.3.0-preview.9
    - 4.3.1
    - 4.4.0-preview.1
    - 4.4.0
    - 4.5.0
    - 4.5.2
    - 5.0.0-pre.7
    - 5.0.0-pre.10
    - 5.0.1
    - 5.0.3
    - 5.0.4
    - 5.0.6
    - 5.0.7
    - 5.1.0
    - 5.1.1
    - 5.2.0
    - 5.2.2
    - 5.2.3
    - 5.2.4
    - 6.0.1-pre.1
    - 6.0.1-pre.2
    - 6.0.1
    - 6.0.2
    - 6.0.3
    - 6.0.4
    - 6.0.5
    - 6.0.6
    compatible:
    - 6.0.6
    recommended: 6.0.6
    deprecated: []
  dependencies:
  - name: com.unity.shadergraph
    version: 17.0.3
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.settings-manager
    version: 1.0.3
  resolvedDependencies:
  - name: com.unity.shadergraph
    version: 17.2.0
  - name: com.unity.render-pipelines.core
    version: 17.2.0
  - name: com.unity.burst
    version: 1.8.23
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.7
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.5
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  - name: com.unity.searcher
    version: 4.9.3
  - name: com.unity.settings-manager
    version: 2.1.0
  keywords:
  - 3d
  - model
  - mesh
  - modeling
  - geometry
  - shape
  - cube
  - blender
  - max
  - maya
  - fbx
  - obj
  - level
  - design
  - block
  - greybox
  - graybox
  - whitebox
  - prototype
  - probuilder
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638881986484920000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.probuilder@6.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.probuilder.git
    revision: 7a59962a86a9fc88227ccfe1191b8d756209f8c6
    path: 
  unityLifecycle:
    version: 6.0.6
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Changes\n\n- [PBLD-228] Updated function to access
    shader property types.\n\n### Fixed\n\n- [PBLD-224] Fixed rect selection in HDRP.\n-
    [PBLD-236] Fixed a bug where degenerate triangles were being added to output
    meshes, causing occasional rendering artifacts.\n- [PBLD-220] Fixed an error
    in the SoftDeleteEdges sample code that was preventing it from appearing in the
    context menu.\n- [PBLD-231] Fixed a bug where Extrude was not being disabled
    in the context menu when ''allow non-manifold actions'' was not selected in the
    ProBuilder preferences.\n- [PBLD-238] Fixed a bug that could cause users to lose
    any work that they did on a ProBuilder mesh between two usages of tool actions
    that had previews (options overlays).\n- [PBLD-222] Fixed crash by preventing
    user from probuilderizing a gameobject that has isPartOfStaticBatch set to true.\n-
    [PBLD-245] Fixed warnings about obsolete API usage when using Unity 6.2 and later.
    Updated the API usage where the alternatives were available in Unity 2022.3."}'
  assetStore:
    productId: 
  fingerprint: c19679f07bae845d35ba92f10a1bb2eb889fb607
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 6.0.6
    minimumUnityVersion: 6000.0.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.progrids@3.0.3-preview.6
  testable: 0
  isDirectDependency: 1
  version: 3.0.3-preview.6
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.progrids@36b0033bf980
  assetPath: Packages/com.unity.progrids
  name: com.unity.progrids
  displayName: ProGrids
  author:
    name: 
    email: 
    url: 
  category: 
  type: asset
  description: 'Advanced grid and object snapping for the scene view. ProGrids is
    a dynamically placed grid, available on all 3 axes and at any position in the
    scene.


    ProGrids snaps objects in world space, ensuring consistent placement
    of objects in your level.


    In addition, ProGrids is completely customizable.
    Change the colors of your grid, the snap sizing, unit of measurement, and more!'
  errors: []
  versions:
    all:
    - 3.0.1-preview
    - 3.0.2-preview
    - 3.0.3-preview.0
    - 3.0.3-preview.4
    - 3.0.3-preview.5
    - 3.0.3-preview.6
    compatible:
    - 3.0.1-preview
    - 3.0.2-preview
    - 3.0.3-preview.0
    - 3.0.3-preview.4
    - 3.0.3-preview.5
    - 3.0.3-preview.6
    recommended: 
    deprecated: []
  dependencies:
  - name: com.unity.settings-manager
    version: 1.0.2
  resolvedDependencies:
  - name: com.unity.settings-manager
    version: 2.1.0
  keywords:
  - grid
  - snap
  - manipulation
  - array
  - align
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637209307060000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: git+https://github.com/Unity-Technologies/com.unity.progrids.git
    revision: 381f2b0b5dda11525d53e2c608284a3f68d1beff
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 0
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 36b0033bf980d263ff6d1d156245d91e6e92f6f9
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 3.0.1-preview
    minimumUnityVersion: 2018.1.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.project-auditor@1.0.1
  testable: 0
  isDirectDependency: 1
  version: 1.0.1
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.project-auditor@94c6e4e98816
  assetPath: Packages/com.unity.project-auditor
  name: com.unity.project-auditor
  displayName: Project Auditor
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: 'Project Auditor is a static analysis tool that analyzes assets, settings,
    and scripts of the Unity project and produces a report containing: Code and Settings
    Diagnostics, the last BuildReport, and assets information.'
  errors: []
  versions:
    all:
    - 0.10.0
    - 1.0.0-pre.1
    - 1.0.0-pre.2
    - 1.0.0-pre.3
    - 1.0.0-pre.4
    - 1.0.0-pre.5
    - 1.0.0-pre.6
    - 1.0.0-pre.7
    - 1.0.0
    - 1.0.1
    compatible:
    - 1.0.1
    recommended: 1.0.1
    deprecated: []
  dependencies:
  - name: com.unity.nuget.mono-cecil
    version: 1.10.1
  - name: com.unity.nuget.newtonsoft-json
    version: 3.2.1
  resolvedDependencies:
  - name: com.unity.nuget.mono-cecil
    version: 1.11.5
  - name: com.unity.nuget.newtonsoft-json
    version: 3.2.1
  keywords:
  - performance
  - analysis
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638773883996110000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.project-auditor@1.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/ProjectAuditor.git
    revision: 7b3e602b8da35bf1c15ed936c802cd1c91a0e007
    path: 
  unityLifecycle:
    version: 1.0.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Changed\n* Don''t suggest switching Physics 2D
    update mode to Script for now.\n\n### Fixed\n* Fixed an issue where Project Auditor
    exhibited compilation issues on certain versions of the Editor.\n* Fixed link
    to help pages.\n* Fix for PROFB-273; Change summary screen message when an area
    is not analyzed.\n* Fix for PROFB-274; Added dividers between the foldouts on
    the shader variant page.\n* Fix for PROFB-277; change text for \"1 ignored are
    hidden\".\n* Fix for PROFB-278; 1 items changed to 1 item(s) in the table.\n*
    Fix for PROFB-279; Build steps all show as info until you hit refresh.\n* Fix
    for PROFB-280; Removed Path option from the Build Size context menu.\n* Fix for
    PROFB-281; Unable to sort columns on Build - Build Size screens.\n* Fix for PROFB-284;
    change references to Texture Streaming when it''s now called Mipmap Streaming.\n*
    Fix for PROFB-285; fix for not being able to hide info compiler messages in the
    compiler messages view.\n* Fix for PROFB-286; fix performance issues when selecting
    lots of table items.\n* Fix for PROFB-287; improve documentation for installation
    instructions.\n* Fix for PROFB-289; exclude shaders that are Hidden from asset
    issues.\n* Fix for PROFB-290; fix settings links for Physics/Physics 2D.\n* Fix
    for PROFB-291; issue where rendering path recommendations would show for the
    wrong pipeline.\n* Fix for PROFB-300; file extensions would sometimes throw because
    a file didn''t have one.\n* Fix for PROFB-310; fix for Analyzing assets causing
    shader issues to vanish.\n* Fix for PROFB-314; speculative attempt to fix out
    of memory crash reported on Discussions."}'
  assetStore:
    productId: 
  fingerprint: 94c6e4e9881619d6fd9744d93bd32bec6a2d676c
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.1
    minimumUnityVersion: 2021.3.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.recorder@5.1.2
  testable: 0
  isDirectDependency: 1
  version: 5.1.2
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.recorder@979a3db2a781
  assetPath: Packages/com.unity.recorder
  name: com.unity.recorder
  displayName: Recorder
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: 'The Recorder allows you to capture and save data from the Unity Editor
    during Play mode: animations, videos, images, audio, AOVs, and more.'
  errors: []
  versions:
    all:
    - 2.0.0-preview.6
    - 2.0.1-preview.1
    - 2.0.2-preview.3
    - 2.0.3-preview.1
    - 2.1.0-preview.1
    - 2.2.0-preview.4
    - 2.3.0-preview.3
    - 2.4.0-preview.1
    - 2.5.0-pre.1
    - 2.5.2
    - 2.5.4
    - 2.5.5
    - 2.5.7
    - 2.6.0-exp.3
    - 2.6.0-exp.4
    - 3.0.0-pre.1
    - 3.0.0-pre.2
    - 3.0.0
    - 3.0.1
    - 3.0.2
    - 3.0.3
    - 3.0.4
    - 4.0.0-pre.1
    - 4.0.0-pre.2
    - 4.0.0-pre.3
    - 4.0.0-pre.4
    - 4.0.0-pre.5
    - 4.0.0
    - 4.0.1
    - 4.0.2
    - 4.0.3
    - 5.0.0-pre.1
    - 5.0.0
    - 5.1.0-pre.1
    - 5.1.0
    - 5.1.1
    - 5.1.2
    compatible:
    - 5.1.2
    recommended: 5.1.2
    deprecated: []
  dependencies:
  - name: com.unity.timeline
    version: 1.8.7
  - name: com.unity.collections
    version: 1.2.4
  - name: com.unity.bindings.openimageio
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.timeline
    version: 1.8.7
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.director
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.particlesystem
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.7
  - name: com.unity.burst
    version: 1.8.23
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.nuget.mono-cecil
    version: 1.11.5
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.bindings.openimageio
    version: 1.0.0
  keywords:
  - unity
  - recorder
  - recording
  - capture
  - video
  - animation
  - clip
  - images
  - render
  - pass
  - layer
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638695210224500000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.recorder@5.1/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.recorder.git
    revision: 0b47302778b3404515124864b0edfe33ff4ec88e
    path: 
  unityLifecycle:
    version: 5.1.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Changed\n- Users can now enable alpha capture from
    the UI in projects that use URP."}'
  assetStore:
    productId: 
  fingerprint: 979a3db2a7818d9c4852c886631b01e394d80af0
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 5.1.2
    minimumUnityVersion: 2023.1.1f1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.render-pipelines.high-definition@17.2.0
  testable: 0
  isDirectDependency: 1
  version: 17.2.0
  source: 2
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.render-pipelines.high-definition@28765669b6fe
  assetPath: Packages/com.unity.render-pipelines.high-definition
  name: com.unity.render-pipelines.high-definition
  displayName: High Definition Render Pipeline
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The High Definition Render Pipeline (HDRP) is a high-fidelity Scriptable
    Render Pipeline built by Unity to target modern (Compute Shader compatible) platforms.
    HDRP utilizes Physically-Based Lighting techniques, linear lighting, HDR lighting,
    and a configurable hybrid Tile/Cluster deferred/Forward lighting architecture
    and gives you the tools you need to create games, technical demos, animations,
    and more to a high graphical standard.
  errors: []
  versions:
    all:
    - 17.2.0
    compatible:
    - 17.2.0
    recommended: 17.2.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.video
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  - name: com.unity.render-pipelines.core
    version: 17.2.0
  - name: com.unity.shadergraph
    version: 17.2.0
  - name: com.unity.visualeffectgraph
    version: 17.2.0
  - name: com.unity.render-pipelines.high-definition-config
    version: 17.2.0
  resolvedDependencies:
  - name: com.unity.modules.video
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  - name: com.unity.render-pipelines.core
    version: 17.2.0
  - name: com.unity.burst
    version: 1.8.23
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.7
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.5
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  - name: com.unity.shadergraph
    version: 17.2.0
  - name: com.unity.searcher
    version: 4.9.3
  - name: com.unity.visualeffectgraph
    version: 17.2.0
  - name: com.unity.render-pipelines.high-definition-config
    version: 17.2.0
  keywords:
  - graphics
  - performance
  - rendering
  - render
  - pipeline
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 17.2.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 28765669b6fee79d765751f370129f832b2c30c4
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 17.2.0
    minimumUnityVersion: 6000.2.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.timeline@1.8.7
  testable: 0
  isDirectDependency: 1
  version: 1.8.7
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.timeline@c58b4ee65782
  assetPath: Packages/com.unity.timeline
  name: com.unity.timeline
  displayName: Timeline
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: Use Unity Timeline to create cinematic content, game-play sequences,
    audio sequences, and complex particle effects.
  errors: []
  versions:
    all:
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.2.4
    - 1.2.5
    - 1.2.6
    - 1.2.7
    - 1.2.9
    - 1.2.10
    - 1.2.11
    - 1.2.12
    - 1.2.13
    - 1.2.14
    - 1.2.15
    - 1.2.16
    - 1.2.17
    - 1.2.18
    - 1.3.0-preview.2
    - 1.3.0-preview.3
    - 1.3.0-preview.5
    - 1.3.0-preview.6
    - 1.3.0-preview.7
    - 1.3.0
    - 1.3.1
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.6
    - 1.3.7
    - 1.4.0-preview.1
    - 1.4.0-preview.2
    - 1.4.0-preview.3
    - 1.4.0-preview.5
    - 1.4.0-preview.6
    - 1.4.0-preview.7
    - 1.4.0
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4
    - 1.4.5
    - 1.4.6
    - 1.4.7
    - 1.4.8
    - 1.5.0-pre.2
    - 1.5.0-preview.1
    - 1.5.0-preview.2
    - 1.5.0-preview.3
    - 1.5.0-preview.4
    - 1.5.0-preview.5
    - 1.5.1-pre.1
    - 1.5.1-pre.2
    - 1.5.1-pre.3
    - 1.5.2
    - 1.5.4
    - 1.5.5
    - 1.5.6
    - 1.5.7
    - 1.6.0-pre.1
    - 1.6.0-pre.3
    - 1.6.0-pre.4
    - 1.6.0-pre.5
    - 1.6.1
    - 1.6.2
    - 1.6.3
    - 1.6.4
    - 1.6.5
    - 1.7.0-pre.1
    - 1.7.0-pre.2
    - 1.7.0
    - 1.7.1
    - 1.7.2
    - 1.7.3
    - 1.7.4
    - 1.7.5
    - 1.7.6
    - 1.7.7
    - 1.8.0
    - 1.8.1
    - 1.8.2
    - 1.8.3
    - 1.8.4
    - 1.8.5
    - 1.8.6
    - 1.8.7
    - 1.8.8
    - 1.8.9
    compatible:
    - 1.8.7
    - 1.8.8
    - 1.8.9
    recommended: 1.8.9
    deprecated: []
  dependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.director
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.particlesystem
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.director
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.particlesystem
    version: 1.0.0
  keywords:
  - unity
  - animation
  - editor
  - timeline
  - tools
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638524833900000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.timeline@1.8/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.timeline.git
    revision: d6432ca638481c3d0e4c01f87f9cdbe3c4b1d529
    path: 
  unityLifecycle:
    version: 1.8.7
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Added\n\n- Released ronl-workflow-custom-marker.md
    Added a new workflow to the Timeline Workflows documentation:\n- Released ronl-workflow-custom-marker.md
    The `Create a custom Notes marker` workflow demonstrates how to create a custom
    marker for adding notes to Timeline instances. This workflow also demonstrates
    how to change the default appearance of a custom marker with scripting and a
    Unity Style Sheet (USS).\n\n### Fixed\n\n- Fixed bug where using , and . (<>)
    to step frames in the Animation Window while the Timeline Window was linked would
    sometimes not work. [IN-56667](https://unity3d.atlassian.net/servicedesk/customer/portal/2/IN-56667)\n-
    When the Timeline and Animation windows are linked and the Timeline Window is
    active, moving the playhead in the Timeline Window will cause the animation window
    to repaint immediately."}'
  assetStore:
    productId: 
  fingerprint: c58b4ee65782ad38338e29f7ee67787cb6998f04
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.8.7
    minimumUnityVersion: 2019.3.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.ugui@2.0.0
  testable: 0
  isDirectDependency: 1
  version: 2.0.0
  source: 2
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.ugui@423bc642aff1
  assetPath: Packages/com.unity.ugui
  name: com.unity.ugui
  displayName: Unity UI
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: "Unity UI is a set of tools for developing user interfaces for games
    and applications. It is a GameObject-based UI system that uses Components and
    the Game View to arrange, position, and style user interfaces. \u200B You cannot
    use Unity UI to create or change user interfaces in the Unity Editor."
  errors: []
  versions:
    all:
    - 2.0.0
    compatible:
    - 2.0.0
    recommended: 2.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  keywords:
  - UI
  - ugui
  - Unity UI
  - Canvas
  - TextMeshPro
  - TextMesh Pro
  - Text
  - TMP
  - SDF
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 2.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 423bc642aff1ba4fed4feef1d7b45461138cbb32
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.0
    minimumUnityVersion: 2019.2.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.visualscripting@1.9.7
  testable: 0
  isDirectDependency: 1
  version: 1.9.7
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.visualscripting@6279e2b7c485
  assetPath: Packages/com.unity.visualscripting
  name: com.unity.visualscripting
  displayName: Visual Scripting
  author:
    name: 
    email: 
    url: 
  category: 
  type: tool
  description: 'Visual scripting is a workflow that uses visual, node-based graphs
    to design behaviors rather than write lines of C# script.


    Enabling artists,
    designers and programmers alike, visual scripting can be used to design final
    logic, quickly create prototypes, iterate on gameplay and create custom nodes
    to help streamline collaboration.


    Visual scripting is compatible with third-party
    APIs, including most packages, assets and custom libraries.'
  errors: []
  versions:
    all:
    - 1.5.0
    - 1.5.1-pre.3
    - 1.5.1-pre.5
    - 1.5.1
    - 1.5.2
    - 1.6.0-pre.3
    - 1.6.0
    - 1.6.1
    - 1.7.1
    - 1.7.2
    - 1.7.3
    - 1.7.5
    - 1.7.6
    - 1.7.7
    - 1.7.8
    - 1.8.0-pre.1
    - 1.8.0
    - 1.9.0
    - 1.9.1
    - 1.9.2
    - 1.9.4
    - 1.9.5
    - 1.9.6
    - 1.9.7
    - 1.9.8
    compatible:
    - 1.9.7
    - 1.9.8
    recommended: 1.9.8
    deprecated:
    - 1.8.0-pre.1
  dependencies:
  - name: com.unity.ugui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638835226812630000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.visualscripting.git
    revision: 8292335505915a41ec7c5cc8e08ac436e24120a9
    path: 
  unityLifecycle:
    version: 1.9.7
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n- Fixed a warning \"Unable to load Unity.Android.Gradle''s
    referenced assembly NiceIO\" when scanning assemblies. [UVSB-2594](https://issuetracker.unity3d.com/product/unity/issues/guid/UVSB-2594)\n-
    Fixed error when trying to load fuzzy finder on multi screen setup on Mac. [UVSB-2419](https://issuetracker.unity3d.com/product/unity/issues/guid/UVSB-2419)\n-
    Fixed the `AOTSafeMode` project setting appearing in the Editor Preferences window.
    It is now shown in the Project Settings tab for Visual Scripting. [UVSB-2590](https://issuetracker.unity3d.com/product/unity/issues/guid/UVSB-2590)\n-
    Fixed possible crash on VisionOS. [UVSB-2565](https://issuetracker.unity3d.com/product/unity/issues/guid/UVSB-2565)\n\n###
    Changed\n- The `AOTSafeMode` project setting has been marked as not visible,
    it will no longer be included when calling `ConfigurationPanel.GetSearchKeywords`.
    [UVSB-2590](https://issuetracker.unity3d.com/product/unity/issues/guid/UVSB-2590)"}'
  assetStore:
    productId: 
  fingerprint: 6279e2b7c4858e56cca7f367cd38c49ef66778c9
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.9.7
    minimumUnityVersion: 2021.3.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.accessibility@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.accessibility
  assetPath: Packages/com.unity.modules.accessibility
  name: com.unity.modules.accessibility
  displayName: Accessibility
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Accessibility module includes utilities to facilitate the development
    of accessible user experiences in Unity. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AccessibilityModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.ai@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.ai
  assetPath: Packages/com.unity.modules.ai
  name: com.unity.modules.ai
  displayName: AI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The AI module implements the path finding features in Unity. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.AIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.androidjni@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.androidjni
  assetPath: Packages/com.unity.modules.androidjni
  name: com.unity.modules.androidjni
  displayName: Android JNI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'AndroidJNI module allows you to call Java code. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AndroidJNIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.animation@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.animation
  assetPath: Packages/com.unity.modules.animation
  name: com.unity.modules.animation
  displayName: Animation
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Animation module implements Unity''s animation system. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.AnimationModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.assetbundle@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.assetbundle
  assetPath: Packages/com.unity.modules.assetbundle
  name: com.unity.modules.assetbundle
  displayName: Asset Bundle
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The AssetBundle module implements the AssetBundle class and related
    APIs to load data from AssetBundles. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AssetBundleModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.audio@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.audio
  assetPath: Packages/com.unity.modules.audio
  name: com.unity.modules.audio
  displayName: Audio
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Audio module implements Unity''s audio system. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.AudioModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.cloth@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.cloth
  assetPath: Packages/com.unity.modules.cloth
  name: com.unity.modules.cloth
  displayName: Cloth
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Cloth module implements cloth physics simulation through the
    Cloth component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ClothModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.director@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.director
  assetPath: Packages/com.unity.modules.director
  name: com.unity.modules.director
  displayName: Director
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Director module implements the PlayableDirector class. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.DirectorModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.imageconversion@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.imageconversion
  assetPath: Packages/com.unity.modules.imageconversion
  name: com.unity.modules.imageconversion
  displayName: Image Conversion
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ImageConversion module implements the ImageConversion class which
    provides helper methods for converting image data. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ImageConversionModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.imgui@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.imgui
  assetPath: Packages/com.unity.modules.imgui
  name: com.unity.modules.imgui
  displayName: IMGUI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The IMGUI module provides Unity''s immediate mode GUI solution for
    creating in-game and editor user interfaces. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.IMGUIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.jsonserialize@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.jsonserialize
  assetPath: Packages/com.unity.modules.jsonserialize
  name: com.unity.modules.jsonserialize
  displayName: JSONSerialize
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The JSONSerialize module provides the JsonUtility class which lets
    you serialize Unity Objects to JSON format. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.JSONSerializeModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.particlesystem@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.particlesystem
  assetPath: Packages/com.unity.modules.particlesystem
  name: com.unity.modules.particlesystem
  displayName: Particle System
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ParticleSystem module implements Unity''s Particle System. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.ParticleSystemModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.physics@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.physics
  assetPath: Packages/com.unity.modules.physics
  name: com.unity.modules.physics
  displayName: Physics
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Physics module implements 3D physics in Unity. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.PhysicsModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.physics2d@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.physics2d
  assetPath: Packages/com.unity.modules.physics2d
  name: com.unity.modules.physics2d
  displayName: Physics 2D
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Physics2d module implements 2D physics in Unity. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.Physics2DModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.screencapture@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.screencapture
  assetPath: Packages/com.unity.modules.screencapture
  name: com.unity.modules.screencapture
  displayName: Screen Capture
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ScreenCapture module provides functionality to take screen shots
    using the ScreenCapture class. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ScreenCaptureModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.terrain@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.terrain
  assetPath: Packages/com.unity.modules.terrain
  name: com.unity.modules.terrain
  displayName: Terrain
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Terrain module implements Unity''s Terrain rendering engine available
    through the Terrain component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TerrainModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.terrainphysics@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.terrainphysics
  assetPath: Packages/com.unity.modules.terrainphysics
  name: com.unity.modules.terrainphysics
  displayName: Terrain Physics
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The TerrainPhysics module connects the Terrain and Physics modules
    by implementing the TerrainCollider component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TerrainPhysicsModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.tilemap@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.tilemap
  assetPath: Packages/com.unity.modules.tilemap
  name: com.unity.modules.tilemap
  displayName: Tilemap
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Tilemap module implements the Tilemap class. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TilemapModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics2d
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics2d
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.ui@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.ui
  assetPath: Packages/com.unity.modules.ui
  name: com.unity.modules.ui
  displayName: UI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UI module implements basic components required for Unity''s UI
    system Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.uielements@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.uielements
  assetPath: Packages/com.unity.modules.uielements
  name: com.unity.modules.uielements
  displayName: UIElements
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UIElements module implements the UIElements retained mode UI
    framework. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UIElementsModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.umbra@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.umbra
  assetPath: Packages/com.unity.modules.umbra
  name: com.unity.modules.umbra
  displayName: Umbra
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Umbra module implements Unity''s occlusion culling system. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.UmbraModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.unityanalytics@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unityanalytics
  assetPath: Packages/com.unity.modules.unityanalytics
  name: com.unity.modules.unityanalytics
  displayName: Unity Analytics
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.unitywebrequest@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequest
  assetPath: Packages/com.unity.modules.unitywebrequest
  name: com.unity.modules.unitywebrequest
  displayName: Unity Web Request
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequest module lets you communicate with http services.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.unitywebrequestassetbundle@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestassetbundle
  assetPath: Packages/com.unity.modules.unitywebrequestassetbundle
  name: com.unity.modules.unitywebrequestassetbundle
  displayName: Unity Web Request Asset Bundle
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestAssetBundle module provides the DownloadHandlerAssetBundle
    class to use UnityWebRequest to download Asset Bundles. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestAssetBundleModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.unitywebrequestaudio@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestaudio
  assetPath: Packages/com.unity.modules.unitywebrequestaudio
  name: com.unity.modules.unitywebrequestaudio
  displayName: Unity Web Request Audio
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestAudio module provides the DownloadHandlerAudioClip
    class to use UnityWebRequest to download AudioClips. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestAudioModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.unitywebrequesttexture@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequesttexture
  assetPath: Packages/com.unity.modules.unitywebrequesttexture
  name: com.unity.modules.unitywebrequesttexture
  displayName: Unity Web Request Texture
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestTexture module provides the DownloadHandlerTexture
    class to use UnityWebRequest to download Textures. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestTextureModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.unitywebrequestwww@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestwww
  assetPath: Packages/com.unity.modules.unitywebrequestwww
  name: com.unity.modules.unitywebrequestwww
  displayName: Unity Web Request WWW
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestWWW module implements the legacy WWW lets you
    communicate with http services. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestWWWModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestassetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestaudio
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestassetbundle
    version: 1.0.0
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestaudio
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.vehicles@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.vehicles
  assetPath: Packages/com.unity.modules.vehicles
  name: com.unity.modules.vehicles
  displayName: Vehicles
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Vehicles module implements vehicle physics simulation through
    the WheelCollider component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VehiclesModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.video@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.video
  assetPath: Packages/com.unity.modules.video
  name: com.unity.modules.video
  displayName: Video
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Video module lets you play back video files in your content.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VideoModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.vr@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.vr
  assetPath: Packages/com.unity.modules.vr
  name: com.unity.modules.vr
  displayName: VR
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The VR module implements support for virtual reality devices in Unity.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VRModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.xr
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.xr
    version: 1.0.0
  - name: com.unity.modules.subsystems
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.wind@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.wind
  assetPath: Packages/com.unity.modules.wind
  name: com.unity.modules.wind
  displayName: Wind
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Wind module implements the WindZone component which can affect
    terrain rendering and particle simulations. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.WindModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.xr@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.xr
  assetPath: Packages/com.unity.modules.xr
  name: com.unity.modules.xr
  displayName: XR
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The XR module contains the VR and AR related platform support functionality.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.XRModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.subsystems
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.subsystems
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.subsystems@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.subsystems
  assetPath: Packages/com.unity.modules.subsystems
  name: com.unity.modules.subsystems
  displayName: Subsystems
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Subsystem module contains the definitions and runtime support
    for general subsystems in Unity. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.SubsystemsModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 0
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.hierarchycore@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 2
  resolvedPath: C:\Unity\Editors\6000.2.0f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.hierarchycore
  assetPath: Packages/com.unity.modules.hierarchycore
  name: com.unity.modules.hierarchycore
  displayName: Hierarchy Core
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'Module that contains a high-performance hierarchy container. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.HierarchyCoreModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 0
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.render-pipelines.core@17.2.0
  testable: 0
  isDirectDependency: 0
  version: 17.2.0
  source: 2
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.render-pipelines.core@bd0e8186c2bc
  assetPath: Packages/com.unity.render-pipelines.core
  name: com.unity.render-pipelines.core
  displayName: Scriptable Render Pipeline Core
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: SRP Core makes it easier to create or customize a Scriptable Render
    Pipeline (SRP). SRP Core contains reusable code, including boilerplate code for
    working with platform-specific graphics APIs, utility functions for common rendering
    operations, and  shader libraries. The code in SRP Core is use by the High Definition
    Render Pipeline (HDRP) and Universal Render Pipeline (URP). If you are creating
    a custom SRP from scratch or customizing a prebuilt SRP, using SRP Core will
    save you time.
  errors: []
  versions:
    all:
    - 17.2.0
    compatible:
    - 17.2.0
    recommended: 17.2.0
    deprecated: []
  dependencies:
  - name: com.unity.burst
    version: 1.8.14
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.collections
    version: 2.4.3
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  resolvedDependencies:
  - name: com.unity.burst
    version: 1.8.23
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.7
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.5
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 17.2.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: bd0e8186c2bcfea109ee5981b813a1b39a6382fb
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 17.2.0
    minimumUnityVersion: 6000.2.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.shadergraph@17.2.0
  testable: 0
  isDirectDependency: 0
  version: 17.2.0
  source: 2
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.shadergraph@f8b69e83dfdd
  assetPath: Packages/com.unity.shadergraph
  name: com.unity.shadergraph
  displayName: Shader Graph
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The Shader Graph package adds a visual Shader editing tool to Unity.
    You can use this tool to create Shaders in a visual way instead of writing code.
    Specific render pipelines can implement specific graph features. Currently, both
    the High Definition Rendering Pipeline and the Universal Rendering Pipeline support
    Shader Graph.
  errors: []
  versions:
    all:
    - 17.2.0
    compatible:
    - 17.2.0
    recommended: 17.2.0
    deprecated: []
  dependencies:
  - name: com.unity.render-pipelines.core
    version: 17.2.0
  - name: com.unity.searcher
    version: 4.9.3
  resolvedDependencies:
  - name: com.unity.render-pipelines.core
    version: 17.2.0
  - name: com.unity.burst
    version: 1.8.23
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.7
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.5
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  - name: com.unity.searcher
    version: 4.9.3
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 17.2.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: f8b69e83dfddc9d7bbdcb1b4c60f079ffee88676
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 17.2.0
    minimumUnityVersion: 6000.2.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.visualeffectgraph@17.2.0
  testable: 0
  isDirectDependency: 0
  version: 17.2.0
  source: 2
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.visualeffectgraph@c8dcb84572f2
  assetPath: Packages/com.unity.visualeffectgraph
  name: com.unity.visualeffectgraph
  displayName: Visual Effect Graph
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The Visual Effect Graph is a node based visual effect editor. It allows
    you to author next generation visual effects that Unity simulates directly on
    the GPU. The Visual Effect Graph is production-ready for the High Definition
    Render Pipeline and runs on all platforms supported by it. Full support for the
    Universal Render Pipeline and compatible mobile devices is still in development.
  errors: []
  versions:
    all:
    - 17.2.0
    compatible:
    - 17.2.0
    recommended: 17.2.0
    deprecated: []
  dependencies:
  - name: com.unity.shadergraph
    version: 17.2.0
  - name: com.unity.render-pipelines.core
    version: 17.2.0
  resolvedDependencies:
  - name: com.unity.shadergraph
    version: 17.2.0
  - name: com.unity.render-pipelines.core
    version: 17.2.0
  - name: com.unity.burst
    version: 1.8.23
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.7
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.5
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  - name: com.unity.searcher
    version: 4.9.3
  keywords:
  - vfx
  - visualeffect
  - graph
  - effect
  - particles
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 17.2.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: c8dcb84572f2d0cfeef21465bb35296970fa0aa9
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 17.2.0
    minimumUnityVersion: 6000.2.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.render-pipelines.high-definition-config@17.2.0
  testable: 0
  isDirectDependency: 0
  version: 17.2.0
  source: 2
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.render-pipelines.high-definition-config@f7c893e8c254
  assetPath: Packages/com.unity.render-pipelines.high-definition-config
  name: com.unity.render-pipelines.high-definition-config
  displayName: High Definition Render Pipeline Config
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Configuration files for the High Definition Render Pipeline.
  errors: []
  versions:
    all:
    - 17.2.0
    compatible:
    - 17.2.0
    recommended: 17.2.0
    deprecated: []
  dependencies:
  - name: com.unity.render-pipelines.core
    version: 17.2.0
  resolvedDependencies:
  - name: com.unity.render-pipelines.core
    version: 17.2.0
  - name: com.unity.burst
    version: 1.8.23
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.7
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.5
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 17.2.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 0
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: f7c893e8c254a6aa8b789a59d79275671ed7d3b9
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 17.2.0
    minimumUnityVersion: 6000.2.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.collections@2.5.7
  testable: 0
  isDirectDependency: 0
  version: 2.5.7
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.collections@d49facba0036
  assetPath: Packages/com.unity.collections
  name: com.unity.collections
  displayName: Collections
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: A C# collections library providing data structures that can be used
    in jobs, and optimized by Burst compiler.
  errors: []
  versions:
    all:
    - 0.0.9-preview.1
    - 0.0.9-preview.2
    - 0.0.9-preview.3
    - 0.0.9-preview.4
    - 0.0.9-preview.5
    - 0.0.9-preview.6
    - 0.0.9-preview.7
    - 0.0.9-preview.8
    - 0.0.9-preview.9
    - 0.0.9-preview.10
    - 0.0.9-preview.11
    - 0.0.9-preview.12
    - 0.0.9-preview.13
    - 0.0.9-preview.14
    - 0.0.9-preview.15
    - 0.0.9-preview.16
    - 0.0.9-preview.17
    - 0.0.9-preview.18
    - 0.0.9-preview.19
    - 0.0.9-preview.20
    - 0.1.0-preview
    - 0.1.1-preview
    - 0.2.0-preview.13
    - 0.3.0-preview.0
    - 0.4.0-preview.6
    - 0.5.0-preview.9
    - 0.5.1-preview.11
    - 0.5.2-preview.8
    - 0.6.0-preview.9
    - 0.7.0-preview.2
    - 0.7.1-preview.3
    - 0.8.0-preview.5
    - 0.9.0-preview.5
    - 0.9.0-preview.6
    - 0.11.0-preview.17
    - 0.12.0-preview.13
    - 0.14.0-preview.16
    - 0.15.0-preview.21
    - 0.17.0-preview.18
    - 1.0.0-pre.3
    - 1.0.0-pre.5
    - 1.0.0-pre.6
    - 1.1.0
    - 1.2.3-pre.1
    - 1.2.3
    - 1.2.4
    - 1.3.1
    - 1.4.0
    - 1.5.1
    - 1.5.2
    - 2.1.0-exp.4
    - 2.1.0-pre.2
    - 2.1.0-pre.6
    - 2.1.0-pre.11
    - 2.1.0-pre.18
    - 2.1.1
    - 2.1.4
    - 2.2.0
    - 2.2.1
    - 2.3.0-exp.1
    - 2.3.0-pre.3
    - 2.4.0-exp.2
    - 2.4.0-pre.2
    - 2.4.0-pre.5
    - 2.4.0
    - 2.4.1
    - 2.4.2
    - 2.4.3
    - 2.5.0-exp.1
    - 2.5.0-pre.2
    - 2.5.1
    - 2.5.2
    - 2.5.3
    - 2.5.7
    - 2.6.0-exp.2
    - 2.6.0-pre.3
    - 2.6.0-pre.4
    compatible:
    - 2.5.7
    - 2.6.0-exp.2
    - 2.6.0-pre.3
    - 2.6.0-pre.4
    recommended: 2.5.7
    deprecated: []
  dependencies:
  - name: com.unity.burst
    version: 1.8.19
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.test-framework
    version: 1.4.6
  - name: com.unity.nuget.mono-cecil
    version: 1.11.5
  - name: com.unity.test-framework.performance
    version: 3.0.3
  resolvedDependencies:
  - name: com.unity.burst
    version: 1.8.23
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.nuget.mono-cecil
    version: 1.11.5
  - name: com.unity.test-framework.performance
    version: 3.1.0
  keywords:
  - dots
  - collections
  - unity
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638803020568560000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.collections@2.5/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/dots.git
    revision: 2520fc806449bf4c278c9031eb21f0a6aeccda46
    path: 
  unityLifecycle:
    version: 2.5.7
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Changed\n\n* Updated the `com.unity.entities` dependency
    to version `1.3.14`\n* Updated the `com.unity.burst` dependency to version `1.8.19`\n*
    Updated the `com.unity.nuget.mono-cecil` dependency to version `1.11.5`\n* Updated
    the `com.unity.test-framework dependency` to version `1.4.6`\n* The minimum supported
    editor version is now 2022.3.20f1\n\n### Fixed\n\n* UnsafeQueue memory leak due
    to OnDomainUnload callback being discarded by Burst."}'
  assetStore:
    productId: 
  fingerprint: d49facba0036aa0e1508296262f9d93b44d1ab3b
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.5.7
    minimumUnityVersion: 2022.3.20f1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.bindings.openimageio@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.bindings.openimageio@3229d2aa5c76
  assetPath: Packages/com.unity.bindings.openimageio
  name: com.unity.bindings.openimageio
  displayName: OpenImageIO Bindings
  author:
    name: 
    email: 
    url: 
  category: 
  type: library
  description: This package contains C# bindings for the OpenImageIO library. OpenImageIO
    is a image manipulation library that provides an unified way to work with multiple
    image formats.
  errors: []
  versions:
    all:
    - 1.0.0-pre.1
    - 1.0.0
    - 1.1.0-exp.1
    compatible:
    - 1.0.0
    - 1.1.0-exp.1
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.collections
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.collections
    version: 2.5.7
  - name: com.unity.burst
    version: 1.8.23
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.nuget.mono-cecil
    version: 1.11.5
  - name: com.unity.test-framework.performance
    version: 3.1.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638465519290000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.recorder.git
    revision: ea9b18e6c1a9d42406064f8e726352d7c26b656d
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 0
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Added\n- Added support for Windows Arm64."}'
  assetStore:
    productId: 
  fingerprint: 3229d2aa5c76c70269dc58c79d5864ec84fa4a74
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 2023.1.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.nuget.mono-cecil@1.11.5
  testable: 0
  isDirectDependency: 0
  version: 1.11.5
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.nuget.mono-cecil@d78732e851eb
  assetPath: Packages/com.unity.nuget.mono-cecil
  name: com.unity.nuget.mono-cecil
  displayName: Mono Cecil
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: 'The mono cecil library from https://www.nuget.org/packages/Mono.Cecil/


    This
    package is intended for internal Unity use only. Most Unity users will be better
    suited using the existing community tooling.

    To avoid assembly clashes,
    please use this package if you intend to use Mono.Cecil.'
  errors: []
  versions:
    all:
    - 0.1.6-preview.2
    - 1.0.0-preview.1
    - 1.10.0-preview.1
    - 1.10.1-preview.1
    - 1.10.1
    - 1.10.2
    - 1.11.4
    - 1.11.5
    compatible:
    - 1.11.5
    recommended: 1.11.5
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638729992509040000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.nuget.mono-cecil@1.11/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.nuget.mono-cecil.git
    revision: ab40507b5d82f57a4f34b6d0eacca1d6b170341d
    path: 
  unityLifecycle:
    version: 1.11.5
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 0
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"* This is a verified release of the com.unity.nuget.mono-cecil
    with embedded dlls of mono-cecil\n* Updated Cecil to 0.11.5 with a grafted patch
    from this bug fix PR (https://github.com/jbevain/cecil/pull/914)"}'
  assetStore:
    productId: 
  fingerprint: d78732e851eb42273c8e4d97993d3a94b207e570
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.11.5
    minimumUnityVersion: 2019.4.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.nuget.newtonsoft-json@3.2.1
  testable: 0
  isDirectDependency: 0
  version: 3.2.1
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.nuget.newtonsoft-json@74deb55db2a0
  assetPath: Packages/com.unity.nuget.newtonsoft-json
  name: com.unity.nuget.newtonsoft-json
  displayName: Newtonsoft Json
  author:
    name: 
    email: 
    url: 
  category: 
  type: library
  description: 'Newtonsoft Json for use in Unity projects and Unity packages. Currently
    synced to version 13.0.2.


    This package is used for advanced json serialization
    and deserialization. Most Unity users will be better suited using the existing
    json tools built into Unity.

    To avoid assembly clashes, please use this
    package if you intend to use Newtonsoft Json.'
  errors: []
  versions:
    all:
    - 1.0.0-preview.2
    - 1.0.0-preview.3
    - 1.0.0-preview.4
    - 1.0.1-preview.1
    - 1.1.2
    - 2.0.0-preview
    - 2.0.0-preview.1
    - 2.0.0-preview.2
    - 2.0.0
    - 2.0.1-preview.1
    - 2.0.2
    - 3.0.1
    - 3.0.2
    - 3.1.0
    - 3.2.0
    - 3.2.1
    compatible:
    - 3.2.1
    recommended: 3.2.1
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638186318800000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.nuget.newtonsoft-json.git
    revision: d8e49aef8979bef617144382052ec2f479645eaf
    path: 
  unityLifecycle:
    version: 3.2.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 0
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"* Fixed Newtonsoft DLL when compiling with netstandard
    2.0."}'
  assetStore:
    productId: 
  fingerprint: 74deb55db2a0c29ddfda576608bcb86abbd13ee6
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 3.2.1
    minimumUnityVersion: 2018.4.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.settings-manager@2.1.0
  testable: 0
  isDirectDependency: 0
  version: 2.1.0
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.settings-manager@41738c275190
  assetPath: Packages/com.unity.settings-manager
  name: com.unity.settings-manager
  displayName: Settings Manager
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: A framework for making any serializable field a setting, complete
    with a pre-built settings interface.
  errors: []
  versions:
    all:
    - 0.1.0-preview.4
    - 0.1.0-preview.8
    - 1.0.0
    - 1.0.1
    - 1.0.2
    - 1.0.3
    - 2.0.0
    - 2.0.1
    - 2.1.0
    compatible:
    - 2.1.0
    recommended: 2.1.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638792046006940000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.settings-manager@2.1/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.settings-manager.git
    revision: 6eb5ed85eaf3276cc09f0cc4fc3311c72f0fc196
    path: 
  unityLifecycle:
    version: 2.1.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 0
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Changes\n\n- Enable Auto Reference on main assembly.\n\n###
    Bug Fixes\n\n- [case: PBLD-56] Added tooltip to \"Options\" gear icon to specify
    that context menu items are relevant only to the selected category."}'
  assetStore:
    productId: 
  fingerprint: 41738c27519039c335849eb78949382f4d7a3544
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.1.0
    minimumUnityVersion: 2022.3.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.autodesk.fbx@5.1.1
  testable: 0
  isDirectDependency: 0
  version: 5.1.1
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.autodesk.fbx@5797ff6b31c7
  assetPath: Packages/com.autodesk.fbx
  name: com.autodesk.fbx
  displayName: Autodesk FBX SDK for Unity
  author:
    name: Unity
    email: 
    url: 
  category: 
  type: assets
  description: "This package provides Unity C# bindings to the Autodesk\xAE FBX\xAE
    SDK."
  errors: []
  versions:
    all:
    - 2.0.0-preview
    - 2.0.0-preview.4
    - 2.0.0-preview.5
    - 2.0.0-preview.6
    - 2.0.0-preview.7
    - 2.0.1-preview.1
    - 3.0.0-preview.1
    - 3.0.1-preview.1
    - 3.1.0-preview.1
    - 3.1.0-preview.2
    - 4.0.0-pre.1
    - 4.0.0-pre.2
    - 4.0.1
    - 4.1.0-pre.1
    - 4.1.0
    - 4.1.1
    - 4.1.2
    - 4.2.0-pre.1
    - 4.2.0
    - 4.2.1
    - 5.0.0-pre.1
    - 5.0.0
    - 5.1.0-pre.1
    - 5.1.0
    - 5.1.1
    compatible:
    - 5.1.1
    recommended: 5.1.1
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - fbx
  - animation
  - modeling
  - maya
  - max
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638464332400000000
  documentationUrl: https://docs.unity3d.com/Packages/com.autodesk.fbx@5.1/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.autodesk.fbx.git
    revision: dccfc2a79ac7abfe7bd6f91f713f990f2fedd141
    path: 
  unityLifecycle:
    version: 5.1.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 0
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"* Update FBX SDK to 2020.3.4.\n* Add support for Windows
    ARM64."}'
  assetStore:
    productId: 
  fingerprint: 5797ff6b31c7c66189bffe1adc9cbf0ad5a9cbbf
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 5.1.1
    minimumUnityVersion: 2020.3.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.ide.visualstudio@2.0.23
  testable: 0
  isDirectDependency: 0
  version: 2.0.23
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.ide.visualstudio@198cdf337d13
  assetPath: Packages/com.unity.ide.visualstudio
  name: com.unity.ide.visualstudio
  displayName: Visual Studio Editor
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Code editor integration for supporting Visual Studio as code editor
    for unity. Adds support for generating csproj files for intellisense purposes,
    auto discovery of installations, etc.
  errors: []
  versions:
    all:
    - 1.0.2
    - 1.0.3
    - 1.0.4
    - 1.0.9
    - 1.0.10
    - 1.0.11
    - 2.0.0
    - 2.0.1
    - 2.0.2
    - 2.0.3
    - 2.0.5
    - 2.0.7
    - 2.0.8
    - 2.0.9
    - 2.0.11
    - 2.0.12
    - 2.0.14
    - 2.0.15
    - 2.0.16
    - 2.0.17
    - 2.0.18
    - 2.0.20
    - 2.0.21
    - 2.0.22
    - 2.0.23
    compatible:
    - 2.0.23
    recommended: 2.0.23
    deprecated: []
  dependencies:
  - name: com.unity.test-framework
    version: 1.1.9
  resolvedDependencies:
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638786696173840000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ide.visualstudio@2.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.ide.visualstudio.git
    revision: 0fe3b29f9aff2b90b9f0962ae35036a824d3dd6b
    path: 
  unityLifecycle:
    version: 2.0.23
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"Integration:\n\n- Monitor `additionalfile` extension
    by default.\n- Try opening a Visual Studio Code workspace if there''s one (`.code-workspace`
    file in the Unity project).\n\nProject generation:\n\n- Identify `asset`, `meta`,
    `prefab` and `unity` files as `yaml` (Visual Studio Code).\n- Add `sln`/`csproj`
    file nesting (Visual Studio Code).\n- Improve SDK style project generation."}'
  assetStore:
    productId: 
  fingerprint: 198cdf337d13c83ca953581515630d66b779e92b
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.23
    minimumUnityVersion: 2019.4.25f1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.ide.rider@3.0.36
  testable: 0
  isDirectDependency: 0
  version: 3.0.36
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.ide.rider@4d374c7eb6db
  assetPath: Packages/com.unity.ide.rider
  name: com.unity.ide.rider
  displayName: JetBrains Rider Editor
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The JetBrains Rider Editor package provides an integration for using
    the JetBrains Rider IDE as a code editor for Unity. It adds support for generating
    .csproj files for code completion and auto-discovery of installations.
  errors: []
  versions:
    all:
    - 1.0.2
    - 1.0.6
    - 1.0.8
    - 1.1.0
    - 1.1.1
    - 1.1.2-preview
    - 1.1.2-preview.2
    - 1.1.3-preview.1
    - 1.1.4-preview
    - 1.1.4
    - 1.2.0-preview
    - 1.2.1
    - 2.0.0-preview
    - 2.0.1
    - 2.0.2
    - 2.0.3
    - 2.0.5
    - 2.0.7
    - 3.0.1
    - 3.0.2
    - 3.0.3
    - 3.0.4
    - 3.0.5
    - 3.0.6
    - 3.0.7
    - 3.0.9
    - 3.0.10
    - 3.0.12
    - 3.0.13
    - 3.0.14
    - 3.0.15
    - 3.0.16
    - 3.0.17
    - 3.0.18
    - 3.0.20
    - 3.0.21
    - 3.0.22
    - 3.0.24
    - 3.0.25
    - 3.0.26
    - 3.0.27
    - 3.0.28
    - 3.0.31
    - 3.0.34
    - 3.0.35
    - 3.0.36
    compatible:
    - 3.0.36
    recommended: 3.0.36
    deprecated: []
  dependencies:
  - name: com.unity.ext.nunit
    version: 1.0.6
  resolvedDependencies:
  - name: com.unity.ext.nunit
    version: 2.0.5
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638799562419060000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ide.rider@3.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.ide.rider.git
    revision: b5315083ab3861d21f6ab2ed0d9514daf04bf208
    path: 
  unityLifecycle:
    version: 3.0.36
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"- fix RIDER-124592 Avoid affecting \"Strip Engine Code\"
    while IL2CPP debug enabled"}'
  assetStore:
    productId: 
  fingerprint: 4d374c7eb6db6907c7e6925e3086c3c73f926e13
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 3.0.36
    minimumUnityVersion: 2019.4.6f1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.editorcoroutines@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.editorcoroutines@7d48783e7b8c
  assetPath: Packages/com.unity.editorcoroutines
  name: com.unity.editorcoroutines
  displayName: Editor Coroutines
  author:
    name: 
    email: 
    url: 
  category: 
  type: asset
  description: 'The editor coroutines package allows developers to start constructs
    similar to Unity''s monobehaviour based coroutines within the editor using abitrary
    objects. '
  errors: []
  versions:
    all:
    - 0.0.1-preview.3
    - 0.0.1-preview.4
    - 0.0.1-preview.5
    - 0.0.2-preview.1
    - 0.1.0-preview.1
    - 0.1.0-preview.2
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - coroutine
  - coroutines
  - editor
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637232611380000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.editorcoroutines.git
    revision: f67fc9992bbc7a553b17375de53a8b2db136528e
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 7d48783e7b8cfcee5f8ef9ba787ed0d9dad4ebca
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 2018.1.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.performance.profile-analyzer@1.2.3
  testable: 0
  isDirectDependency: 0
  version: 1.2.3
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.performance.profile-analyzer@a68e7bc84997
  assetPath: Packages/com.unity.performance.profile-analyzer
  name: com.unity.performance.profile-analyzer
  displayName: Profile Analyzer
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: "The Profile Analyzer tool supports the standard Unity Profiler. You
    can use it to analyze multiple frames and multiple data sets of the CPU data
    in the Profiler.\n\nMain features: \n\u25AA Multi-frame analysis of a single
    set of Profiler CPU data \n\u25AA Comparison of two multi-frame profile scans
    \n\n"
  errors: []
  versions:
    all:
    - 0.4.0-preview.3
    - 0.4.0-preview.5
    - 0.4.0-preview.6
    - 0.5.0-preview.1
    - 0.6.0-preview.1
    - 0.7.0-preview.4
    - 1.0.0
    - 1.0.1
    - 1.0.2
    - 1.0.3
    - 1.1.0-pre.2
    - 1.1.0
    - 1.1.1
    - 1.2.2
    - 1.2.3
    compatible:
    - 1.2.3
    recommended: 1.2.3
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638699545898200000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.performance.profile-analyzer@1.2/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.performance.profile-analyzer.git
    revision: 835e61d94bd201d0dea5763dff75e86b6b61de29
    path: 
  unityLifecycle:
    version: 1.2.3
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n\n* Fixed minimum supported version in documentation.\n*
    Fixed PROFB-199; Unchecking ''Hide Removed Markers'' doesn''t work."}'
  assetStore:
    productId: 
  fingerprint: a68e7bc849973d943853204178d08a2bc7656ffe
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.2.3
    minimumUnityVersion: 2020.3.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.test-framework@1.5.1
  testable: 0
  isDirectDependency: 0
  version: 1.5.1
  source: 2
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.test-framework@a6f5be5f149c
  assetPath: Packages/com.unity.test-framework
  name: com.unity.test-framework
  displayName: Test Framework
  author:
    name: 
    email: 
    url: 
  category: Unity Test Framework
  type: 
  description: Test framework for running Edit mode and Play mode tests in Unity.
  errors: []
  versions:
    all:
    - 1.5.1
    compatible:
    - 1.5.1
    recommended: 1.5.1
    deprecated: []
  dependencies:
  - name: com.unity.ext.nunit
    version: 2.0.3
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords:
  - Test
  - TestFramework
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.5.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: a6f5be5f149c799640cce1ac6aa4057426adc0c7
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.5.1
    minimumUnityVersion: 2022.3.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.testtools.codecoverage@1.2.6
  testable: 0
  isDirectDependency: 0
  version: 1.2.6
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.testtools.codecoverage@205a02cbcb39
  assetPath: Packages/com.unity.testtools.codecoverage
  name: com.unity.testtools.codecoverage
  displayName: Code Coverage
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: Use this package to export code coverage data and reports from your
    automated tests. Additionally, the Code Coverage package offers a Coverage Recording
    feature which allows capturing coverage data on demand, for manual testing or
    when there are no automated tests in the project.
  errors: []
  versions:
    all:
    - 0.2.0-preview
    - 0.2.1-preview
    - 0.2.2-preview
    - 0.2.3-preview
    - 0.3.0-preview
    - 0.3.1-preview
    - 0.4.0-preview
    - 0.4.1-preview
    - 0.4.2-preview
    - 0.4.3-preview
    - 1.0.0-pre.1
    - 1.0.0-pre.2
    - 1.0.0-pre.3
    - 1.0.0-pre.4
    - 1.0.0
    - 1.0.1
    - 1.1.0
    - 1.1.1
    - 1.2.0-exp.1
    - 1.2.0-exp.2
    - 1.2.0-exp.3
    - 1.2.0-exp.4
    - 1.2.0-exp.5
    - 1.2.0-exp.6
    - 1.2.0-exp.7
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.2.4
    - 1.2.5
    - 1.2.6
    compatible:
    - 1.2.6
    recommended: 1.2.6
    deprecated: []
  dependencies:
  - name: com.unity.test-framework
    version: 1.0.16
  - name: com.unity.settings-manager
    version: 1.0.1
  resolvedDependencies:
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.settings-manager
    version: 2.1.0
  keywords:
  - test
  - coverage
  - testing
  - opencover
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638575857900000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.testtools.codecoverage@1.2/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.testtools.codecoverage.git
    revision: 959db8ff97eedf9b211ad5cf320b87ac01f1e90f
    path: 
  unityLifecycle:
    version: 1.2.6
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixes\n- Documentation: Fixed formatting in [Using
    Code Coverage in batchmode](https://docs.unity3d.com/Packages/com.unity.testtools.codecoverage@1.2/manual/CoverageBatchmode.html)
    page (case [COV-40](https://issuetracker.unity3d.com/issues/docs-formatting-in-using-code-coverage-in-batchmode-docs-page-is-incorrect)).\n-
    Removed the references to the deprecated FindObjectOfType method in the *Asteroids
    sample project* (case [COV-42](https://issuetracker.unity3d.com/issues/sample-project-is-using-obsolete-findobjectoftype-method-which-causes-multiple-warnings-in-console-when-it-is-imported)).\n-
    Added missing logs for the ReportGenerator (case [COV-46](https://issuetracker.unity3d.com/issues/code-coverage-package-does-not-report-some-of-the-internal-reportgenerator-errors))."}'
  assetStore:
    productId: 
  fingerprint: 205a02cbcb39584f20b51c49b853047aceb3a3a7
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.2.6
    minimumUnityVersion: 2019.3.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.burst@1.8.23
  testable: 0
  isDirectDependency: 0
  version: 1.8.23
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.burst@6aff1dd08a0c
  assetPath: Packages/com.unity.burst
  name: com.unity.burst
  displayName: Burst
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Burst is a compiler that translates from IL/.NET bytecode to highly
    optimized native code using LLVM.
  errors: []
  versions:
    all:
    - 0.2.4-preview.5
    - 0.2.4-preview.7
    - 0.2.4-preview.11
    - 0.2.4-preview.12
    - 0.2.4-preview.13
    - 0.2.4-preview.14
    - 0.2.4-preview.15
    - 0.2.4-preview.16
    - 0.2.4-preview.17
    - 0.2.4-preview.18
    - 0.2.4-preview.19
    - 0.2.4-preview.20
    - 0.2.4-preview.21
    - 0.2.4-preview.22
    - 0.2.4-preview.23
    - 0.2.4-preview.24
    - 0.2.4-preview.25
    - 0.2.4-preview.30
    - 0.2.4-preview.31
    - 0.2.4-preview.33
    - 0.2.4-preview.34
    - 0.2.4-preview.37
    - 0.2.4-preview.41
    - 0.2.4-preview.45
    - 0.2.4-preview.48
    - 0.2.4-preview.50
    - 1.1.0-preview.2
    - 1.1.0-preview.3
    - 1.1.0-preview.4
    - 1.1.1
    - 1.1.2
    - 1.1.3-preview.3
    - 1.2.0-preview.1
    - 1.2.0-preview.5
    - 1.2.0-preview.6
    - 1.2.0-preview.8
    - 1.2.0-preview.9
    - 1.2.0-preview.10
    - 1.2.0-preview.11
    - 1.2.0-preview.12
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.3.0-preview.1
    - 1.3.0-preview.2
    - 1.3.0-preview.3
    - 1.3.0-preview.4
    - 1.3.0-preview.5
    - 1.3.0-preview.6
    - 1.3.0-preview.7
    - 1.3.0-preview.8
    - 1.3.0-preview.9
    - 1.3.0-preview.10
    - 1.3.0-preview.11
    - 1.3.0-preview.12
    - 1.3.0-preview.13
    - 1.3.0
    - 1.3.1
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.6
    - 1.3.7
    - 1.3.8
    - 1.3.9
    - 1.4.0-pre.1
    - 1.4.0-preview.1
    - 1.4.0-preview.2
    - 1.4.0-preview.3
    - 1.4.0-preview.4
    - 1.4.0-preview.5
    - 1.4.1-pre.1
    - 1.4.1-pre.2
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4-preview.1
    - 1.4.4-preview.2
    - 1.4.4
    - 1.4.5
    - 1.4.6
    - 1.4.7
    - 1.4.8
    - 1.4.9
    - 1.4.11
    - 1.5.0-pre.3
    - 1.5.0-pre.4
    - 1.5.0-pre.5
    - 1.5.0
    - 1.5.1
    - 1.5.2
    - 1.5.3
    - 1.5.4
    - 1.5.5
    - 1.5.6-preview.1
    - 1.5.6
    - 1.6.0-pre.2
    - 1.6.0-pre.3
    - 1.6.0-pre.4
    - 1.6.0
    - 1.6.1
    - 1.6.2
    - 1.6.3
    - 1.6.4
    - 1.6.5
    - 1.6.6
    - 1.7.0-pre.1
    - 1.7.0
    - 1.7.1
    - 1.7.2
    - 1.7.3
    - 1.7.4
    - 1.8.0-pre.1
    - 1.8.0-pre.2
    - 1.8.0
    - 1.8.1
    - 1.8.2
    - 1.8.3
    - 1.8.4
    - 1.8.7
    - 1.8.8
    - 1.8.9
    - 1.8.10
    - 1.8.11
    - 1.8.12
    - 1.8.13
    - 1.8.14
    - 1.8.15
    - 1.8.16
    - 1.8.17
    - 1.8.18
    - 1.8.19
    - 1.8.20
    - 1.8.21
    - 1.8.22
    - 1.8.23
    - 1.8.24
    compatible:
    - 1.8.23
    - 1.8.24
    recommended: 1.8.24
    deprecated: []
  dependencies:
  - name: com.unity.mathematics
    version: 1.2.1
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638856629417660000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.burst@1.8/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/burst.git
    revision: 0a4fca7c4d2ed5ed3ce7fd57c6944390dd4745a7
    path: 
  unityLifecycle:
    version: 1.8.23
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n- Fixed \"Building for ''visionOS-simulator'',
    but linking in object file built for ''visionOS''\" errors that could occur in
    1.8.22\n- Fixed crash that could occur during Burst compilation for methods with
    `Span<T>` or `ReadOnlySpan<T>` parameter types\n- Fixed Burst compilation error
    related to `Aliasing.ExpectNotAliased` failing because of unexpected aliasing
    of `[NativeContainer]` types\n\n### Changed\n- On QNX and Embedded Linux, the
    `BurstDebugInformation_DoNotShip` folder is now prefixed with the player build
    directory name instead of the product name."}'
  assetStore:
    productId: 
  fingerprint: 6aff1dd08a0c2f92ddb7f56ec033a5cb88967056
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.8.23
    minimumUnityVersion: 2021.3.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.dt.app-ui@1.3.1
  testable: 0
  isDirectDependency: 0
  version: 1.3.1
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.dt.app-ui@7b87c8225c06
  assetPath: Packages/com.unity.dt.app-ui
  name: com.unity.dt.app-ui
  displayName: App UI
  author:
    name: 
    email: 
    url: 
  category: UI
  type: 
  description: App UI is a versatile and customizable UI library for Unity that provides
    essential building blocks for creating beautiful and responsive user interfaces.
    With support for MVVM architecture, state management, and localization, App UI
    simplifies app development while maintaining flexibility and extensibility. The
    library includes a wide range of UI components, design tokens, and symbols, all
    of which can be easily customized to fit your app's unique style and branding.
  errors: []
  versions:
    all:
    - 0.2.7
    - 0.2.9
    - 0.2.11
    - 0.2.12
    - 0.3.1
    - 0.3.2
    - 0.3.3
    - 0.3.4
    - 0.3.5
    - 0.3.8
    - 0.3.9
    - 0.4.0
    - 0.4.1
    - 0.4.2
    - 0.5.0
    - 0.5.1
    - 0.5.2
    - 0.5.3
    - 0.5.4
    - 0.5.5
    - 0.6.0
    - 0.6.1
    - 0.6.2
    - 0.6.3
    - 0.6.4
    - 0.6.5
    - 1.0.0-pre.1
    - 1.0.0-pre.2
    - 1.0.0-pre.3
    - 1.0.0-pre.4
    - 1.0.0-pre.5
    - 1.0.0-pre.6
    - 1.0.0-pre.7
    - 1.0.0-pre.8
    - 1.0.0-pre.9
    - 1.0.0-pre.10
    - 1.0.0-pre.11
    - 1.0.0-pre.12
    - 1.0.0-pre.13
    - 1.0.0-pre.14
    - 1.0.0-pre.15
    - 1.0.1
    - 1.0.2
    - 1.0.3
    - 1.0.4
    - 1.0.5
    - 1.0.6
    - 1.1.0
    - 1.1.1
    - 1.2.0
    - 1.2.1
    - 1.3.1
    - 2.0.0-pre.2
    - 2.0.0-pre.3
    - 2.0.0-pre.4
    - 2.0.0-pre.5
    - 2.0.0-pre.6
    - 2.0.0-pre.7
    - 2.0.0-pre.8
    - 2.0.0-pre.9
    - 2.0.0-pre.10
    - 2.0.0-pre.11
    - 2.0.0-pre.12
    - 2.0.0-pre.13
    - 2.0.0-pre.14
    - 2.0.0-pre.15
    - 2.0.0-pre.16
    - 2.0.0-pre.17
    - 2.0.0-pre.18
    - 2.0.0-pre.19
    - 2.0.0-pre.20
    - 2.0.0-pre.21
    - 2.0.0-pre.22
    - 2.0.0
    - 2.1.0-pre.1
    - 2.1.0-pre.2
    - 2.1.0-pre.3
    - 2.1.0
    - 2.1.1-pre.1
    - 2.1.1
    compatible:
    - 1.3.1
    - 2.0.0-pre.2
    - 2.0.0-pre.3
    - 2.0.0-pre.4
    - 2.0.0-pre.5
    - 2.0.0-pre.6
    - 2.0.0-pre.7
    - 2.0.0-pre.8
    - 2.0.0-pre.9
    - 2.0.0-pre.10
    - 2.0.0-pre.11
    - 2.0.0-pre.12
    - 2.0.0-pre.13
    - 2.0.0-pre.14
    - 2.0.0-pre.15
    - 2.0.0-pre.16
    - 2.0.0-pre.17
    - 2.0.0-pre.18
    - 2.0.0-pre.19
    - 2.0.0-pre.20
    - 2.0.0-pre.21
    - 2.0.0-pre.22
    - 2.0.0
    - 2.1.0-pre.1
    - 2.1.0-pre.2
    - 2.1.0-pre.3
    - 2.1.0
    - 2.1.1-pre.1
    - 2.1.1
    recommended: 1.3.1
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.androidjni
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.screencapture
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.androidjni
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  - name: com.unity.modules.screencapture
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords:
  - app
  - ui
  - components
  - runtime
  - framework
  - navigation
  - dependency-injection
  - mvvm
  - undostack
  - redux
  - state-management
  - dark-theme
  - data-binding
  - cloud
  - unity
  - app-ui
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638887003362730000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.dt.app-ui@1.3/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/unity-app-ui.git
    revision: 3c151f9a0a6981b1f7293b70420639802d5c6c1b
    path: 
  unityLifecycle:
    version: 1.3.1
    nextVersion: 2.1.0-pre.2
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 0
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Removed\n\n- Removed warning messages about using
    obsolete code."}'
  assetStore:
    productId: 
  fingerprint: 7b87c8225c066f90ed48f3fbee5375942c16f476
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.3.1
    minimumUnityVersion: 2021.3.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.ai.toolkit@1.0.0-pre.19
  testable: 0
  isDirectDependency: 0
  version: 1.0.0-pre.19
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.ai.toolkit@493fe336b8c1
  assetPath: Packages/com.unity.ai.toolkit
  name: com.unity.ai.toolkit
  displayName: AI Toolkit
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: AI Toolkit is a package that provides tools for adding generative
    AI to Unity.
  errors: []
  versions:
    all:
    - 1.0.0-pre.2
    - 1.0.0-pre.3
    - 1.0.0-pre.4
    - 1.0.0-pre.5
    - 1.0.0-pre.6
    - 1.0.0-pre.7
    - 1.0.0-pre.8
    - 1.0.0-pre.9
    - 1.0.0-pre.10
    - 1.0.0-pre.11
    - 1.0.0-pre.12
    - 1.0.0-pre.13
    - 1.0.0-pre.14
    - 1.0.0-pre.15
    - 1.0.0-pre.16
    - 1.0.0-pre.17
    - 1.0.0-pre.18
    - 1.0.0-pre.19
    compatible:
    - 1.0.0-pre.2
    - 1.0.0-pre.3
    - 1.0.0-pre.4
    - 1.0.0-pre.5
    - 1.0.0-pre.6
    - 1.0.0-pre.7
    - 1.0.0-pre.8
    - 1.0.0-pre.9
    - 1.0.0-pre.10
    - 1.0.0-pre.11
    - 1.0.0-pre.12
    - 1.0.0-pre.13
    - 1.0.0-pre.14
    - 1.0.0-pre.15
    - 1.0.0-pre.16
    - 1.0.0-pre.17
    - 1.0.0-pre.18
    - 1.0.0-pre.19
    recommended: 
    deprecated: []
  dependencies:
  - name: com.unity.nuget.newtonsoft-json
    version: 3.2.1
  resolvedDependencies:
  - name: com.unity.nuget.newtonsoft-json
    version: 3.2.1
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638907945038530000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ai.toolkit@1.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/UnityAIWorkflows.git
    revision: 55389365acd56d9d9c807df4560c5a77a6af63c4
    path: 
  unityLifecycle:
    version: 
    nextVersion: 1.0.0-pre.8
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 0
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Changed\n\n- For the purposes of AI Toolkit features
    and for the duration of access token validity, project connections now persist
    through failures of UnityConnect and Unity Hub."}'
  assetStore:
    productId: 
  fingerprint: 493fe336b8c1049873d9a70a76ba3c04a33dd870
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0-pre.2
    minimumUnityVersion: 6000.2.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.mathematics@1.3.2
  testable: 0
  isDirectDependency: 0
  version: 1.3.2
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.mathematics@8017b507cc74
  assetPath: Packages/com.unity.mathematics
  name: com.unity.mathematics
  displayName: Mathematics
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: Unity's C# SIMD math library providing vector types and math functions
    with a shader like syntax.
  errors: []
  versions:
    all:
    - 0.0.12-preview.2
    - 0.0.12-preview.5
    - 0.0.12-preview.8
    - 0.0.12-preview.10
    - 0.0.12-preview.11
    - 0.0.12-preview.13
    - 0.0.12-preview.17
    - 0.0.12-preview.19
    - 0.0.12-preview.20
    - 1.0.0-preview.1
    - 1.0.1
    - 1.1.0-preview.1
    - 1.1.0
    - 1.2.1
    - 1.2.4
    - 1.2.5
    - 1.2.6
    - 1.3.1
    - 1.3.2
    compatible:
    - 1.3.2
    recommended: 1.3.2
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - unity
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638409134840000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.mathematics@1.3/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/Unity.Mathematics.git
    revision: 1695a8503482a3131be78cc26308a93f82c05b04
    path: 
  unityLifecycle:
    version: 1.3.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n* Fixed `math.hash` crash when using IL2CPP
    builds on Arm 32 bit devices.\n* Fixed obsolete method usage warnings for `MatrixDrawer.CanCacheInspectorGUI`
    and `PrimitiveVectorDrawer.CanCacheInspectorGUI` in UNITY_2023_2_OR_NEWER.\n*
    Updated minimum editor version to 2021.3"}'
  assetStore:
    productId: 
  fingerprint: 8017b507cc74bf0a1dd14b18aa860569f807314d
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.3.2
    minimumUnityVersion: 2021.3.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.serialization@3.1.2
  testable: 0
  isDirectDependency: 0
  version: 3.1.2
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.serialization@582cbf30bbfd
  assetPath: Packages/com.unity.serialization
  name: com.unity.serialization
  displayName: Serialization
  author:
    name: 
    email: 
    url: 
  category: 
  type: asset
  description: Use Unity's Serialization to serialize and deserialize from/to JSON
    and Binary formats.
  errors: []
  versions:
    all:
    - 0.2.1-preview
    - 0.3.0-preview
    - 0.3.1-preview
    - 0.4.0-preview
    - 0.4.1-preview
    - 0.5.0-preview
    - 0.5.1-preview
    - 0.6.0-preview
    - 0.6.1-preview
    - 0.6.2-preview
    - 0.6.3-preview
    - 0.6.4-preview
    - 1.0.0-preview.5
    - 1.1.0-preview
    - 1.1.1-preview
    - 1.2.0-preview
    - 1.3.0-preview
    - 1.3.1-preview
    - 1.4.3-preview
    - 1.5.0-preview
    - 1.6.0-preview
    - 1.6.1-preview
    - 1.6.2-preview
    - 1.7.0-preview.1
    - 1.7.1-preview
    - 1.7.3-preview
    - 1.7.4-preview
    - 1.7.5-preview
    - 1.8.2-preview
    - 1.8.3-preview
    - 2.0.0-exp.11
    - 2.0.0-exp.13
    - 2.1.0-exp.1
    - 2.1.1-exp.1
    - 2.1.2-exp.1
    - 3.0.0-pre.1
    - 3.0.0-pre.2
    - 3.0.0-pre.3
    - 3.1.1
    - 3.1.2
    compatible:
    - 3.1.2
    recommended: 3.1.2
    deprecated: []
  dependencies:
  - name: com.unity.burst
    version: 1.7.2
  - name: com.unity.collections
    version: 2.4.2
  resolvedDependencies:
  - name: com.unity.burst
    version: 1.8.23
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.7
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.nuget.mono-cecil
    version: 1.11.5
  - name: com.unity.test-framework.performance
    version: 3.1.0
  keywords:
  - serialization
  - json
  - binary
  - unity
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638599246800000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.serialization@3.1/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.properties.git
    revision: cb798388e9a2cfa022a3bbd84012a9c61c61b7f4
    path: 
  unityLifecycle:
    version: 3.1.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 0
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Changed\n* Updated `com.unity.collections` to version
    `2.4.2`.\n* Updated minimum Unity version to `2022.3`.\n\n### Fixed\n* Fixed
    the way type names are exported to ensure that they can be correctly retrieved
    using `Type.GetType()`."}'
  assetStore:
    productId: 
  fingerprint: 582cbf30bbfd3c90a738e49c2ad69eafd8803f1d
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 3.1.2
    minimumUnityVersion: 2022.2.0a18
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.2d.common@11.0.1
  testable: 0
  isDirectDependency: 0
  version: 11.0.1
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.2d.common@dd402daace1b
  assetPath: Packages/com.unity.2d.common
  name: com.unity.2d.common
  displayName: 2D Common
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: 2D Common is a package that contains shared functionalities that are
    used by most of the other 2D packages.
  errors: []
  versions:
    all:
    - 1.0.9-preview.1
    - 1.0.9-preview.2
    - 1.0.10-preview
    - 1.0.11-preview.1
    - 1.1.0-preview.1
    - 1.1.0-preview.2
    - 1.2.0-preview.1
    - 2.0.1
    - 2.0.2
    - 2.1.0
    - 2.1.1
    - 2.1.2
    - 3.0.0
    - 3.0.1
    - 4.0.0
    - 4.0.1
    - 4.0.2
    - 4.0.3
    - 4.0.4
    - 4.1.0
    - 4.2.0
    - 4.2.1
    - 5.0.0-pre.1
    - 5.0.0-pre.2
    - 5.0.0
    - 6.0.0-pre.2
    - 6.0.0-pre.3
    - 6.0.0-pre.4
    - 6.0.0
    - 6.0.1
    - 6.0.2
    - 6.0.3
    - 6.0.4
    - 6.0.5
    - 6.0.6
    - 6.0.7
    - 6.0.8
    - 6.1.0
    - 7.0.0-pre.3
    - 7.0.0-pre.4
    - 7.0.0
    - 7.0.1
    - 7.0.2
    - 7.0.3
    - 8.0.0-pre.1
    - 8.0.0-pre.2
    - 8.0.0
    - 8.0.1
    - 8.0.2
    - 8.0.3
    - 8.0.4
    - 8.1.0
    - 8.1.1
    - 9.0.0-pre.1
    - 9.0.0-pre.2
    - 9.0.0
    - 9.0.1
    - 9.0.2
    - 9.0.3
    - 9.0.4
    - 9.0.5
    - 9.0.6
    - 9.0.7
    - 9.1.0
    - 9.1.1
    - 10.0.0
    - 11.0.0
    - 11.0.1
    - 12.0.0
    compatible:
    - 11.0.1
    recommended: 11.0.1
    deprecated: []
  dependencies:
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.mathematics
    version: 1.1.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.burst
    version: 1.8.4
  resolvedDependencies:
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.burst
    version: 1.8.23
  keywords:
  - 2d
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 638840901264720000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.2d.common@11.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/2d.git
    revision: 98a4faf22484a3679f8d6a3b60b5d07f5e652cd7
    path: 
  unityLifecycle:
    version: 11.0.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 0
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Changed\n- Added support code for PolySpatial."}'
  assetStore:
    productId: 
  fingerprint: dd402daace1b7dbeb70cb9e7ae044cc4b10f989a
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 11.0.1
    minimumUnityVersion: 6000.2.0a9
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.searcher@4.9.3
  testable: 0
  isDirectDependency: 0
  version: 4.9.3
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.searcher@1e17ce91558d
  assetPath: Packages/com.unity.searcher
  name: com.unity.searcher
  displayName: Searcher
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: General search window for use in the Editor. First target use is for
    GraphView node search.
  errors: []
  versions:
    all:
    - 4.0.0-preview
    - 4.0.0
    - 4.0.7-preview
    - 4.0.7
    - 4.0.8-preview
    - 4.0.9
    - 4.1.0-preview
    - 4.1.0
    - 4.2.0
    - 4.3.0
    - 4.3.1
    - 4.3.2
    - 4.6.0-preview
    - 4.7.0-preview
    - 4.9.1
    - 4.9.2
    - 4.9.3
    compatible:
    - 4.9.2
    - 4.9.3
    recommended: 4.9.3
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - search
  - searcher
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638731478077990000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.searcher@4.9/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.searcher.git
    revision: 6fad693b6604ae7175b59ebb4990d9a0b6c1d012
    path: 
  unityLifecycle:
    version: 4.9.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 0
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"- Fixed a bug where spaces are removed when highlighted.\n-
    Changed VisualSplitter to twoPaneSplitView and updated indentation"}'
  assetStore:
    productId: 
  fingerprint: 1e17ce91558d1d9127554adc03d275f39a7466a2
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 4.9.2
    minimumUnityVersion: 2019.1.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.rendering.light-transport@1.0.1
  testable: 0
  isDirectDependency: 0
  version: 1.0.1
  source: 2
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.rendering.light-transport@2c9279f90d7c
  assetPath: Packages/com.unity.rendering.light-transport
  name: com.unity.rendering.light-transport
  displayName: Unity Light Transport Library
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Unity Light Transport Library exposes reusable code for writing light
    transport algorithms such as raytracing or pathtracing
  errors: []
  versions:
    all:
    - 1.0.1
    compatible:
    - 1.0.1
    recommended: 1.0.1
    deprecated: []
  dependencies:
  - name: com.unity.collections
    version: 2.2.0
  - name: com.unity.mathematics
    version: 1.2.4
  - name: com.unity.modules.terrain
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.collections
    version: 2.5.7
  - name: com.unity.burst
    version: 1.8.23
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.nuget.mono-cecil
    version: 1.11.5
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  keywords:
  - raytracing
  - pathtracing
  - monte-carlo
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 0
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 2c9279f90d7c8dfb6e76e3f90b6b3be1cafabfbc
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.1
    minimumUnityVersion: 2023.3.0b1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.test-framework.performance@3.1.0
  testable: 0
  isDirectDependency: 0
  version: 3.1.0
  source: 1
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.test-framework.performance@92d1d09a72ed
  assetPath: Packages/com.unity.test-framework.performance
  name: com.unity.test-framework.performance
  displayName: Performance testing API
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Package that extends Unity Test Framework package. Adds performance
    testing capabilities and collects configuration metadata.
  errors: []
  versions:
    all:
    - 0.1.27-preview
    - 0.1.29-preview
    - 0.1.31-preview
    - 0.1.33-preview
    - 0.1.34-preview
    - 0.1.36-preview
    - 0.1.37-preview
    - 0.1.39-preview
    - 0.1.40-preview
    - 0.1.41-preview
    - 0.1.42-preview
    - 0.1.44-preview
    - 0.1.45-preview
    - 0.1.47-preview
    - 0.1.48-preview
    - 0.1.49-preview
    - 0.1.50-preview
    - 1.0.4-preview
    - 1.0.6-preview
    - 1.0.9-preview
    - 1.1.2-preview
    - 1.2.0-preview
    - 1.2.1-preview
    - 1.2.3-preview
    - 1.2.5-preview
    - 1.2.6-preview
    - 1.3.0-preview
    - 1.3.1-preview
    - 1.3.2-preview
    - 1.3.3-preview
    - 2.0.1-preview
    - 2.0.2-preview
    - 2.0.3-preview
    - 2.0.6-preview
    - 2.0.7-preview
    - 2.0.8-preview
    - 2.0.9-preview
    - 2.1.0-preview
    - 2.2.0-preview
    - 2.3.1-preview
    - 2.4.1-preview
    - 2.5.1-preview
    - 2.6.0-preview
    - 2.7.0-preview
    - 2.8.0-preview
    - 2.8.1-preview
    - 3.0.0-pre.1
    - 3.0.0-pre.2
    - 3.0.1
    - 3.0.2
    - 3.0.3
    - 3.1.0
    compatible:
    - 3.1.0
    recommended: 3.1.0
    deprecated: []
  dependencies:
  - name: com.unity.test-framework
    version: 1.1.33
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords:
  - performance
  - test
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638792643567670000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.test-framework.performance@3.1/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.test-framework.performance.git
    revision: 6126c0ee357019ed762100ffeca520029274e869
    path: 
  unityLifecycle:
    version: 3.1.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 1
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Added\n- Added an optional command-line argument
    \"perfTestResults\" to control the target location of performance test run results
    file.\n### Fixed\n- Warmup cycles no longer record GC measurements.\n- Setup
    and Cleanup cycles no longer contribute to GC measurements."}'
  assetStore:
    productId: 
  fingerprint: 92d1d09a72ed696fa23fd76c675b29d211664b50
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 3.1.0
    minimumUnityVersion: 2020.3.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.ext.nunit@2.0.5
  testable: 0
  isDirectDependency: 0
  version: 2.0.5
  source: 2
  resolvedPath: C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.ext.nunit@031a54704bff
  assetPath: Packages/com.unity.ext.nunit
  name: com.unity.ext.nunit
  displayName: Custom NUnit
  author:
    name: 
    email: 
    url: 
  category: Libraries
  type: 
  description: A custom version of NUnit used by Unity Test Framework. Based on NUnit
    version 3.5 and works with all platforms, il2cpp and Mono AOT.
  errors: []
  versions:
    all:
    - 2.0.5
    compatible:
    - 2.0.5
    recommended: 2.0.5
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - nunit
  - unittest
  - test
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ext.nunit@2.0/manual/index.html
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 2.0.5
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
    isDiscoverable: 0
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 031a54704bffe39e6a0324909f8eaa4565bdebf2
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.5
    minimumUnityVersion: 2019.4.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
m_BuiltInPackagesHash: 4a0237811440af8948b7808e46555399b3f7b3c8
