{"root": [{"assemblyName": "Assembly-CSharp", "nameSpace": "", "className": "PersistenceManager", "methodName": "BootstrapRestorationFlag", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp", "nameSpace": "", "className": "BatteryController", "methodName": "InitializeOnStartup", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp", "nameSpace": "", "className": "ItemDatabase", "methodName": "Initialize", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Unity.AppUI", "nameSpace": "Unity.AppUI.Core", "className": "AppUI", "methodName": "RunInitializeInPlayer", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.AppUI", "nameSpace": "Unity.AppUI.Core", "className": "AppUIManagerBehaviour", "methodName": "Create", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.Burst", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Collections", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__1652832624114795843", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Collections", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.InferenceEngine", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__10670624660373352954", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.InferenceEngine", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Rendering.LightTransport.Runtime", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__16164947281921951637", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.Core.Runtime", "nameSpace": "UnityEngine.Experimental.Rendering", "className": "XRSystem", "methodName": "XRSystemInit", "loadTypes": 3, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.Core.Runtime", "nameSpace": "UnityEngine.Rendering", "className": "DebugUpdater", "methodName": "RuntimeInit", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.GPUDriven.Runtime", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__15867191014387474753", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.GPUDriven.Runtime", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.HighDefinition.Runtime", "nameSpace": "UnityEngine.Rendering.HighDefinition", "className": "LightLateUpdate", "methodName": "Init", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.HighDefinition.Runtime", "nameSpace": "UnityEngine.Rendering.HighDefinition", "className": "HDRuntimeReflectionSystem", "methodName": "Initialize", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.HighDefinition.Runtime", "nameSpace": "UnityEngine.Rendering.HighDefinition", "className": "LocalVolumetricFogManager/RegisterLocalVolumetricFogEarlyUpdate", "methodName": "Init", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.HighDefinition.Runtime", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__12546557883459504226", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Serialization", "nameSpace": "Unity.Serialization", "className": "DefaultPropertyBagInitializer", "methodName": "Initialize", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.Serialization", "nameSpace": "Unity.Serialization.Json", "className": "JsonObject", "methodName": "RegisterPropertyBag", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.Serialization", "nameSpace": "Unity.Serialization.Json", "className": "JsonArray", "methodName": "RegisterPropertyBag", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.Serialization", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__14542720655017034712", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.VisualScripting.Core", "nameSpace": "Unity.VisualScripting", "className": "RuntimeVSUsageUtility", "methodName": "RuntimeInitializeOnLoadBeforeSceneLoad", "loadTypes": 1, "isUnityClass": true}]}