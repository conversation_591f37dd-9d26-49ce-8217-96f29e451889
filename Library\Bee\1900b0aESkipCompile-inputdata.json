{"BeeBuildProgramCommon.Data.ConfigurationData": {"Il2CppDir": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp", "UnityLinkerPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp/build/deploy/UnityLinker.exe", "Il2CppPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp/build/deploy/il2cpp.exe", "NetCoreRunPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data\\Tools\\netcorerun\\netcorerun.exe", "DotNetExe": "C:/Unity/Editors/6000.2.0f1/Editor/Data/NetCoreRuntime/dotnet.exe", "EditorContentsPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data", "Packages": [{"Name": "com.unity.2d.enhancers", "ResolvedPath": "Library/PackageCache/com.unity.2d.enhancers@1df0eb7756ab"}, {"Name": "com.unity.2d.sprite", "ResolvedPath": "Library/PackageCache/com.unity.2d.sprite@28296e5d02fb"}, {"Name": "com.unity.ai.assistant", "ResolvedPath": "Library/PackageCache/com.unity.ai.assistant@91c166a13c3b"}, {"Name": "com.unity.ai.generators", "ResolvedPath": "Library/PackageCache/com.unity.ai.generators@49eb4eaccb48"}, {"Name": "com.unity.ai.inference", "ResolvedPath": "Library/PackageCache/com.unity.ai.inference@4ac711cab9a3"}, {"Name": "com.unity.collab-proxy", "ResolvedPath": "Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f"}, {"Name": "com.unity.feature.development", "ResolvedPath": "Library/PackageCache/com.unity.feature.development@767aadbc6eb7"}, {"Name": "com.unity.formats.fbx", "ResolvedPath": "Library/PackageCache/com.unity.formats.fbx@db39de05b0db"}, {"Name": "com.unity.multiplayer.center", "ResolvedPath": "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546"}, {"Name": "com.unity.probuilder", "ResolvedPath": "Library/PackageCache/com.unity.probuilder@c19679f07bae"}, {"Name": "com.unity.progrids", "ResolvedPath": "Library/PackageCache/com.unity.progrids@36b0033bf980"}, {"Name": "com.unity.project-auditor", "ResolvedPath": "Library/PackageCache/com.unity.project-auditor@94c6e4e98816"}, {"Name": "com.unity.recorder", "ResolvedPath": "Library/PackageCache/com.unity.recorder@979a3db2a781"}, {"Name": "com.unity.render-pipelines.high-definition", "ResolvedPath": "Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe"}, {"Name": "com.unity.timeline", "ResolvedPath": "Library/PackageCache/com.unity.timeline@c58b4ee65782"}, {"Name": "com.unity.ugui", "ResolvedPath": "Library/PackageCache/com.unity.ugui@423bc642aff1"}, {"Name": "com.unity.visualscripting", "ResolvedPath": "Library/PackageCache/com.unity.visualscripting@6279e2b7c485"}, {"Name": "com.unity.modules.accessibility", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.accessibility"}, {"Name": "com.unity.modules.ai", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.ai"}, {"Name": "com.unity.modules.androidjni", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.androidjni"}, {"Name": "com.unity.modules.animation", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.animation"}, {"Name": "com.unity.modules.assetbundle", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.assetbundle"}, {"Name": "com.unity.modules.audio", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.audio"}, {"Name": "com.unity.modules.cloth", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.cloth"}, {"Name": "com.unity.modules.director", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.director"}, {"Name": "com.unity.modules.imageconversion", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.imageconversion"}, {"Name": "com.unity.modules.imgui", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.imgui"}, {"Name": "com.unity.modules.jsonserialize", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.jsonserialize"}, {"Name": "com.unity.modules.particlesystem", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.particlesystem"}, {"Name": "com.unity.modules.physics", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.physics"}, {"Name": "com.unity.modules.physics2d", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.physics2d"}, {"Name": "com.unity.modules.screencapture", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.screencapture"}, {"Name": "com.unity.modules.terrain", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.terrain"}, {"Name": "com.unity.modules.terrainphysics", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.terrainphysics"}, {"Name": "com.unity.modules.tilemap", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.tilemap"}, {"Name": "com.unity.modules.ui", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.ui"}, {"Name": "com.unity.modules.uielements", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.uielements"}, {"Name": "com.unity.modules.umbra", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.umbra"}, {"Name": "com.unity.modules.unityanalytics", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.unityanalytics"}, {"Name": "com.unity.modules.unitywebrequest", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequest"}, {"Name": "com.unity.modules.unitywebrequestassetbundle", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequestassetbundle"}, {"Name": "com.unity.modules.unitywebrequestaudio", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequestaudio"}, {"Name": "com.unity.modules.unitywebrequesttexture", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequesttexture"}, {"Name": "com.unity.modules.unitywebrequestwww", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequestwww"}, {"Name": "com.unity.modules.vehicles", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.vehicles"}, {"Name": "com.unity.modules.video", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.video"}, {"Name": "com.unity.modules.vr", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.vr"}, {"Name": "com.unity.modules.wind", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.wind"}, {"Name": "com.unity.modules.xr", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.xr"}, {"Name": "com.unity.modules.subsystems", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.subsystems"}, {"Name": "com.unity.modules.hierarchycore", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.hierarchycore"}, {"Name": "com.unity.render-pipelines.core", "ResolvedPath": "Library/PackageCache/com.unity.render-pipelines.core@bd0e8186c2bc"}, {"Name": "com.unity.shadergraph", "ResolvedPath": "Library/PackageCache/com.unity.shadergraph@f8b69e83dfdd"}, {"Name": "com.unity.visualeffectgraph", "ResolvedPath": "Library/PackageCache/com.unity.visualeffectgraph@c8dcb84572f2"}, {"Name": "com.unity.render-pipelines.high-definition-config", "ResolvedPath": "Library/PackageCache/com.unity.render-pipelines.high-definition-config@f7c893e8c254"}, {"Name": "com.unity.collections", "ResolvedPath": "Library/PackageCache/com.unity.collections@d49facba0036"}, {"Name": "com.unity.bindings.openimageio", "ResolvedPath": "Library/PackageCache/com.unity.bindings.openimageio@3229d2aa5c76"}, {"Name": "com.unity.nuget.mono-cecil", "ResolvedPath": "Library/PackageCache/com.unity.nuget.mono-cecil@d78732e851eb"}, {"Name": "com.unity.nuget.newtonsoft-json", "ResolvedPath": "Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0"}, {"Name": "com.unity.settings-manager", "ResolvedPath": "Library/PackageCache/com.unity.settings-manager@41738c275190"}, {"Name": "com.autodesk.fbx", "ResolvedPath": "Library/PackageCache/com.autodesk.fbx@5797ff6b31c7"}, {"Name": "com.unity.ide.visualstudio", "ResolvedPath": "Library/PackageCache/com.unity.ide.visualstudio@198cdf337d13"}, {"Name": "com.unity.ide.rider", "ResolvedPath": "Library/PackageCache/com.unity.ide.rider@4d374c7eb6db"}, {"Name": "com.unity.editorcoroutines", "ResolvedPath": "Library/PackageCache/com.unity.editorcoroutines@7d48783e7b8c"}, {"Name": "com.unity.performance.profile-analyzer", "ResolvedPath": "Library/PackageCache/com.unity.performance.profile-analyzer@a68e7bc84997"}, {"Name": "com.unity.test-framework", "ResolvedPath": "Library/PackageCache/com.unity.test-framework@a6f5be5f149c"}, {"Name": "com.unity.testtools.codecoverage", "ResolvedPath": "Library/PackageCache/com.unity.testtools.codecoverage@205a02cbcb39"}, {"Name": "com.unity.burst", "ResolvedPath": "Library/PackageCache/com.unity.burst@6aff1dd08a0c"}, {"Name": "com.unity.dt.app-ui", "ResolvedPath": "Library/PackageCache/com.unity.dt.app-ui@7b87c8225c06"}, {"Name": "com.unity.ai.toolkit", "ResolvedPath": "Library/PackageCache/com.unity.ai.toolkit@493fe336b8c1"}, {"Name": "com.unity.mathematics", "ResolvedPath": "Library/PackageCache/com.unity.mathematics@8017b507cc74"}, {"Name": "com.unity.serialization", "ResolvedPath": "Library/PackageCache/com.unity.serialization@582cbf30bbfd"}, {"Name": "com.unity.2d.common", "ResolvedPath": "Library/PackageCache/com.unity.2d.common@dd402daace1b"}, {"Name": "com.unity.searcher", "ResolvedPath": "Library/PackageCache/com.unity.searcher@1e17ce91558d"}, {"Name": "com.unity.rendering.light-transport", "ResolvedPath": "Library/PackageCache/com.unity.rendering.light-transport@2c9279f90d7c"}, {"Name": "com.unity.test-framework.performance", "ResolvedPath": "Library/PackageCache/com.unity.test-framework.performance@92d1d09a72ed"}, {"Name": "com.unity.ext.nunit", "ResolvedPath": "Library/PackageCache/com.unity.ext.nunit@031a54704bff"}], "UnityVersion": "6000.2.0f1", "UnityVersionNumeric": {"Release": 6000, "Major": 2, "Minor": 0}, "Batchmode": false, "EmitDataForBeeWhy": false, "NamedPipeOrUnixSocket": "unity-ilpp-7939e47b98dcc72853c907ef3913884e"}, "ScriptCompilationBuildProgram.Data.ScriptCompilationData": {"Assemblies": [], "DotnetRuntimePath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/NetCoreRuntime", "DotnetRoslynPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/DotNetSdkRoslyn", "MovedFromExtractorPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll", "OutputDirectory": "Library/ScriptAssemblies", "Debug": false, "BuildTarget": "StandaloneWindows64", "Localization": "en-US", "BuildPlayerDataOutput": "Library/BuildPlayerData/Editor", "ExtractRuntimeInitializeOnLoads": false, "EnableDiagnostics": false, "EmitInfoForScriptUpdater": true, "AssembliesToScanForTypeDB": ["Library/ScriptAssemblies/Assembly-CSharp.dll", "Library/ScriptAssemblies/Autodesk.Fbx.BuildTestAssets.dll", "Library/ScriptAssemblies/Autodesk.Fbx.dll", "Library/ScriptAssemblies/BakeryRuntimeAssembly.dll", "Library/ScriptAssemblies/Domain_Reload.dll", "Library/ScriptAssemblies/Tayx.Graphy.dll", "Library/ScriptAssemblies/Unity.2D.Common.Runtime.dll", "Library/ScriptAssemblies/Unity.AI.Animate.Motion.Runtime.dll", "Library/ScriptAssemblies/Unity.AppUI.dll", "Library/ScriptAssemblies/Unity.AppUI.InternalAPIBridge.dll", "Library/ScriptAssemblies/Unity.AppUI.MVVM.dll", "Library/ScriptAssemblies/Unity.AppUI.Navigation.dll", "Library/ScriptAssemblies/Unity.AppUI.Redux.dll", "Library/ScriptAssemblies/Unity.AppUI.Undo.dll", "Library/ScriptAssemblies/Unity.Burst.dll", "Library/ScriptAssemblies/Unity.Collections.dll", "Library/ScriptAssemblies/Unity.Formats.Fbx.Runtime.dll", "Library/ScriptAssemblies/Unity.InferenceEngine.dll", "Library/ScriptAssemblies/Unity.InferenceEngine.iOSBLAS.dll", "Library/ScriptAssemblies/Unity.InferenceEngine.MacBLAS.dll", "Library/ScriptAssemblies/Unity.InternalAPIEngineBridge.001.dll", "Library/ScriptAssemblies/Unity.Mathematics.dll", "Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll", "Library/ScriptAssemblies/Unity.PerformanceTesting.dll", "Library/ScriptAssemblies/Unity.ProBuilder.Csg.dll", "Library/ScriptAssemblies/Unity.ProBuilder.dll", "Library/ScriptAssemblies/Unity.ProBuilder.KdTree.dll", "Library/ScriptAssemblies/Unity.ProBuilder.Poly2Tri.dll", "Library/ScriptAssemblies/Unity.ProBuilder.Stl.dll", "Library/ScriptAssemblies/Unity.ProGrids.dll", "Library/ScriptAssemblies/Unity.Recorder.Base.dll", "Library/ScriptAssemblies/Unity.Recorder.dll", "Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.Core.Samples.Runtime.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.HighDefinition.Runtime.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "Library/ScriptAssemblies/Unity.Serialization.dll", "Library/ScriptAssemblies/Unity.TextMeshPro.dll", "Library/ScriptAssemblies/Unity.Timeline.dll", "Library/ScriptAssemblies/Unity.VisualEffectGraph.Runtime.dll", "Library/ScriptAssemblies/Unity.VisualScripting.Core.dll", "Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll", "Library/ScriptAssemblies/Unity.VisualScripting.State.dll", "Library/ScriptAssemblies/UnityEngine.TestRunner.dll", "Library/ScriptAssemblies/UnityEngine.UI.dll", "Packages/com.unity.ai.assistant/Plugins/CodeAnalysis/Microsoft.CodeAnalysis.CSharp.dll", "Packages/com.unity.ai.assistant/Plugins/CodeAnalysis/Microsoft.CodeAnalysis.dll", "Packages/com.unity.ai.assistant/Plugins/CodeAnalysis/System.Collections.Immutable.dll", "Packages/com.unity.ai.assistant/Plugins/CodeAnalysis/System.Reflection.Metadata.dll", "Packages/com.unity.ai.assistant/Plugins/Markdig/Markdig.dll", "Packages/com.unity.burst/Unity.Burst.CodeGen/Unity.Burst.Cecil.dll", "Packages/com.unity.burst/Unity.Burst.CodeGen/Unity.Burst.Cecil.Mdb.dll", "Packages/com.unity.burst/Unity.Burst.CodeGen/Unity.Burst.Cecil.Pdb.dll", "Packages/com.unity.burst/Unity.Burst.CodeGen/Unity.Burst.Cecil.Rocks.dll", "Packages/com.unity.burst/Unity.Burst.Unsafe.dll", "Packages/com.unity.collections/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll", "Packages/com.unity.collections/Unity.Collections.Tests/System.IO.Hashing/System.IO.Hashing.dll", "Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll", "Packages/com.unity.ext.nunit/net40/unity-custom/nunit.framework.dll", "Packages/com.unity.nuget.mono-cecil/Mono.Cecil.dll", "Packages/com.unity.nuget.mono-cecil/Mono.Cecil.Mdb.dll", "Packages/com.unity.nuget.mono-cecil/Mono.Cecil.Pdb.dll", "Packages/com.unity.nuget.mono-cecil/Mono.Cecil.Rocks.dll", "Packages/com.unity.nuget.newtonsoft-json/Runtime/Newtonsoft.Json.dll", "Packages/com.unity.testtools.codecoverage/lib/ReportGenerator/ReportGeneratorMerged.dll", "Packages/com.unity.visualscripting/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"], "SearchPaths": ["C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Managed\\UnityEngine", "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx", "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard", "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\NetStandard\\EditorExtensions", "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0", "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\NetStandard\\ref\\2.1.0", "Library/ScriptAssemblies", "Packages/com.unity.ai.assistant/Plugins/CodeAnalysis", "Packages/com.unity.ai.assistant/Plugins/Markdig", "Packages/com.unity.burst", "Packages/com.unity.burst/Unity.Burst.CodeGen", "Packages/com.unity.collections/Unity.Collections.LowLevel.ILSupport", "Packages/com.unity.collections/Unity.Collections.Tests/System.IO.Hashing", "Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe", "Packages/com.unity.ext.nunit/net40/unity-custom", "Packages/com.unity.nuget.mono-cecil", "Packages/com.unity.nuget.newtonsoft-json/Runtime", "Packages/com.unity.testtools.codecoverage/lib/ReportGenerator", "Packages/com.unity.visualscripting/Runtime/VisualScripting.Flow/Dependencies/NCalc"]}}