-target:library
-out:"Library/Bee/artifacts/1900b0aP.dag/Unity.InferenceEngine.dll"
-refout:"Library/Bee/artifacts/1900b0aP.dag/Unity.InferenceEngine.ref.dll"
-define:UNITY_6000_2_0
-define:UNITY_6000_2
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_6000_1_OR_NEWER
-define:UNITY_6000_2_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_UNITY_CONSENT
-define:ENABLE_UNITY_CLOUD_IDENTIFIERS
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_STANDALONE_WIN
-define:PLATFORM_STANDALONE
-define:UNITY_STANDALONE_WIN
-define:UNITY_STANDALONE
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_NVIDIA
-define:ENABLE_AMD
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_CLOUD_SERVICES_ENGINE_DIAGNOSTICS
-define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
-define:PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER
-define:PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION
-define:PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:MIRROR
-define:MIRROR_81_OR_NEWER
-define:MIRROR_82_OR_NEWER
-define:MIRROR_83_OR_NEWER
-define:MIRROR_84_OR_NEWER
-define:MIRROR_85_OR_NEWER
-define:MIRROR_86_OR_NEWER
-define:MIRROR_89_OR_NEWER
-define:MIRROR_90_OR_NEWER
-define:MIRROR_93_OR_NEWER
-define:EDGEGAP_PLUGIN_SERVERS
-define:RealtimeCSG
-define:RealtimeCSG_1
-define:RealtimeCSG_1_6
-define:RealtimeCSG_1_6_01
-define:BAKERY_INCLUDED
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/ref/2.1.0/netstandard.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AccessibilityModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AndroidJNIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AnimationModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ARModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AssetBundleModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AudioModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClothModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClusterInputModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClusterRendererModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ContentLoadModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.CoreModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.CrashReportingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.DirectorModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.DSPGraphModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GameCenterModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GridModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.HierarchyCoreModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.HotReloadModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.IdentifiersModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ImageConversionModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.IMGUIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputForUIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputLegacyModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InsightsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.JSONSerializeModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.LocalizationModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.MarshallingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.MultiplayerModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ParticleSystemModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PerformanceReportingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.Physics2DModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PhysicsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PropertiesModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ScreenCaptureModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SharedInternalsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SpriteMaskModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SpriteShapeModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.StreamingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SubstanceModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SubsystemsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TerrainModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TerrainPhysicsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextCoreFontEngineModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextCoreTextEngineModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextRenderingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TilemapModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TLSModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UIElementsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UmbraModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityAnalyticsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityConnectModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityConsentModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityCurlModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityTestProtocolModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VehiclesModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VFXModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VideoModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VirtualTexturingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VRModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.WindModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.XRModule.dll"
-r:"Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.IO.Hashing/System.IO.Hashing.dll"
-r:"Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/AOT/Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/Bee/artifacts/1900b0aP.dag/Unity.Burst.ref.dll"
-r:"Library/Bee/artifacts/1900b0aP.dag/Unity.Collections.ref.dll"
-r:"Library/Bee/artifacts/1900b0aP.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/1900b0aP.dag/UnityEngine.UI.ref.dll"
-analyzer:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
-analyzer:"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/SourceGenerator/SourceGenerators.dll"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/AssemblyInfo.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/BackendFactory.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/CPU/BurstCPU.Backend.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/CPU/BurstCPU.Backend.gen.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/CPU/BurstCPU.Helper.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/CPU/BurstCPU.Jobs.Broadcast.gen.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/CPU/BurstCPU.Jobs.ConvA.gen.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/CPU/BurstCPU.Jobs.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/CPU/BurstCPU.Jobs.Dimension.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/CPU/BurstCPU.Jobs.Einsum.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/CPU/BurstCPU.Jobs.GenericA.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/CPU/BurstCPU.Jobs.ImageBased.gen.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/CPU/BurstCPU.Jobs.IndexingOpsA.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/CPU/BurstCPU.Jobs.Logical.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/CPU/BurstCPU.Jobs.Normalization.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/CPU/BurstCPU.Jobs.Other.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/CPU/BurstCPU.Jobs.Pad.gen.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/CPU/BurstCPU.Jobs.PointwiseUnary.gen.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/CPU/BurstCPU.Jobs.Pool.gen.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/CPU/BurstCPU.Jobs.Reduction.gen.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/CPU/BurstCPU.Jobs.RNN.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/CPU/BurstCPU.MatMul.gen.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/CPU/BurstCPUJobSystem.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/CPU/BurstTensorData.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/CPU/NativeTensorArray.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/CPU/ReferenceCPU.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/CPU/ReferenceCPU.Einsum.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/CPUFallbackCalculator.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/GfxDevice/GfxDeviceBackend.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/GPUCompute/CommandBufferHelper.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/GPUCompute/Compute.Backend.gen.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/GPUCompute/ComputeHelper.ComputeFunctions.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/GPUCompute/ComputeHelper.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/GPUCompute/ComputeHelper.PropertyID.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/GPUCompute/ComputeInfo.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/GPUCompute/ComputeTensorData.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/GPUCompute/GPUCompute.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/GPUPixel/GPUPixel.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/GPUPixel/GPUPixelBurstJobs.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/GPUPixel/PixelShaderSingleton.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/GPUPixel/TextureTensorData.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/IBackend.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/LayerFusingHelper.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/ModelOptimizer.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/ModelStorage.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/ModelValidator.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/Ops.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/OpsUtils.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/TensorAllocator.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/TensorClassPool.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/TensorDataPool.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/TensorPool.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Backends/Worker.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/BurstJobsCastTensor.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/BurstJobsQuantizeTensor.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Compiler/Analyser/GraphLogicAnalysis.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Compiler/Analyser/MemoryFootprintAnalysis.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Compiler/Analyser/PartialInferenceAnalysis.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Compiler/Passes/ConcatenateTransposesPass.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Compiler/Passes/ContractSubExpressionsPass.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Compiler/Passes/ContractToSimplerLayerPass.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Compiler/Passes/EinsumToMatMulPass.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Compiler/Passes/FuseActivationsPass.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Compiler/Passes/FuseConstantsPass.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Compiler/Passes/FuseDensePass.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Compiler/Passes/FuseLinearLayersPass.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Compiler/Passes/IModelPass.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Compiler/Passes/PassesUtils.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Compiler/Passes/RemoveDuplicatesPass.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Compiler/Passes/RemoveNoOpsPass.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Compiler/Passes/RemoveUnusedPass.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Compiler/Passes/RoundDenormalWeightsPass.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Compiler/Passes/SimplifyReshapeInputPass.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Compiler/Passes/ValidatePasses.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Compiler/Validation/IValidationPass.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Compiler/Validation/ValidateShapeInference.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Converters/TextureConverter.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Converters/TextureTransform.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Functional/Functional.Extensions.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Functional/Functional.Layer.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Functional/Functional.Math.Comparison.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Functional/Functional.Math.LinearAlgebra.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Functional/Functional.Math.Other.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Functional/Functional.Math.Pointwise.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Functional/Functional.Math.Reduction.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Functional/Functional.Model.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Functional/Functional.NN.Convolution.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Functional/Functional.NN.NonLinearActivation.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Functional/Functional.NN.Pooling.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Functional/Functional.NN.Sparse.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Functional/Functional.NN.Vision.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Functional/Functional.Tensor.Constant.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Functional/Functional.Tensor.Creation.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Functional/Functional.Tensor.Random.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Functional/Functional.Tensor.Transformation.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Functional/Functional.Tensor.Type.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Functional/Functional.Vision.Detection.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Functional/FunctionalGraph.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Functional/FunctionalTensor.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Functional/FunctionalTensor.Indexer.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Functional/FunctionalTensor.Operator.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Functional/Node.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/InferenceEngine.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Internals/Debug.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Internals/ModelAsset.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Internals/ModelAssetData.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Internals/ModelAssetWeightsData.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Layers/Constant.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Layers/Layer.Activation.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Layers/Layer.ActivationNonLinear.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Layers/Layer.Convolution.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Layers/Layer.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Layers/Layer.Dimension.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Layers/Layer.Generator.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Layers/Layer.Indexing.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Layers/Layer.Logical.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Layers/Layer.Math.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Layers/Layer.Normalization.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Layers/Layer.ObjectDetection.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Layers/Layer.Pooling.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Layers/Layer.Quantization.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Layers/Layer.Random.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Layers/Layer.Recurrent.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Layers/Layer.Reduction.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Layers/Layer.Transformation.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Layers/Layer.Trigonometric.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Logger.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Model.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/PluginInterfaces.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/ProfilerMarkers.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Quantization/ModelQuantizer.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Quantization/QuantizeConstantsPass.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Random.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/FlatBuffers/ByteBuffer.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/FlatBuffers/ByteBufferUtil.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/FlatBuffers/FlatBufferBuilder.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/FlatBuffers/FlatBufferConstants.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/FlatBuffers/FlatBufferVerify.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/FlatBuffers/IFlatbufferObject.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/FlatBuffers/Offset.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/FlatBuffers/Struct.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/FlatBuffers/Table.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/ModelLoader.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/ModelUpgrader.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/ModelWriter.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/BackendPartitioning.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/BackendType.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/Bool.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/BoolList.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/Buffer.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/Byte.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/Chain.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/DataSegment.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/EDim.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/EValue.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/ExecutionPlan.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/Float.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/FloatList.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/Instruction.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/InstructionArguments.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/Int.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/IntList.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/KernelCall.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/KernelTypes.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/Null.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/Operator.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/Program.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/ScalarType.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/Short.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/String.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/SymbolicDim.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/Tensor.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Serialization/SentisFlatBuffer/TensorShapeDynamism.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/ShapeInference/DynamicTensorDim.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/ShapeInference/DynamicTensorShape.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/ShapeInference/EinsumHelper.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/ShapeInference/PartialInferenceContext.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/ShapeInference/PartialTensor.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/ShapeInference/PartialTensorElement.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/ShapeInference/ShapeInferenceHelper.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/ShapeInference/TensorShapeInferenceHelper.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/SourceGenerator/SourceGeneratorAttributes.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/Tensor.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/TensorData.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/TensorGeneric.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/TensorIndex.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/TensorIterator.cs"
"Library/PackageCache/com.unity.ai.inference@4ac711cab9a3/Runtime/Core/TensorShape.cs"
-langversion:9.0
/unsafe+
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0
/additionalfile:"Library/Bee/artifacts/1900b0aP.dag/Unity.InferenceEngine.UnityAdditionalFile.txt"