{"BeeBuildProgramCommon.Data.ConfigurationData": {"Il2CppDir": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp", "UnityLinkerPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp/build/deploy/UnityLinker.exe", "Il2CppPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp/build/deploy/il2cpp.exe", "NetCoreRunPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data\\Tools\\netcorerun\\netcorerun.exe", "DotNetExe": "C:/Unity/Editors/6000.2.0f1/Editor/Data/NetCoreRuntime/dotnet.exe", "EditorContentsPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data", "Packages": [{"Name": "com.unity.2d.enhancers", "ResolvedPath": "Library/PackageCache/com.unity.2d.enhancers@1df0eb7756ab"}, {"Name": "com.unity.2d.sprite", "ResolvedPath": "Library/PackageCache/com.unity.2d.sprite@28296e5d02fb"}, {"Name": "com.unity.ai.assistant", "ResolvedPath": "Library/PackageCache/com.unity.ai.assistant@91c166a13c3b"}, {"Name": "com.unity.ai.generators", "ResolvedPath": "Library/PackageCache/com.unity.ai.generators@49eb4eaccb48"}, {"Name": "com.unity.ai.inference", "ResolvedPath": "Library/PackageCache/com.unity.ai.inference@4ac711cab9a3"}, {"Name": "com.unity.collab-proxy", "ResolvedPath": "Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f"}, {"Name": "com.unity.feature.development", "ResolvedPath": "Library/PackageCache/com.unity.feature.development@767aadbc6eb7"}, {"Name": "com.unity.formats.fbx", "ResolvedPath": "Library/PackageCache/com.unity.formats.fbx@db39de05b0db"}, {"Name": "com.unity.multiplayer.center", "ResolvedPath": "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546"}, {"Name": "com.unity.probuilder", "ResolvedPath": "Library/PackageCache/com.unity.probuilder@c19679f07bae"}, {"Name": "com.unity.progrids", "ResolvedPath": "Library/PackageCache/com.unity.progrids@36b0033bf980"}, {"Name": "com.unity.project-auditor", "ResolvedPath": "Library/PackageCache/com.unity.project-auditor@94c6e4e98816"}, {"Name": "com.unity.recorder", "ResolvedPath": "Library/PackageCache/com.unity.recorder@979a3db2a781"}, {"Name": "com.unity.render-pipelines.high-definition", "ResolvedPath": "Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe"}, {"Name": "com.unity.timeline", "ResolvedPath": "Library/PackageCache/com.unity.timeline@c58b4ee65782"}, {"Name": "com.unity.ugui", "ResolvedPath": "Library/PackageCache/com.unity.ugui@423bc642aff1"}, {"Name": "com.unity.visualscripting", "ResolvedPath": "Library/PackageCache/com.unity.visualscripting@6279e2b7c485"}, {"Name": "com.unity.modules.accessibility", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.accessibility"}, {"Name": "com.unity.modules.ai", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.ai"}, {"Name": "com.unity.modules.androidjni", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.androidjni"}, {"Name": "com.unity.modules.animation", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.animation"}, {"Name": "com.unity.modules.assetbundle", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.assetbundle"}, {"Name": "com.unity.modules.audio", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.audio"}, {"Name": "com.unity.modules.cloth", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.cloth"}, {"Name": "com.unity.modules.director", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.director"}, {"Name": "com.unity.modules.imageconversion", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.imageconversion"}, {"Name": "com.unity.modules.imgui", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.imgui"}, {"Name": "com.unity.modules.jsonserialize", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.jsonserialize"}, {"Name": "com.unity.modules.particlesystem", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.particlesystem"}, {"Name": "com.unity.modules.physics", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.physics"}, {"Name": "com.unity.modules.physics2d", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.physics2d"}, {"Name": "com.unity.modules.screencapture", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.screencapture"}, {"Name": "com.unity.modules.terrain", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.terrain"}, {"Name": "com.unity.modules.terrainphysics", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.terrainphysics"}, {"Name": "com.unity.modules.tilemap", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.tilemap"}, {"Name": "com.unity.modules.ui", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.ui"}, {"Name": "com.unity.modules.uielements", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.uielements"}, {"Name": "com.unity.modules.umbra", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.umbra"}, {"Name": "com.unity.modules.unityanalytics", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.unityanalytics"}, {"Name": "com.unity.modules.unitywebrequest", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequest"}, {"Name": "com.unity.modules.unitywebrequestassetbundle", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequestassetbundle"}, {"Name": "com.unity.modules.unitywebrequestaudio", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequestaudio"}, {"Name": "com.unity.modules.unitywebrequesttexture", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequesttexture"}, {"Name": "com.unity.modules.unitywebrequestwww", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequestwww"}, {"Name": "com.unity.modules.vehicles", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.vehicles"}, {"Name": "com.unity.modules.video", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.video"}, {"Name": "com.unity.modules.vr", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.vr"}, {"Name": "com.unity.modules.wind", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.wind"}, {"Name": "com.unity.modules.xr", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.xr"}, {"Name": "com.unity.modules.subsystems", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.subsystems"}, {"Name": "com.unity.modules.hierarchycore", "ResolvedPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.modules.hierarchycore"}, {"Name": "com.unity.render-pipelines.core", "ResolvedPath": "Library/PackageCache/com.unity.render-pipelines.core@bd0e8186c2bc"}, {"Name": "com.unity.shadergraph", "ResolvedPath": "Library/PackageCache/com.unity.shadergraph@f8b69e83dfdd"}, {"Name": "com.unity.visualeffectgraph", "ResolvedPath": "Library/PackageCache/com.unity.visualeffectgraph@c8dcb84572f2"}, {"Name": "com.unity.render-pipelines.high-definition-config", "ResolvedPath": "Library/PackageCache/com.unity.render-pipelines.high-definition-config@f7c893e8c254"}, {"Name": "com.unity.collections", "ResolvedPath": "Library/PackageCache/com.unity.collections@d49facba0036"}, {"Name": "com.unity.bindings.openimageio", "ResolvedPath": "Library/PackageCache/com.unity.bindings.openimageio@3229d2aa5c76"}, {"Name": "com.unity.nuget.mono-cecil", "ResolvedPath": "Library/PackageCache/com.unity.nuget.mono-cecil@d78732e851eb"}, {"Name": "com.unity.nuget.newtonsoft-json", "ResolvedPath": "Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0"}, {"Name": "com.unity.settings-manager", "ResolvedPath": "Library/PackageCache/com.unity.settings-manager@41738c275190"}, {"Name": "com.autodesk.fbx", "ResolvedPath": "Library/PackageCache/com.autodesk.fbx@5797ff6b31c7"}, {"Name": "com.unity.ide.visualstudio", "ResolvedPath": "Library/PackageCache/com.unity.ide.visualstudio@198cdf337d13"}, {"Name": "com.unity.ide.rider", "ResolvedPath": "Library/PackageCache/com.unity.ide.rider@4d374c7eb6db"}, {"Name": "com.unity.editorcoroutines", "ResolvedPath": "Library/PackageCache/com.unity.editorcoroutines@7d48783e7b8c"}, {"Name": "com.unity.performance.profile-analyzer", "ResolvedPath": "Library/PackageCache/com.unity.performance.profile-analyzer@a68e7bc84997"}, {"Name": "com.unity.test-framework", "ResolvedPath": "Library/PackageCache/com.unity.test-framework@a6f5be5f149c"}, {"Name": "com.unity.testtools.codecoverage", "ResolvedPath": "Library/PackageCache/com.unity.testtools.codecoverage@205a02cbcb39"}, {"Name": "com.unity.burst", "ResolvedPath": "Library/PackageCache/com.unity.burst@6aff1dd08a0c"}, {"Name": "com.unity.dt.app-ui", "ResolvedPath": "Library/PackageCache/com.unity.dt.app-ui@7b87c8225c06"}, {"Name": "com.unity.ai.toolkit", "ResolvedPath": "Library/PackageCache/com.unity.ai.toolkit@493fe336b8c1"}, {"Name": "com.unity.mathematics", "ResolvedPath": "Library/PackageCache/com.unity.mathematics@8017b507cc74"}, {"Name": "com.unity.serialization", "ResolvedPath": "Library/PackageCache/com.unity.serialization@582cbf30bbfd"}, {"Name": "com.unity.2d.common", "ResolvedPath": "Library/PackageCache/com.unity.2d.common@dd402daace1b"}, {"Name": "com.unity.searcher", "ResolvedPath": "Library/PackageCache/com.unity.searcher@1e17ce91558d"}, {"Name": "com.unity.rendering.light-transport", "ResolvedPath": "Library/PackageCache/com.unity.rendering.light-transport@2c9279f90d7c"}, {"Name": "com.unity.test-framework.performance", "ResolvedPath": "Library/PackageCache/com.unity.test-framework.performance@92d1d09a72ed"}, {"Name": "com.unity.ext.nunit", "ResolvedPath": "Library/PackageCache/com.unity.ext.nunit@031a54704bff"}], "UnityVersion": "6000.2.0f1", "UnityVersionNumeric": {"Release": 6000, "Major": 2, "Minor": 0}, "Batchmode": false, "EmitDataForBeeWhy": false, "NamedPipeOrUnixSocket": "unity-ilpp-7939e47b98dcc72853c907ef3913884e"}, "PlayerBuildProgramLibrary.Data.PlayerBuildConfig": {"DestinationPath": "C:/Unity/Builds/NLAME/BLAME.exe", "StagingArea": "Temp/StagingArea", "DataFolder": "Library/PlayerDataCache/Win642/Data", "CompanyName": "Gridbased", "ProductName": "Persistent Object", "PlayerPackage": "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport", "ApplicationIdentifier": "com.Unity-Technologies.com.unity.template.hdrp-blank", "Architecture": "x64", "ScriptingBackend": "Mono", "NoGUID": false, "InstallIntoBuildsFolder": false, "GenerateIdeProject": false, "Development": false, "UseNewInputSystem": true, "GenerateNativePluginsForAssembliesSettings": {"HasCallback": true, "DisplayName": "<PERSON> <PERSON><PERSON><PERSON>", "AdditionalInputFiles": ["ProjectSettings/BurstAotSettings_StandaloneWindows.json", "Library/BurstCache/AotSettings_StandaloneWindows.hash", "C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.burst@6aff1dd08a0c/.Runtime\\bcl.exe"]}, "Services": {"EnableUnityConnect": false, "EnablePerformanceReporting": false, "EnableAnalytics": false, "EnableCrashReporting": false, "EnableInsights": false}, "ManagedAssemblies": ["C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AIModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AMDModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ARModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AccessibilityModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AndroidJNIModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AnimationModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AssetBundleModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AudioModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClothModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClusterInputModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClusterRendererModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ContentLoadModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.CoreModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.CrashReportingModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.DSPGraphModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.DirectorModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GIModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GameCenterModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GraphicsStateCollectionSerializerModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GridModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.HierarchyCoreModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.HotReloadModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.IMGUIModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.IdentifiersModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ImageConversionModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputForUIModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputLegacyModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InsightsModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.JSONSerializeModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.LocalizationModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.MarshallingModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.MultiplayerModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.NVIDIAModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ParticleSystemModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PerformanceReportingModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.Physics2DModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PhysicsModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PropertiesModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ScreenCaptureModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ShaderVariantAnalyticsModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SharedInternalsModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SpriteMaskModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SpriteShapeModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.StreamingModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SubstanceModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SubsystemsModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TLSModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TerrainModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TerrainPhysicsModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextCoreFontEngineModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextCoreTextEngineModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextRenderingModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TilemapModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UIElementsModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UIModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UmbraModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityAnalyticsCommonModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityAnalyticsModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityConnectModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityConsentModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityCurlModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityTestProtocolModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestAudioModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestTextureModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestWWWModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VFXModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VRModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VehiclesModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VideoModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VirtualTexturingModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.WindModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.XRModule.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Autodesk.Fbx.BuildTestAssets.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Autodesk.Fbx.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/BakeryRuntimeAssembly.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Domain_Reload.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Tayx.Graphy.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.2D.Common.Runtime.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.InternalAPIBridge.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.MVVM.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.Navigation.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.Redux.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.Undo.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Burst.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Collections.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Formats.Fbx.Runtime.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.InferenceEngine.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.InternalAPIEngineBridge.001.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Csg.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.KdTree.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Poly2Tri.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Stl.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProGrids.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Recorder.Base.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Recorder.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Samples.Runtime.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Runtime.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Serialization.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.VisualEffectGraph.Runtime.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll", "C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll", "C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll", "C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.IO.Hashing/System.IO.Hashing.dll", "C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll", "C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.burst@6aff1dd08a0c/Unity.Burst.Unsafe.dll", "C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/AOT/Newtonsoft.Json.dll", "C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Accessibility.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Commons.Xml.Relaxng.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/CustomMarshalers.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/Microsoft.Win32.Primitives.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/Microsoft.Win32.Registry.AccessControl.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/Microsoft.Win32.Registry.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.AppContext.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Buffers.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Collections.Concurrent.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Collections.NonGeneric.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Collections.Specialized.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Collections.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ComponentModel.Annotations.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ComponentModel.EventBasedAsync.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ComponentModel.Primitives.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ComponentModel.TypeConverter.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ComponentModel.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Console.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Data.Common.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Data.SqlClient.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.Contracts.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.Debug.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.FileVersionInfo.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.Process.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.StackTrace.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.TextWriterTraceListener.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.Tools.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.TraceEvent.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.TraceSource.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.Tracing.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Drawing.Primitives.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Dynamic.Runtime.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Globalization.Calendars.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Globalization.Extensions.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Globalization.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.Compression.ZipFile.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.FileSystem.AccessControl.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.FileSystem.DriveInfo.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.FileSystem.Primitives.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.FileSystem.Watcher.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.FileSystem.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.IsolatedStorage.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.MemoryMappedFiles.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.Pipes.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.UnmanagedMemoryStream.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Linq.Expressions.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Linq.Parallel.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Linq.Queryable.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Linq.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Memory.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.AuthenticationManager.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Cache.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Http.Rtc.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.HttpListener.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Mail.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.NameResolution.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.NetworkInformation.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Ping.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Primitives.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Requests.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Security.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.ServicePoint.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Sockets.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Utilities.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.WebHeaderCollection.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.WebSockets.Client.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.WebSockets.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ObjectModel.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.DispatchProxy.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.Emit.ILGeneration.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.Emit.Lightweight.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.Emit.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.Extensions.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.Primitives.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.TypeExtensions.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Resources.Reader.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Resources.ReaderWriter.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Resources.ResourceManager.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Resources.Writer.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.CompilerServices.VisualC.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Extensions.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Handles.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.InteropServices.RuntimeInformation.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.InteropServices.WindowsRuntime.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.InteropServices.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Loader.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Numerics.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Serialization.Formatters.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Serialization.Json.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Serialization.Primitives.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Serialization.Xml.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.AccessControl.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Claims.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Algorithms.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Cng.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Csp.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.DeriveBytes.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Encoding.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Encryption.Aes.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Encryption.ECDiffieHellman.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Encryption.ECDsa.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Encryption.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Hashing.Algorithms.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Hashing.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.OpenSsl.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Pkcs.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Primitives.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.ProtectedData.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.RSA.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.RandomNumberGenerator.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.X509Certificates.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Principal.Windows.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Principal.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.SecureString.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ServiceModel.Duplex.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ServiceModel.Http.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ServiceModel.NetTcp.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ServiceModel.Primitives.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ServiceModel.Security.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ServiceProcess.ServiceController.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Text.Encoding.CodePages.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Text.Encoding.Extensions.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Text.Encoding.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Text.RegularExpressions.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.AccessControl.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.Overlapped.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.Tasks.Extensions.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.Tasks.Parallel.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.Tasks.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.Thread.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.ThreadPool.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.Timer.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ValueTuple.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.ReaderWriter.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.XDocument.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.XPath.XDocument.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.XPath.XmlDocument.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.XPath.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.XmlDocument.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.XmlSerializer.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.Xsl.Primitives.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/netstandard.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/I18N.CJK.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/I18N.MidEast.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/I18N.Other.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/I18N.Rare.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/I18N.West.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/I18N.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/IBM.Data.DB2.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/ICSharpCode.SharpZipLib.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.Build.Engine.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.Build.Framework.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.Build.Tasks.v4.0.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.Build.Utilities.v4.0.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.Build.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.CSharp.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.VisualC.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.Web.Infrastructure.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Btls.Interface.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.C5.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.CSharp.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Cairo.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.CompilerServices.SymbolWriter.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Data.Sqlite.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Data.Tds.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Http.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Management.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Messaging.RabbitMQ.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Messaging.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Options.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Parallel.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Posix.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Profiler.Log.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Security.Win32.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Security.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Simd.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Tasklets.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.WebBrowser.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.XBuild.Tasks.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Novell.Directory.Ldap.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/PEAPI.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/RabbitMQ.Client.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/SMDiagnostics.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ComponentModel.Composition.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ComponentModel.DataAnnotations.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Configuration.Install.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Configuration.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Core.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.DataSetExtensions.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.Entity.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.Linq.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.OracleClient.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.Services.Client.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.Services.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Deployment.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Design.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.DirectoryServices.Protocols.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.DirectoryServices.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Drawing.Design.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Drawing.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Dynamic.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.EnterpriseServices.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.IO.Compression.FileSystem.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.IO.Compression.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.IdentityModel.Selectors.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.IdentityModel.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Json.Microsoft.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Json.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Management.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Messaging.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Net.Http.Formatting.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Net.Http.WebRequest.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Net.Http.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Net.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Numerics.Vectors.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Numerics.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Core.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Debugger.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Experimental.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Interfaces.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Linq.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Observable.Aliases.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.PlatformServices.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Providers.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Runtime.Remoting.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Windows.Forms.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Windows.Threading.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reflection.Context.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Runtime.Caching.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Runtime.DurableInstancing.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Runtime.Remoting.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Runtime.Serialization.Formatters.Soap.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Runtime.Serialization.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Security.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.Activation.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.Discovery.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.Internals.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.Routing.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.Web.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceProcess.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Threading.Tasks.Dataflow.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Transactions.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Abstractions.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.ApplicationServices.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.DynamicData.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Extensions.Design.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Extensions.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Http.SelfHost.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Http.WebHost.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Http.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Mobile.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Mvc.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Razor.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.RegularExpressions.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Routing.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Services.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.WebPages.Deployment.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.WebPages.Razor.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.WebPages.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Windows.Forms.DataVisualization.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Windows.Forms.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Windows.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Workflow.Activities.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Workflow.ComponentModel.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Workflow.Runtime.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Xaml.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Xml.Linq.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Xml.Serialization.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Xml.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/SystemWebTestShim.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/WebMatrix.Data.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/WindowsBase.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/cscompmgd.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/monodoc.dll", "C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/mscorlib.dll"], "StreamingAssetsFiles": []}, "PlayerBuildProgramLibrary.Data.PluginsData": {"Plugins": [{"AssetPath": "Packages/com.unity.dt.app-ui/Runtime/Core/Platform/Windows/Plugins/arm64/AppUINativePlugin.dll", "DestinationPath": "ARM64\\AppUINativePlugin.dll", "AddToEmbeddedBinaries": false}, {"AssetPath": "Packages/com.unity.dt.app-ui/Runtime/Core/Platform/Windows/Plugins/x64/AppUINativePlugin.dll", "DestinationPath": "x86_64\\AppUINativePlugin.dll", "AddToEmbeddedBinaries": false}]}, "PlayerBuildProgramLibrary.Data.LinkerConfig": {"LinkXmlFiles": ["C:/Unity/BLAME/BLAME/Library/Bee/artifacts/UnityLinkerInputs/MethodsToPreserve.xml", "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/UnityLinkerInputs/TypesInScenes.xml", "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/UnityLinkerInputs/SerializedTypes.xml"], "AssembliesToProcess": ["UnityEngine.AIModule.dll", "UnityEngine.AMDModule.dll", "UnityEngine.ARModule.dll", "UnityEngine.AccessibilityModule.dll", "UnityEngine.AndroidJNIModule.dll", "UnityEngine.AnimationModule.dll", "UnityEngine.AssetBundleModule.dll", "UnityEngine.AudioModule.dll", "UnityEngine.ClothModule.dll", "UnityEngine.ClusterInputModule.dll", "UnityEngine.ClusterRendererModule.dll", "UnityEngine.ContentLoadModule.dll", "UnityEngine.CoreModule.dll", "UnityEngine.CrashReportingModule.dll", "UnityEngine.DSPGraphModule.dll", "UnityEngine.DirectorModule.dll", "UnityEngine.GIModule.dll", "UnityEngine.GameCenterModule.dll", "UnityEngine.GraphicsStateCollectionSerializerModule.dll", "UnityEngine.GridModule.dll", "UnityEngine.HierarchyCoreModule.dll", "UnityEngine.HotReloadModule.dll", "UnityEngine.IMGUIModule.dll", "UnityEngine.IdentifiersModule.dll", "UnityEngine.ImageConversionModule.dll", "UnityEngine.InputForUIModule.dll", "UnityEngine.InputLegacyModule.dll", "UnityEngine.InputModule.dll", "UnityEngine.InsightsModule.dll", "UnityEngine.JSONSerializeModule.dll", "UnityEngine.LocalizationModule.dll", "UnityEngine.MarshallingModule.dll", "UnityEngine.MultiplayerModule.dll", "UnityEngine.NVIDIAModule.dll", "UnityEngine.ParticleSystemModule.dll", "UnityEngine.PerformanceReportingModule.dll", "UnityEngine.Physics2DModule.dll", "UnityEngine.PhysicsModule.dll", "UnityEngine.PropertiesModule.dll", "UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "UnityEngine.ScreenCaptureModule.dll", "UnityEngine.ShaderVariantAnalyticsModule.dll", "UnityEngine.SharedInternalsModule.dll", "UnityEngine.SpriteMaskModule.dll", "UnityEngine.SpriteShapeModule.dll", "UnityEngine.StreamingModule.dll", "UnityEngine.SubstanceModule.dll", "UnityEngine.SubsystemsModule.dll", "UnityEngine.TLSModule.dll", "UnityEngine.TerrainModule.dll", "UnityEngine.TerrainPhysicsModule.dll", "UnityEngine.TextCoreFontEngineModule.dll", "UnityEngine.TextCoreTextEngineModule.dll", "UnityEngine.TextRenderingModule.dll", "UnityEngine.TilemapModule.dll", "UnityEngine.UIElementsModule.dll", "UnityEngine.UIModule.dll", "UnityEngine.UmbraModule.dll", "UnityEngine.UnityAnalyticsCommonModule.dll", "UnityEngine.UnityAnalyticsModule.dll", "UnityEngine.UnityConnectModule.dll", "UnityEngine.UnityConsentModule.dll", "UnityEngine.UnityCurlModule.dll", "UnityEngine.UnityTestProtocolModule.dll", "UnityEngine.UnityWebRequestAssetBundleModule.dll", "UnityEngine.UnityWebRequestAudioModule.dll", "UnityEngine.UnityWebRequestModule.dll", "UnityEngine.UnityWebRequestTextureModule.dll", "UnityEngine.UnityWebRequestWWWModule.dll", "UnityEngine.VFXModule.dll", "UnityEngine.VRModule.dll", "UnityEngine.VehiclesModule.dll", "UnityEngine.VideoModule.dll", "UnityEngine.VirtualTexturingModule.dll", "UnityEngine.WindModule.dll", "UnityEngine.XRModule.dll", "UnityEngine.dll", "Assembly-CSharp.dll", "Autodesk.Fbx.BuildTestAssets.dll", "Autodesk.Fbx.dll", "BakeryRuntimeAssembly.dll", "Domain_Reload.dll", "Tayx.Graphy.dll", "Unity.2D.Common.Runtime.dll", "Unity.AppUI.dll", "Unity.AppUI.InternalAPIBridge.dll", "Unity.AppUI.MVVM.dll", "Unity.AppUI.Navigation.dll", "Unity.AppUI.Redux.dll", "Unity.AppUI.Undo.dll", "Unity.Burst.dll", "Unity.Collections.dll", "Unity.Formats.Fbx.Runtime.dll", "Unity.InferenceEngine.dll", "Unity.InternalAPIEngineBridge.001.dll", "Unity.Mathematics.dll", "Unity.Multiplayer.Center.Common.dll", "Unity.ProBuilder.Csg.dll", "Unity.ProBuilder.dll", "Unity.ProBuilder.KdTree.dll", "Unity.ProBuilder.Poly2Tri.dll", "Unity.ProBuilder.Stl.dll", "Unity.ProGrids.dll", "Unity.Recorder.Base.dll", "Unity.Recorder.dll", "Unity.Rendering.LightTransport.Runtime.dll", "Unity.RenderPipelines.Core.Runtime.dll", "Unity.RenderPipelines.Core.Runtime.Shared.dll", "Unity.RenderPipelines.Core.Samples.Runtime.dll", "Unity.RenderPipelines.Core.ShaderLibrary.dll", "Unity.RenderPipelines.GPUDriven.Runtime.dll", "Unity.RenderPipelines.HighDefinition.Config.Runtime.dll", "Unity.RenderPipelines.HighDefinition.Runtime.dll", "Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll", "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "Unity.Serialization.dll", "Unity.TextMeshPro.dll", "Unity.Timeline.dll", "Unity.VisualEffectGraph.Runtime.dll", "Unity.VisualScripting.Core.dll", "Unity.VisualScripting.Flow.dll", "Unity.VisualScripting.State.dll", "UnityEngine.UI.dll", "Unity.Collections.LowLevel.ILSupport.dll", "System.IO.Hashing.dll", "Unity.VisualScripting.Antlr3.Runtime.dll", "Unity.Burst.Unsafe.dll", "Newtonsoft.Json.dll", "System.Runtime.CompilerServices.Unsafe.dll"], "EditorToLinkerData": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/UnityLinkerInputs/EditorToUnityLinkerData.json", "Runtime": "mono", "Profile": "unityaot-win32", "Ruleset": "Copy", "ModulesAssetPath": "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/modules.asset", "AdditionalArgs": [], "AllowDebugging": false, "PerformEngineStripping": true}, "WinPlayerBuildProgram.Data.WinPlayerBuildConfig": {"VariationFolder": "C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono", "ServerPlayer": false, "CopyPdbFiles": false, "WindowsBuildAndRunRemoteDeploy": false, "AssetImages": [], "ManifestTemplate": "", "CertificatePath": "", "CopyD3D12Files": true}}