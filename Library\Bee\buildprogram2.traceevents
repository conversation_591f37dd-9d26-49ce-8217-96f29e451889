{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "netcorerun.dll" } },
{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "-1" } },
{ "pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 35942, "tid": 1, "ts": 1755289760491712, "dur": 383601, "ph": "X", "name": "BuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1755289760492893, "dur": 52065, "ph": "X", "name": "BuildProgramContextConstructor", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1755289760555104, "dur": 272626, "ph": "X", "name": "RunBuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1755289760556027, "dur": 216200, "ph": "X", "name": "PlayerBuildProgramBase.SetupPlayerBuild", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1755289760556600, "dur": 80874, "ph": "X", "name": "SetupDataFiles", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1755289760637920, "dur": 1471, "ph": "X", "name": "SetupCopyPlugins", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1755289760642160, "dur": 127217, "ph": "X", "name": "SetupUnityLinker", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1755289760835858, "dur": 3506, "ph": "X", "name": "OutputData.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1755289760839366, "dur": 35934, "ph": "X", "name": "Backend.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1755289760840637, "dur": 26735, "ph": "X", "name": "JsonToString", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1755289760883041, "dur": 1482, "ph": "X", "name": "", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1755289760882586, "dur": 2212, "ph": "X", "name": "Write chrome-trace events", "args": {} },
