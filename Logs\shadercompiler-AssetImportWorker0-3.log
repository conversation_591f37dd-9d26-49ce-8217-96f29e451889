Base path: 'C:/Unity/Editors/6000.2.0f1/Editor/Data', plugins path 'C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines'
Cmd: initializeCompiler

Cmd: compileComputeKernel
  insize=901 file=Packages/com.unity.render-pipelines.high-definition/Runtime/Core/CoreResources/ClearUIntTextureArray.compute kernel=ClearUIntTextureArray ppOnly=0 stripLineD=0 buildPlatform=19 km=<UNITY_VERSION=60020000> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=350

Cmd: compileComputeKernel
  insize=840 file=Packages/com.unity.render-pipelines.high-definition/Runtime/Core/CoreResources/GPUCopy.compute kernel=KSampleCopy4_1_x_8 ppOnly=0 stripLineD=0 buildPlatform=19 km=<KERNEL_NAME41=KSampleCopy4_1_x_8>,<KERNEL_SIZE=8>,<UNITY_VERSION=60020000> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=598

Cmd: compileComputeKernel
  insize=1509 file=Packages/com.unity.render-pipelines.high-definition/Runtime/Lighting/LightLoop/cleardispatchindirect.compute kernel=ClearDispatchIndirect ppOnly=0 stripLineD=0 buildPlatform=19 km=<UNITY_VERSION=60020000> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=582

Cmd: compileComputeKernel
  insize=2826 file=Packages/com.unity.render-pipelines.high-definition/Runtime/Lighting/LightLoop/builddispatchindirect.compute kernel=BuildIndirect ppOnly=0 stripLineD=0 buildPlatform=19 km=<UNITY_VERSION=60020000> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=2169

Cmd: compileComputeKernel
  insize=12140 file=Packages/com.unity.render-pipelines.high-definition/Runtime/Lighting/LightLoop/Deferred.compute kernel=Deferred_Indirect_Fptl_Variant1 ppOnly=0 stripLineD=0 buildPlatform=19 km=<SHADE_OPAQUE_ENTRY=Deferred_Indirect_Fptl_Variant1>,<USE_INDIRECT=1>,<VARIANT=1>,<UNITY_VERSION=60020000> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SCREEN_SPACE_SHADOWS_OFF PROBE_VOLUMES_L1 PUNCTUAL_SHADOW_HIGH DIRECTIONAL_SHADOW_HIGH AREA_SHADOW_MEDIUM SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=55024

Cmd: compileComputeKernel
  insize=12140 file=Packages/com.unity.render-pipelines.high-definition/Runtime/Lighting/LightLoop/Deferred.compute kernel=Deferred_Indirect_Fptl_Variant6 ppOnly=0 stripLineD=0 buildPlatform=19 km=<SHADE_OPAQUE_ENTRY=Deferred_Indirect_Fptl_Variant6>,<USE_INDIRECT=1>,<VARIANT=6>,<UNITY_VERSION=60020000> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SCREEN_SPACE_SHADOWS_OFF PROBE_VOLUMES_L1 PUNCTUAL_SHADOW_HIGH DIRECTIONAL_SHADOW_HIGH AREA_SHADOW_MEDIUM SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=86581

Cmd: compileComputeKernel
  insize=12140 file=Packages/com.unity.render-pipelines.high-definition/Runtime/Lighting/LightLoop/Deferred.compute kernel=Deferred_Indirect_Fptl_Variant12 ppOnly=0 stripLineD=0 buildPlatform=19 km=<SHADE_OPAQUE_ENTRY=Deferred_Indirect_Fptl_Variant12>,<USE_INDIRECT=1>,<VARIANT=12>,<UNITY_VERSION=60020000> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SCREEN_SPACE_SHADOWS_OFF PROBE_VOLUMES_L1 PUNCTUAL_SHADOW_HIGH DIRECTIONAL_SHADOW_HIGH AREA_SHADOW_MEDIUM SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=137400

Cmd: compileComputeKernel
  insize=12140 file=Packages/com.unity.render-pipelines.high-definition/Runtime/Lighting/LightLoop/Deferred.compute kernel=Deferred_Indirect_Fptl_Variant26 ppOnly=0 stripLineD=0 buildPlatform=19 km=<SHADE_OPAQUE_ENTRY=Deferred_Indirect_Fptl_Variant26>,<USE_INDIRECT=1>,<VARIANT=26>,<UNITY_VERSION=60020000> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SCREEN_SPACE_SHADOWS_OFF PROBE_VOLUMES_L1 PUNCTUAL_SHADOW_HIGH DIRECTIONAL_SHADOW_HIGH AREA_SHADOW_MEDIUM SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=89298

Cmd: compileComputeKernel
  insize=633 file=Packages/com.unity.render-pipelines.high-definition/Runtime/PostProcessing/Shaders/AlphaCopy.compute kernel=KMain ppOnly=0 stripLineD=0 buildPlatform=19 km=<UNITY_VERSION=60020000> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=453

Cmd: compileComputeKernel
  insize=13256 file=Packages/com.unity.render-pipelines.high-definition/Runtime/PostProcessing/Shaders/LutBuilder3D.compute kernel=KBuild ppOnly=0 stripLineD=0 buildPlatform=19 km=<UNITY_VERSION=60020000> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=TONEMAPPING_NONE GRADE_IN_ACESCG SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=8936


Cmd: initializeCompiler

Unhandled exception: Protocol error - failed to read magic number. Error code 0x80000004 (Not connected). (transferred 0/4)

Quitting shader compiler process
