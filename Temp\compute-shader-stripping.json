{"totalVariantsIn": 19330, "totalVariantsOut": 5106, "shaders": [{"inputVariants": 4, "outputVariants": 0, "name": "StageSetupSegment", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 2.0915}, {"inputVariants": 2, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0032}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "DepthOfFieldCoC", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: KMainPhysical", "stripTimeMs": 0.0281}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0297}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: KMainPhysical", "stripTimeMs": 0.009000000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0085}]}]}, {"inputVariants": 720, "outputVariants": 40, "name": "WaterLighting", "pipelines": [{"inputVariants": 720, "outputVariants": 40, "pipeline": "", "variants": [{"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterClearIndirect", "stripTimeMs": 0.11470000000000001}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterClassifyTiles", "stripTimeMs": 0.0881}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: PrepareSSRIndirect", "stripTimeMs": 0.06770000000000001}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterDeferredLighting_Variant0", "stripTimeMs": 0.0714}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterDeferredLighting_Variant1", "stripTimeMs": 0.0555}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterDeferredLighting_Variant2", "stripTimeMs": 1.1981000000000002}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterDeferredLighting_Variant3", "stripTimeMs": 0.33690000000000003}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterDeferredLighting_Variant4", "stripTimeMs": 0.2738}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterFogIndirect", "stripTimeMs": 0.2523}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterFogTransmittanceIndirect", "stripTimeMs": 0.2985}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterClearIndirect", "stripTimeMs": 0.2768}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterClassifyTiles", "stripTimeMs": 0.2841}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: PrepareSSRIndirect", "stripTimeMs": 0.35750000000000004}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterDeferredLighting_Variant0", "stripTimeMs": 0.6009}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterDeferredLighting_Variant1", "stripTimeMs": 0.3865}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterDeferredLighting_Variant2", "stripTimeMs": 0.2169}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterDeferredLighting_Variant3", "stripTimeMs": 0.19210000000000002}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterDeferredLighting_Variant4", "stripTimeMs": 0.2944}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterFogIndirect", "stripTimeMs": 0.3034}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterFogTransmittanceIndirect", "stripTimeMs": 0.2836}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "LensFlareMergeOcclusionDataDriven", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCS", "stripTimeMs": 0.0106}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCS", "stripTimeMs": 0.0035}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "OccluderDepthPyramidKernels", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>D<PERSON><PERSON><PERSON>", "stripTimeMs": 0.1648}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>D<PERSON><PERSON><PERSON>", "stripTimeMs": 0.0466}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "ProbeVolumeUploadData", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: UploadData", "stripTimeMs": 0.1602}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: UploadData", "stripTimeMs": 0.08510000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "BakeCloudTexture", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: BakeCloudTexture", "stripTimeMs": 0.0257}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: BakeCloudTexture", "stripTimeMs": 0.0048000000000000004}]}]}, {"inputVariants": 13392, "outputVariants": 248, "name": "Deferred", "pipelines": [{"inputVariants": 13392, "outputVariants": 248, "pipeline": "", "variants": [{"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Direct_Fptl", "stripTimeMs": 1.4118000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Direct_Fptl_DebugDisplay", "stripTimeMs": 1.3991}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant0", "stripTimeMs": 1.0065}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant1", "stripTimeMs": 1.0996000000000001}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant2", "stripTimeMs": 1.9080000000000001}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant3", "stripTimeMs": 1.7383000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant4", "stripTimeMs": 1.7335}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant5", "stripTimeMs": 1.9256000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant6", "stripTimeMs": 1.1987}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant7", "stripTimeMs": 1.6865}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant8", "stripTimeMs": 1.8916000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant9", "stripTimeMs": 1.0766}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant10", "stripTimeMs": 1.1932}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant11", "stripTimeMs": 1.6092000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant12", "stripTimeMs": 1.2482}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant13", "stripTimeMs": 1.0454}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant14", "stripTimeMs": 1.1776}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant15", "stripTimeMs": 1.207}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant16", "stripTimeMs": 1.1926}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant17", "stripTimeMs": 1.2255}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant18", "stripTimeMs": 2.4495}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant19", "stripTimeMs": 0.22290000000000001}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant20", "stripTimeMs": 0.1971}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant21", "stripTimeMs": 0.2}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant22", "stripTimeMs": 0.19290000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant23", "stripTimeMs": 0.19090000000000001}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant24", "stripTimeMs": 0.2126}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant25", "stripTimeMs": 0.19110000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant26", "stripTimeMs": 0.19740000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant27", "stripTimeMs": 0.1968}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant28", "stripTimeMs": 0.19870000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Direct_Fptl", "stripTimeMs": 0.1956}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Direct_Fptl_DebugDisplay", "stripTimeMs": 0.1907}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant0", "stripTimeMs": 0.2023}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant1", "stripTimeMs": 0.2013}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant2", "stripTimeMs": 0.1998}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant3", "stripTimeMs": 0.1991}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant4", "stripTimeMs": 0.203}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant5", "stripTimeMs": 0.1991}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant6", "stripTimeMs": 0.1973}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant7", "stripTimeMs": 0.1938}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant8", "stripTimeMs": 0.1913}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant9", "stripTimeMs": 0.274}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant10", "stripTimeMs": 0.1962}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant11", "stripTimeMs": 0.19720000000000001}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant12", "stripTimeMs": 0.1956}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant13", "stripTimeMs": 0.19310000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant14", "stripTimeMs": 0.19340000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant15", "stripTimeMs": 0.20770000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant16", "stripTimeMs": 0.19820000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant17", "stripTimeMs": 0.1937}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant18", "stripTimeMs": 0.1937}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant19", "stripTimeMs": 0.1951}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant20", "stripTimeMs": 0.194}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant21", "stripTimeMs": 0.1912}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant22", "stripTimeMs": 0.1948}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant23", "stripTimeMs": 0.1915}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant24", "stripTimeMs": 0.19260000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant25", "stripTimeMs": 0.1918}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant26", "stripTimeMs": 0.19060000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant27", "stripTimeMs": 0.193}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant28", "stripTimeMs": 0.1943}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "lightlistbuild-clustered", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_NoDepthRT", "stripTimeMs": 0.017}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_NoDepthRT_SrcBigTile", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_SrcBigTile", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA_SrcBigTile", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_Oblique", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA_Oblique", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_SrcBigTile_Oblique", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA_SrcBigTile_Oblique", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_NoDepthRT", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_NoDepthRT_SrcBigTile", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_SrcBigTile", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA_SrcBigTile", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_Oblique", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA_Oblique", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_SrcBigTile_Oblique", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA_SrcBigTile_Oblique", "stripTimeMs": 0.0022}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "WaterFoam", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReprojectFoam", "stripTimeMs": 0.0112}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AttenuateFoam", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReprojectFoam", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AttenuateFoam", "stripTimeMs": 0.0031000000000000003}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "PaniniProjection", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0315}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0125}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "BloomBlur", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0064}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: K<PERSON><PERSON><PERSON>mple", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: K<PERSON><PERSON><PERSON>mple", "stripTimeMs": 0.003}]}]}, {"inputVariants": 8, "outputVariants": 4, "name": "FXAA", "pipelines": [{"inputVariants": 8, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 2, "variantName": "Kernel: FXAA", "stripTimeMs": 0.0149}, {"inputVariants": 4, "outputVariants": 2, "variantName": "Kernel: FXAA", "stripTimeMs": 0.0097}]}]}, {"inputVariants": 4, "outputVariants": 0, "name": "StageRasterBin", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: MainArgs", "stripTimeMs": 0.0009000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0009000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: MainArgs", "stripTimeMs": 0.0009000000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "ApplyExposure", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0095}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0055000000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "DepthOfFieldCoCDilate", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0066}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0034000000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "lightlistbuild-clearatomic", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearAtomic", "stripTimeMs": 0.0067}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearAtomic", "stripTimeMs": 0.0037}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "<PERSON><PERSON><PERSON><PERSON>", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0344}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.031100000000000003}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "WaterLine", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearWaterLine", "stripTimeMs": 0.006500000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LineEvaluation1D", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BoundsPropagation", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearWaterLine", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LineEvaluation1D", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BoundsPropagation", "stripTimeMs": 0.0028}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "HistogramExposure", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KHistogramGen", "stripTimeMs": 0.009300000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KHistogramReduce", "stripTimeMs": 0.005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KHistogramGen", "stripTimeMs": 0.0061}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KHistogramReduce", "stripTimeMs": 0.0051}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "ProbeVolumeSamplingDebugPositionNormal", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ComputePositionNormal", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ComputePositionNormal", "stripTimeMs": 0.001}]}]}, {"inputVariants": 18, "outputVariants": 18, "name": "VFXCopyBuffer", "pipelines": [{"inputVariants": 18, "outputVariants": 18, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyBuffer", "stripTimeMs": 0.006200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyStructBuffer", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXInitDeadListBuffer", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXZeroInitBuffer", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXZeroInitBufferUint", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchCopyCountUint", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchCopyCountKvp", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXInitBoundsBuffer", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyStructBufferSelf", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyBuffer", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyStructBuffer", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXInitDeadListBuffer", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXZeroInitBuffer", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXZeroInitBufferUint", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchCopyCountUint", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchCopyCountKvp", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXInitBoundsBuffer", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyStructBufferSelf", "stripTimeMs": 0.0023}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "BloomPrefilter", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0205}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.015000000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "VolumetricMaterial", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeVolumetricMaterialRenderingParameters", "stripTimeMs": 0.0078000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeVolumetricMaterialRenderingParameters", "stripTimeMs": 0.005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "ScreenSpaceMultipleScattering", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScreenSpaceMultipleScattering", "stripTimeMs": 0.006500000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScreenSpaceMultipleScattering", "stripTimeMs": 0.0031000000000000003}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "InstanceTransformUpdateKernels", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterInitTransformMain", "stripTimeMs": 0.012400000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterUpdateTransformMain", "stripTimeMs": 0.0099}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterUpdateMotionMain", "stripTimeMs": 0.0083}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterUpdateProbesMain", "stripTimeMs": 0.009000000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterInitTransformMain", "stripTimeMs": 0.008400000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterUpdateTransformMain", "stripTimeMs": 0.008}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterUpdateMotionMain", "stripTimeMs": 0.0081}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterUpdateProbesMain", "stripTimeMs": 0.0076}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "MotionBlur", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: MotionBlurCS", "stripTimeMs": 0.0131}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: MotionBlurCS", "stripTimeMs": 0.0088}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "GTAOSpatialDenoise", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: SpatialDenoise", "stripTimeMs": 0.0098}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: SpatialDenoise", "stripTimeMs": 0.005200000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DepthOfFieldMipSafe", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0129}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.009300000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "GTAOTemporalDenoise", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TemporalDenoise", "stripTimeMs": 0.0114}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TemporalDenoise", "stripTimeMs": 0.0054}]}]}, {"inputVariants": 14, "outputVariants": 14, "name": "WaterSimulation", "pipelines": [{"inputVariants": 14, "outputVariants": 14, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: InitializePhillipsSpectrum", "stripTimeMs": 0.0066}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateDispersion", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateNormals", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateNormalsJacobian", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PrepareCausticsGeometry", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateInstanceData", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateInstanceDataInfinite", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: InitializePhillipsSpectrum", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateDispersion", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateNormals", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateNormalsJacobian", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PrepareCausticsGeometry", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateInstanceData", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateInstanceDataInfinite", "stripTimeMs": 0.0021000000000000003}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DownsampleVTFeedback", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0066}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainMSAA", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainMSAA", "stripTimeMs": 0.0024000000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "MotionBlurGenTilePass", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TileGenPass", "stripTimeMs": 0.008700000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TileGenPass", "stripTimeMs": 0.0049}]}]}, {"inputVariants": 64, "outputVariants": 64, "name": "StpSetup", "pipelines": [{"inputVariants": 64, "outputVariants": 64, "pipeline": "", "variants": [{"inputVariants": 32, "outputVariants": 32, "variantName": "Kernel: StpSetup", "stripTimeMs": 0.07200000000000001}, {"inputVariants": 32, "outputVariants": 32, "variantName": "Kernel: StpSetup", "stripTimeMs": 0.06520000000000001}]}]}, {"inputVariants": 48, "outputVariants": 24, "name": "LutBuilder3D", "pipelines": [{"inputVariants": 48, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 24, "outputVariants": 12, "variantName": "Kernel: KBuild", "stripTimeMs": 0.0901}, {"inputVariants": 24, "outputVariants": 12, "variantName": "Kernel: KBuild", "stripTimeMs": 0.08}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "StpTaa", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: StpTaa", "stripTimeMs": 0.0409}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: StpTaa", "stripTimeMs": 0.0341}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DepthOfFieldPreCombineFar", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KMainPreCombineFar", "stripTimeMs": 0.0115}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KMainPreCombineFar", "stripTimeMs": 0.0051}]}]}, {"inputVariants": 4, "outputVariants": 2, "name": "ContactShadows", "pipelines": [{"inputVariants": 4, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 1, "variantName": "Kernel: DeferredContactShadow", "stripTimeMs": 0.009000000000000001}, {"inputVariants": 2, "outputVariants": 1, "variantName": "Kernel: DeferredContactShadow", "stripTimeMs": 0.0053}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "BakeCloudShadows", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: BakeCloudShadows", "stripTimeMs": 0.058600000000000006}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: BakeCloudShadows", "stripTimeMs": 0.0356}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "DepthOfFieldGather", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: K<PERSON>ain<PERSON>ear", "stripTimeMs": 0.037200000000000004}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.030500000000000003}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: K<PERSON>ain<PERSON>ear", "stripTimeMs": 0.0307}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0339}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DepthOfFieldCoCReproject", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0095}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0053}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "HairMultipleScatteringPreIntegration", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeAttenuationForward", "stripTimeMs": 0.0067}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeAttenuationBackward", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeAzimuthalScattering", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeLongitudinalScattering", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeAttenuationForward", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeAttenuationBackward", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeAzimuthalScattering", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeLongitudinalScattering", "stripTimeMs": 0.0029000000000000002}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "BlitAndExpose", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.006900000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KAccumMain", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KAccumMain", "stripTimeMs": 0.0024000000000000002}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "MomentShadows", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeMomentShadows", "stripTimeMs": 0.0081}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MomentSummedAreaTableHorizontal", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MomentSummedAreaTableVertical", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeMomentShadows", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MomentSummedAreaTableHorizontal", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MomentSummedAreaTableVertical", "stripTimeMs": 0.0031000000000000003}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "ColorPyramid", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0117}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KColorDownsample", "stripTimeMs": 0.0088}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0077}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KColorDownsample", "stripTimeMs": 0.0074}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "builddispatchindirect", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BuildIndirect", "stripTimeMs": 0.006500000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BuildIndirect", "stripTimeMs": 0.0036000000000000003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "RandomDownsample", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Downsample", "stripTimeMs": 0.0067}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Downsample", "stripTimeMs": 0.0029000000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "DebugHistogramImage", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KHistogramGen", "stripTimeMs": 0.0068000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KHistogramGen", "stripTimeMs": 0.0031000000000000003}]}]}, {"inputVariants": 12, "outputVariants": 0, "name": "StagePrepare", "pipelines": [{"inputVariants": 12, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearCounters", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearBins", "stripTimeMs": 0.0014}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearClusters", "stripTimeMs": 0.0014}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ComputeClusterRanges", "stripTimeMs": 0.0015}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearCountersPer<PERSON><PERSON><PERSON>", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: UpdateOffsets", "stripTimeMs": 0.0013000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearCounters", "stripTimeMs": 0.0016}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearBins", "stripTimeMs": 0.0012000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearClusters", "stripTimeMs": 0.0009000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ComputeClusterRanges", "stripTimeMs": 0.0013000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearCountersPer<PERSON><PERSON><PERSON>", "stripTimeMs": 0.0013000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: UpdateOffsets", "stripTimeMs": 0.0012000000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "InstanceDataBufferUploadKernels", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainUploadScatterInstances", "stripTimeMs": 0.008700000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainUploadScatterInstances", "stripTimeMs": 0.005200000000000001}]}]}, {"inputVariants": 8, "outputVariants": 4, "name": "ContrastAdaptiveSharpen", "pipelines": [{"inputVariants": 8, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.008700000000000001}, {"inputVariants": 2, "outputVariants": 1, "variantName": "Kernel: KInitialize", "stripTimeMs": 0.0057}, {"inputVariants": 2, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0044}, {"inputVariants": 2, "outputVariants": 1, "variantName": "Kernel: KInitialize", "stripTimeMs": 0.0047}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "BloomUpsample", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.009000000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0047}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "MotionBlurNeighborhoodTilePass", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TileNeighbourhood", "stripTimeMs": 0.0122}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TileNeighbourhood", "stripTimeMs": 0.0097}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "lightlistbuild-bigtile", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: BigTileLightListGen", "stripTimeMs": 0.0085}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: BigTileLightListGen", "stripTimeMs": 0.0048000000000000004}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "ProbeVolumeUploadDataL2", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UploadDataL2", "stripTimeMs": 0.006200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UploadDataL2", "stripTimeMs": 0.0027}]}]}, {"inputVariants": 4, "outputVariants": 0, "name": "DebugWaveform", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KWaveformGather", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KWaveformClear", "stripTimeMs": 0.0008}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KWaveformGather", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KWaveformClear", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DepthPyramid", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KDepthDownsample8DualUav", "stripTimeMs": 0.0078000000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KDepthDownsample8DualUav", "stripTimeMs": 0.0048000000000000004}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "lightlistbuild", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: TileLightListGen", "stripTimeMs": 0.030000000000000002}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: TileLightListGen", "stripTimeMs": 0.0269}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "ResolveStencilBuffer", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: Main", "stripTimeMs": 0.039400000000000004}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: Main", "stripTimeMs": 0.0333}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "PostSharpenPass", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: SharpenCS", "stripTimeMs": 0.020300000000000002}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: SharpenCS", "stripTimeMs": 0.010700000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "GTAO", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: GTAOMain", "stripTimeMs": 0.0159}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: GTAOMain", "stripTimeMs": 0.0089}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "BilateralUpsample", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralUpSampleColorHalf", "stripTimeMs": 0.0071}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralUpSampleColor", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralUpSampleColorHalf", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralUpSampleColor", "stripTimeMs": 0.0026000000000000003}]}]}, {"inputVariants": 42, "outputVariants": 42, "name": "GenSdfRayMap", "pipelines": [{"inputVariants": 42, "outputVariants": 42, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: InBucketSum", "stripTimeMs": 0.007}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlockSums", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: FinalSum", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ToTextureNormalized", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyTextures", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: JFA", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DistanceTransform", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Copy<PERSON><PERSON><PERSON>", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GenerateRayMapLocal", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignPass6Rays", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignPassNeighbors", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ToBlockSumBuffer", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearTexturesAndBuffers", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GenerateTrianglesUV", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ConservativeRasterization", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ChooseDirectionTriangleOnly", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SurfaceClosing", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanX", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanY", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanZ", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: InBucketSum", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlockSums", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: FinalSum", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ToTextureNormalized", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyTextures", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: JFA", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DistanceTransform", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Copy<PERSON><PERSON><PERSON>", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GenerateRayMapLocal", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignPass6Rays", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignPassNeighbors", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ToBlockSumBuffer", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearTexturesAndBuffers", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GenerateTrianglesUV", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ConservativeRasterization", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ChooseDirectionTriangleOnly", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SurfaceClosing", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanX", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanY", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanZ", "stripTimeMs": 0.0022}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "DepthOfFieldClearIndirectArgs", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON>lear", "stripTimeMs": 0.0083}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON>lear", "stripTimeMs": 0.0029000000000000002}]}]}, {"inputVariants": 4, "outputVariants": 0, "name": "DebugVectorscope", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KVectorscopeGather", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KVectorscopeClear", "stripTimeMs": 0.0016}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KVectorscopeGather", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KVectorscopeClear", "stripTimeMs": 0.0008}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "AmbientProbeConvolution", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionDiffuse", "stripTimeMs": 0.006}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionVolumetric", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionDiffuseVolumetric", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionClouds", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionDiffuse", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionVolumetric", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionDiffuseVolumetric", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionClouds", "stripTimeMs": 0.0029000000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "InScatteredRadiancePrecomputation", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: main", "stripTimeMs": 0.0089}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: main", "stripTimeMs": 0.004}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "scrbound", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: main", "stripTimeMs": 0.006500000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: main", "stripTimeMs": 0.003}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "TemporalFilter", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ValidateHistory", "stripTimeMs": 0.0063}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationSingle", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationSingleArray", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationColor", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationColorArray", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyHistory", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlendHistorySingleArray", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlendHistorySingleArrayNoValidity", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlendHistoryColorArray", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: OutputHistoryArray", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ValidateHistory", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationSingle", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationSingleArray", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationColor", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationColorArray", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyHistory", "stripTimeMs": 0.002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlendHistorySingleArray", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlendHistorySingleArrayNoValidity", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlendHistoryColorArray", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: OutputHistoryArray", "stripTimeMs": 0.0022}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "ClearLightLists", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearList", "stripTimeMs": 0.006500000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearList", "stripTimeMs": 0.0032}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "DoFCircleOfConfusion", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KMainCoCPhysical", "stripTimeMs": 0.0095}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KMainCoCManual", "stripTimeMs": 0.006200000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KMainCoCPhysical", "stripTimeMs": 0.0047}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KMainCoCManual", "stripTimeMs": 0.0045000000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "DoFMinMaxDilate", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0099}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0038}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "ComputeGgxIblSampleData", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeGgxIblSampleData", "stripTimeMs": 0.0082}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeGgxIblSampleData", "stripTimeMs": 0.0047}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "EyeCausticLUTGen", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SampleC<PERSON>tic", "stripTimeMs": 0.008700000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyToLUT", "stripTimeMs": 0.0063}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SampleC<PERSON>tic", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyToLUT", "stripTimeMs": 0.0053}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.004}]}]}, {"inputVariants": 14, "outputVariants": 14, "name": "GPUPrefixSum", "pipelines": [{"inputVariants": 14, "outputVariants": 14, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCalculateLevelDispatchArgsFromConst", "stripTimeMs": 0.0063}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCalculateLevelDispatchArgsFromBuffer", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumOnGroup", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumOnGroupExclusive", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumNextInput", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumResolveParent", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumResolveParentExclusive", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCalculateLevelDispatchArgsFromConst", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCalculateLevelDispatchArgsFromBuffer", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumOnGroup", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumOnGroupExclusive", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumNextInput", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumResolveParent", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumResolveParentExclusive", "stripTimeMs": 0.0022}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "VFXPrefixSum", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXPrepareSingleInstance", "stripTimeMs": 0.0079}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchSumCount", "stripTimeMs": 0.005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchSumCount_128", "stripTimeMs": 0.0047}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBuildPrefixSum", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBuildPrefixSum_128", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXPrepareSingleInstance", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchSumCount", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchSumCount_128", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBuildPrefixSum", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBuildPrefixSum_128", "stripTimeMs": 0.0023}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DepthOfFieldKernel", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KParametricBlurKernel", "stripTimeMs": 0.0066}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KParametricFloodfillKernel", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KParametricBlurKernel", "stripTimeMs": 0.0053}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KParametricFloodfillKernel", "stripTimeMs": 0.0027}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "SkyLUTGenerator", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MultiScatteringLUT", "stripTimeMs": 0.0091}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SkyViewLUT", "stripTimeMs": 0.0051}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AtmosphericScatteringLUTCamera", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AtmosphericScatteringLUTWorld", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AtmosphericScatteringBlur", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MultiScatteringLUT", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SkyViewLUT", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AtmosphericScatteringLUTCamera", "stripTimeMs": 0.0046}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AtmosphericScatteringLUTWorld", "stripTimeMs": 0.0046}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AtmosphericScatteringBlur", "stripTimeMs": 0.0045000000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "GTAOCopyHistory", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GTAODenoise_CopyHistory", "stripTimeMs": 0.0064}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GTAODenoise_CopyHistory", "stripTimeMs": 0.0031000000000000003}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "ClearDebugBuffer", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: clearMain", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: clearMain", "stripTimeMs": 0.0023}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Texture3DAtlas", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0081}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GenerateMipMap", "stripTimeMs": 0.0046}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0046}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GenerateMipMap", "stripTimeMs": 0.004200000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "NaNKiller", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.014400000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.009600000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "VFXFillIndirectArgs", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXIndirectArgs", "stripTimeMs": 0.006500000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXIndirectArgs", "stripTimeMs": 0.0031000000000000003}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "DepthOfFieldTileMax", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0134}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0091}]}]}, {"inputVariants": 12, "outputVariants": 0, "name": "StageRasterFine", "pipelines": [{"inputVariants": 12, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0013000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0008}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0011}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0012000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0006000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "UpdateStrips", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UpdateParticleStrip", "stripTimeMs": 0.0057}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UpdateParticleStrip", "stripTimeMs": 0.0026000000000000003}]}]}, {"inputVariants": 24, "outputVariants": 16, "name": "ScreenSpaceGlobalIllumination", "pipelines": [{"inputVariants": 24, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: TraceGlobalIllumination", "stripTimeMs": 0.012700000000000001}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: TraceGlobalIlluminationHalf", "stripTimeMs": 0.0063}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: ReprojectGlobalIllumination", "stripTimeMs": 0.0059}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: ReprojectGlobalIlluminationHalf", "stripTimeMs": 0.007500000000000001}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: TraceGlobalIllumination", "stripTimeMs": 0.006200000000000001}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: TraceGlobalIlluminationHalf", "stripTimeMs": 0.006}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: ReprojectGlobalIllumination", "stripTimeMs": 0.006500000000000001}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: ReprojectGlobalIlluminationHalf", "stripTimeMs": 0.006500000000000001}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Exposure", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KFixedExposure", "stripTimeMs": 0.007}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KManualCameraExposure", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KPrePass", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KReduction", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KFixedExposure", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KManualCameraExposure", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KPrePass", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KReduction", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0023}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "WaterDeformation", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: FilterDeformation", "stripTimeMs": 0.01}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: EvaluateDeformationSurfaceGradient", "stripTimeMs": 0.0046}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: FilterDeformation", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: EvaluateDeformationSurfaceGradient", "stripTimeMs": 0.0067}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "GTAOBlurAndUpsample", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlurUpsample", "stripTimeMs": 0.008400000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralUpsampling", "stripTimeMs": 0.005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BoxUpsampling", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Blur", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Blur_FullRes", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlurUpsample", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralUpsampling", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BoxUpsampling", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Blur", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Blur_FullRes", "stripTimeMs": 0.0048000000000000004}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "PlanarReflectionFiltering", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: FilterPlanarReflection", "stripTimeMs": 0.0066}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DownScale", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DepthConversion", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: FilterPlanarReflection", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DownScale", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DepthConversion", "stripTimeMs": 0.0023}]}]}, {"inputVariants": 8, "outputVariants": 4, "name": "ProbeVolumeBlendStates", "pipelines": [{"inputVariants": 8, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 2, "variantName": "Kernel: BlendScenarios", "stripTimeMs": 0.0137}, {"inputVariants": 4, "outputVariants": 2, "variantName": "Kernel: BlendScenarios", "stripTimeMs": 0.0088}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "GPUCopy", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KSampleCopy4_1_x_8", "stripTimeMs": 0.0067}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KSampleCopy4_1_x_1", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KSampleCopy4_1_x_8", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KSampleCopy4_1_x_1", "stripTimeMs": 0.0024000000000000002}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "StpPreTaa", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: StpPreTaa", "stripTimeMs": 0.0388}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: StpPreTaa", "stripTimeMs": 0.033100000000000004}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "EncodeBC6H", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KEncodeFastCubemapMip", "stripTimeMs": 0.0081}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KEncodeFastCubemapMip", "stripTimeMs": 0.003}]}]}, {"inputVariants": 32, "outputVariants": 16, "name": "SubsurfaceScattering", "pipelines": [{"inputVariants": 32, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 4, "variantName": "Kernel: SubsurfaceScattering", "stripTimeMs": 0.032}, {"inputVariants": 8, "outputVariants": 4, "variantName": "Kernel: PackDiffusionProfile", "stripTimeMs": 0.0296}, {"inputVariants": 8, "outputVariants": 4, "variantName": "Kernel: SubsurfaceScattering", "stripTimeMs": 0.027600000000000003}, {"inputVariants": 8, "outputVariants": 4, "variantName": "Kernel: PackDiffusionProfile", "stripTimeMs": 0.025}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "OcclusionCullingDebug", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearOcclusionDebug", "stripTimeMs": 0.0089}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearOcclusionDebug", "stripTimeMs": 0.004200000000000001}]}]}, {"inputVariants": 384, "outputVariants": 256, "name": "VolumetricLighting", "pipelines": [{"inputVariants": 384, "outputVariants": 256, "pipeline": "", "variants": [{"inputVariants": 192, "outputVariants": 128, "variantName": "Kernel: VolumetricLighting", "stripTimeMs": 0.3808}, {"inputVariants": 192, "outputVariants": 128, "variantName": "Kernel: VolumetricLighting", "stripTimeMs": 0.3728}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "DiffuseDenoiser", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GeneratePointDistribution", "stripTimeMs": 0.017400000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralFilterSingle", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralFilterColor", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>ingle", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GatherColor", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GeneratePointDistribution", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralFilterSingle", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralFilterColor", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>ingle", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GatherColor", "stripTimeMs": 0.0023}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DoFComputeSlowTiles", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeSlowTiles", "stripTimeMs": 0.014100000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeSlowTiles", "stripTimeMs": 0.0056}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "BuildProbabilityTables", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeConditionalDensities", "stripTimeMs": 0.0076}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeMarginalRowDensities", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeConditionalDensities", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeMarginalRowDensities", "stripTimeMs": 0.0046}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "FourierTransform", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RowPassTi_256", "stripTimeMs": 0.0074}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ColPassTi_256", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RowPassTi_128", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ColPassTi_128", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RowPassTi_64", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ColPassTi_64", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RowPassTi_256", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ColPassTi_256", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RowPassTi_128", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ColPassTi_128", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RowPassTi_64", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ColPassTi_64", "stripTimeMs": 0.0022}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "VolumeVoxelization", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: VolumeVoxelization", "stripTimeMs": 0.0118}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: VolumeVoxelization", "stripTimeMs": 0.0064}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "WaterEvaluation", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: FindVerticalDisplacements", "stripTimeMs": 0.0431}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: FindVerticalDisplacements", "stripTimeMs": 0.0373}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "AlphaCopy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0071}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0045000000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "DoFCoCMinMax", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainCoCMinMax", "stripTimeMs": 0.0079}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainCoCMinMax", "stripTimeMs": 0.004200000000000001}]}]}, {"inputVariants": 48, "outputVariants": 48, "name": "DepthOfFieldCombine", "pipelines": [{"inputVariants": 48, "outputVariants": 48, "pipeline": "", "variants": [{"inputVariants": 24, "outputVariants": 24, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.059300000000000005}, {"inputVariants": 24, "outputVariants": 24, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0505}]}]}, {"inputVariants": 48, "outputVariants": 48, "name": "VrsTexture", "pipelines": [{"inputVariants": 48, "outputVariants": 48, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: TextureCopy", "stripTimeMs": 0.0523}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: TextureReduce", "stripTimeMs": 0.0407}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: TextureCopy", "stripTimeMs": 0.025900000000000003}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: TextureReduce", "stripTimeMs": 0.0364}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "MotionBlurMergeTilePass", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileMerge", "stripTimeMs": 0.0106}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileMerge", "stripTimeMs": 0.0041}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "ClearUIntTextureArray", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearUIntTexture", "stripTimeMs": 0.009600000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearUIntTextureArray", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearUIntTexture", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearUIntTextureArray", "stripTimeMs": 0.0036000000000000003}]}]}, {"inputVariants": 160, "outputVariants": 160, "name": "ScreenSpaceReflections", "pipelines": [{"inputVariants": 160, "outputVariants": 160, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsTracing", "stripTimeMs": 0.0149}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsReprojection", "stripTimeMs": 0.0098}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionBoth", "stripTimeMs": 0.0089}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionSourceOnly", "stripTimeMs": 0.0081}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionTargetOnly", "stripTimeMs": 0.0081}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionBoth", "stripTimeMs": 0.0089}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionSourceOnly", "stripTimeMs": 0.0085}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionTargetOnly", "stripTimeMs": 0.008700000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionBoth", "stripTimeMs": 0.0088}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionSourceOnly", "stripTimeMs": 0.008}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionTargetOnly", "stripTimeMs": 0.0081}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionBothDebug", "stripTimeMs": 0.008400000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionSourceOnlyDebug", "stripTimeMs": 0.008}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionTargetOnlyDebug", "stripTimeMs": 0.0086}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionBothDebug", "stripTimeMs": 0.0085}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionSourceOnlyDebug", "stripTimeMs": 0.008}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionTargetOnlyDebug", "stripTimeMs": 0.0085}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionBothDebug", "stripTimeMs": 0.0088}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionSourceOnlyDebug", "stripTimeMs": 0.0081}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionTargetOnlyDebug", "stripTimeMs": 0.0086}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsTracing", "stripTimeMs": 0.0081}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsReprojection", "stripTimeMs": 0.0082}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionBoth", "stripTimeMs": 0.008400000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionSourceOnly", "stripTimeMs": 0.009600000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionTargetOnly", "stripTimeMs": 0.0079}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionBoth", "stripTimeMs": 0.008}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionSourceOnly", "stripTimeMs": 0.008700000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionTargetOnly", "stripTimeMs": 0.0091}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionBoth", "stripTimeMs": 0.008}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionSourceOnly", "stripTimeMs": 0.008}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionTargetOnly", "stripTimeMs": 0.0081}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionBothDebug", "stripTimeMs": 0.0089}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionSourceOnlyDebug", "stripTimeMs": 0.0079}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionTargetOnlyDebug", "stripTimeMs": 0.008}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionBothDebug", "stripTimeMs": 0.0085}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionSourceOnlyDebug", "stripTimeMs": 0.0088}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionTargetOnlyDebug", "stripTimeMs": 0.0083}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionBothDebug", "stripTimeMs": 0.0083}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionSourceOnlyDebug", "stripTimeMs": 0.008700000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionTargetOnlyDebug", "stripTimeMs": 0.0081}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "DepthOfFieldMip", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColor", "stripTimeMs": 0.022500000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColorAlpha", "stripTimeMs": 0.005200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainCoC", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColorCopy", "stripTimeMs": 0.0054}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColorCopyAlpha", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColor", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColorAlpha", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainCoC", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColorCopy", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColorCopyAlpha", "stripTimeMs": 0.003}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "DoFCombine", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: UpsampleFastTiles", "stripTimeMs": 0.025900000000000003}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: UpsampleFastTiles", "stripTimeMs": 0.016800000000000002}]}]}, {"inputVariants": 10, "outputVariants": 0, "name": "StageWorkQueue", "pipelines": [{"inputVariants": 10, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueBuildArgs", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueBuild", "stripTimeMs": 0.0012000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueActiveBins", "stripTimeMs": 0.0008}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueActiveClusters", "stripTimeMs": 0.001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: BuildFineRasterArgs", "stripTimeMs": 0.0009000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueBuildArgs", "stripTimeMs": 0.0016}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueBuild", "stripTimeMs": 0.0009000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueActiveBins", "stripTimeMs": 0.0011}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueActiveClusters", "stripTimeMs": 0.0019}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: BuildFineRasterArgs", "stripTimeMs": 0.0007}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DoFApertureShape", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeShape<PERSON>uffer", "stripTimeMs": 0.0164}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeShape<PERSON>uffer", "stripTimeMs": 0.0064}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "GPUSort", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 5, "outputVariants": 5, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.020300000000000002}, {"inputVariants": 5, "outputVariants": 5, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.011000000000000001}, {"inputVariants": 5, "outputVariants": 5, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0109}, {"inputVariants": 5, "outputVariants": 5, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0109}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "GenerateMaxZ", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeMaxZ", "stripTimeMs": 0.014100000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeFinalMask", "stripTimeMs": 0.0078000000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: DilateMask", "stripTimeMs": 0.0079}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeMaxZ", "stripTimeMs": 0.0046}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeFinalMask", "stripTimeMs": 0.0049}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: DilateMask", "stripTimeMs": 0.0048000000000000004}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "Sort", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort128", "stripTimeMs": 0.008}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort1024", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort1024_128", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort2048", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort2048_128", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort4096", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort4096_128", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass4096_128", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass2048_128", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort128", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort1024", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort1024_128", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort2048", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort2048_128", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort4096", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort4096_128", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass4096_128", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass2048_128", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0023}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "GroundIrradiancePrecomputation", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: main", "stripTimeMs": 0.0088}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: main", "stripTimeMs": 0.0032}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Accumulation", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.015000000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.009000000000000001}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "MotionBlurMotionVecPrep", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 6, "outputVariants": 6, "variantName": "Kernel: MotionVecPreppingCS", "stripTimeMs": 0.02}, {"inputVariants": 6, "outputVariants": 6, "variantName": "Kernel: MotionVecPreppingCS", "stripTimeMs": 0.014400000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "VolumetricLightingFiltering", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: FilterVolumetricLighting", "stripTimeMs": 0.0132}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: FilterVolumetricLighting", "stripTimeMs": 0.0058000000000000005}]}]}, {"inputVariants": 8, "outputVariants": 4, "name": "EdgeAdaptiveSpatialUpsampling", "pipelines": [{"inputVariants": 8, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0182}, {"inputVariants": 4, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0095}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "DebugHDRxyMapping", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KCIExyGen", "stripTimeMs": 0.006900000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KCIExyGen", "stripTimeMs": 0.0032}]}]}, {"inputVariants": 256, "outputVariants": 128, "name": "UberPost", "pipelines": [{"inputVariants": 256, "outputVariants": 128, "pipeline": "", "variants": [{"inputVariants": 128, "outputVariants": 64, "variantName": "Kernel: Uber", "stripTimeMs": 0.43310000000000004}, {"inputVariants": 128, "outputVariants": 64, "variantName": "Kernel: Uber", "stripTimeMs": 0.2607}]}]}, {"inputVariants": 48, "outputVariants": 48, "name": "DepthOfFieldPrefilter", "pipelines": [{"inputVariants": 48, "outputVariants": 48, "pipeline": "", "variants": [{"inputVariants": 24, "outputVariants": 24, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0683}, {"inputVariants": 24, "outputVariants": 24, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.052500000000000005}]}]}, {"inputVariants": 4, "outputVariants": 0, "name": "DebugLightVolumes", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: LightVolumeGradient", "stripTimeMs": 0.0068000000000000005}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: LightVolumeColors", "stripTimeMs": 0.0016}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: LightVolumeGradient", "stripTimeMs": 0.0018000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: LightVolumeColors", "stripTimeMs": 0.0014}]}]}, {"inputVariants": 72, "outputVariants": 72, "name": "InstanceOcclusionCullingKernels", "pipelines": [{"inputVariants": 72, "outputVariants": 72, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: ResetDrawArgs", "stripTimeMs": 0.0511}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: CopyInstances", "stripTimeMs": 0.0424}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: CullInstances", "stripTimeMs": 0.045000000000000005}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: ResetDrawArgs", "stripTimeMs": 0.041800000000000004}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: CopyInstances", "stripTimeMs": 0.041100000000000005}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: CullInstances", "stripTimeMs": 0.0429}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "InstanceDataBufferCopyKernels", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCopyInstances", "stripTimeMs": 0.013900000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCopyInstances", "stripTimeMs": 0.0043}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "ClearBuffer2D", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearBuffer2DMain", "stripTimeMs": 0.009300000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearBuffer2DMain", "stripTimeMs": 0.004200000000000001}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "EVSMBlur", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ConvertAndBlur", "stripTimeMs": 0.0091}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Blur", "stripTimeMs": 0.0053}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyMoments", "stripTimeMs": 0.0058000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ConvertAndBlur", "stripTimeMs": 0.005200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Blur", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyMoments", "stripTimeMs": 0.0051}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "materialflags", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: MaterialFlagsGen", "stripTimeMs": 0.012}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: MaterialFlagsGen", "stripTimeMs": 0.0067}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "cleardispatchindirect", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearDispatchIndirect", "stripTimeMs": 0.0067}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearDispatchIndirect", "stripTimeMs": 0.003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "InstanceWindDataUpdateKernels", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: WindDataCopyHistoryMain", "stripTimeMs": 0.006200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: WindDataCopyHistoryMain", "stripTimeMs": 0.0027}]}]}, {"inputVariants": 20, "outputVariants": 0, "name": "StageShadingSetup", "pipelines": [{"inputVariants": 20, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearShadingSampleVisibilityBuffer", "stripTimeMs": 0.0049}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearShadingCompactionBuffer", "stripTimeMs": 0.0009000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearShadingAtlas", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearHistogram", "stripTimeMs": 0.0008}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CopyHistoryToShadingAtlas", "stripTimeMs": 0.0015}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CreateCompactedShadingSamplesMapping", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ReplicateShadedSamplesToShadingAtlas", "stripTimeMs": 0.0006000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CalculateHistogram", "stripTimeMs": 0.0006000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CalculateHighestVisibleHistogramID", "stripTimeMs": 0.0013000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: DiscardSamplesBasedOnHistogram", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearShadingSampleVisibilityBuffer", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearShadingCompactionBuffer", "stripTimeMs": 0.0006000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearShadingAtlas", "stripTimeMs": 0.0006000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearHistogram", "stripTimeMs": 0.0006000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CopyHistoryToShadingAtlas", "stripTimeMs": 0.0006000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CreateCompactedShadingSamplesMapping", "stripTimeMs": 0.0006000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ReplicateShadedSamplesToShadingAtlas", "stripTimeMs": 0.0006000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CalculateHistogram", "stripTimeMs": 0.0005}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CalculateHighestVisibleHistogramID", "stripTimeMs": 0.0006000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: DiscardSamplesBasedOnHistogram", "stripTimeMs": 0.0006000000000000001}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "ReferenceImpl.IndexingOpsA", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Tile", "stripTimeMs": 0.016800000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0053}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GatherElementsFast", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScatterElementsFast", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScatterElements", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Expand", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Slice", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Tile", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GatherElementsFast", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScatterElementsFast", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScatterElements", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Expand", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Slice", "stripTimeMs": 0.0021000000000000003}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "TensorToTexture", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TensorToTextureExact", "stripTimeMs": 0.013000000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TensorToTextureLinear", "stripTimeMs": 0.0051}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TensorToTextureExact", "stripTimeMs": 0.005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TensorToTextureLinear", "stripTimeMs": 0.0047}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "Resize", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample1D_Nearest_Floor", "stripTimeMs": 0.0106}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample1D_Nearest_Ceil", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample1D_Linear_None", "stripTimeMs": 0.0046}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample2D_Nearest_Floor", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample2D_Nearest_Ceil", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample2D_Linear_None", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample3D_Nearest_Floor", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample3D_Nearest_Ceil", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample3D_Linear_None", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Resize1D_Nearest_Floor", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Resize1D_Nearest_Ceil", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Resize1D_Linear_None", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample1D_Nearest_Floor", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample1D_Nearest_Ceil", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample1D_Linear_None", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample2D_Nearest_Floor", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample2D_Nearest_Ceil", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample2D_Linear_None", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample3D_Nearest_Floor", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample3D_Nearest_Ceil", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample3D_Linear_None", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Resize1D_Nearest_Floor", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Resize1D_Nearest_Ceil", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Resize1D_Linear_None", "stripTimeMs": 0.0027}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "GemmT", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmT_XT_T8x8_R4x4", "stripTimeMs": 0.0105}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmT_WT_T8x8_R4x4", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmT_XT_WT_T8x8_R4x4", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmT_XT_T8x8_R4x4", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmT_WT_T8x8_R4x4", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmT_XT_WT_T8x8_R4x4", "stripTimeMs": 0.0035}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "ImageBased", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DepthToSpaceDepthColumnRow", "stripTimeMs": 0.0083}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DepthToSpaceColumnRowDepth", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SpaceToDepth", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DepthToSpaceDepthColumnRow", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DepthToSpaceColumnRowDepth", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SpaceToDepth", "stripTimeMs": 0.0022}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "ReferenceImpl", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.006900000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0032}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "ReduceIndices", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMaxFloatFirst", "stripTimeMs": 0.0092}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMinFloatFirst", "stripTimeMs": 0.005200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMaxFloatLast", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMinFloatLast", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMaxIntFirst", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMinIntFirst", "stripTimeMs": 0.0046}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMaxIntLast", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMinIntLast", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMaxFloatFirst", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMinFloatFirst", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMaxFloatLast", "stripTimeMs": 0.0055000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMinFloatLast", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMaxIntFirst", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMinIntFirst", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMaxIntLast", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMinIntLast", "stripTimeMs": 0.0041}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "IndexingOps", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: OneHot", "stripTimeMs": 0.007200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GatherND", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SliceSet", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: OneHot", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GatherND", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SliceSet", "stripTimeMs": 0.0023}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "AxisActivations", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LogSoftmaxEnd", "stripTimeMs": 0.006900000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SoftmaxEnd", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: HardmaxEnd", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LogSoftmaxEnd", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SoftmaxEnd", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: HardmaxEnd", "stripTimeMs": 0.0024000000000000002}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Normalization", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LayerNormalizationTail", "stripTimeMs": 0.0086}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RMSNormalizationTail", "stripTimeMs": 0.0051}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BatchNormalization", "stripTimeMs": 0.0049}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScaleBias", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LayerNormalizationTail", "stripTimeMs": 0.0047}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RMSNormalizationTail", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BatchNormalization", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScaleBias", "stripTimeMs": 0.0041}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Random", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RandomUniform", "stripTimeMs": 0.0083}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RandomNormal", "stripTimeMs": 0.0049}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BernoulliFloat", "stripTimeMs": 0.0057}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>lliInt", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TopP", "stripTimeMs": 0.0053}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RandomUniform", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RandomNormal", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BernoulliFloat", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>lliInt", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TopP", "stripTimeMs": 0.0038}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "NMS", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NMSBitmaskCorners", "stripTimeMs": 0.0064}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NMSBitmaskCenter", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NMSSelect", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NMSCompact", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NMSBitmaskCorners", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NMSBitmaskCenter", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NMSSelect", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NMSCompact", "stripTimeMs": 0.0023}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "ReferenceImpl.PadA", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PadBorderND", "stripTimeMs": 0.006500000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PadReflectND", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PadSymmetricND", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PadEdgeND", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PadWrapND", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PadBorderND", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PadReflectND", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PadSymmetricND", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PadEdgeND", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PadWrapND", "stripTimeMs": 0.0022}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "RNN", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LSTMEnd", "stripTimeMs": 0.007200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LSTMEnd", "stripTimeMs": 0.0033}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "CumSum", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumFloatForwardInclusive", "stripTimeMs": 0.0068000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumFloatReverseInclusive", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumFloatForwardExclusive", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumFloatReverseExclusive", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumIntForwardInclusive", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumIntReverseInclusive", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumIntForwardExclusive", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumIntReverseExclusive", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumFloatForwardInclusive", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumFloatReverseInclusive", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumFloatForwardExclusive", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumFloatReverseExclusive", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumIntForwardInclusive", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumIntReverseInclusive", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumIntForwardExclusive", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumIntReverseExclusive", "stripTimeMs": 0.0022}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "ScatterOps", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 10, "outputVariants": 10, "variantName": "Kernel: ScatterND", "stripTimeMs": 0.027200000000000002}, {"inputVariants": 10, "outputVariants": 10, "variantName": "Kernel: ScatterND", "stripTimeMs": 0.021400000000000002}]}]}, {"inputVariants": 38, "outputVariants": 38, "name": "Compute.Shaders.ReductionUnrolled.gen", "pipelines": [{"inputVariants": 38, "outputVariants": 38, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMaxFloat", "stripTimeMs": 0.0076}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMinFloat", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSumFloat", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSumSquareFloat", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMeanSquareFloat", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMeanFloat", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceProdFloat", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceL1Float", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceL2Float", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSqrtFloat", "stripTimeMs": 0.0049}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceLogSumFloat", "stripTimeMs": 0.0046}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceLogSumExpFloat", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSumExpFloat", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMaxInt", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMinInt", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSumInt", "stripTimeMs": 0.0055000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSumSquareInt", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceProdInt", "stripTimeMs": 0.0054}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceL1Int", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMaxFloat", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMinFloat", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSumFloat", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSumSquareFloat", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMeanSquareFloat", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMeanFloat", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceProdFloat", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceL1Float", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceL2Float", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSqrtFloat", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceLogSumFloat", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceLogSumExpFloat", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSumExpFloat", "stripTimeMs": 0.0054}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMaxInt", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMinInt", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSumInt", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSumSquareInt", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceProdInt", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceL1Int", "stripTimeMs": 0.0047}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "<PERSON><PERSON>", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Dense_T16x16_R4x4", "stripTimeMs": 0.0071}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Gemm_T16x16_R4x4", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Dense_T8x8_R4x4", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Gemm_T8x8_R4x4", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmBatched_T16x16_R4x4", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmBatched_T8x8_R4x4", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DenseBatched_T16x16_R4x4", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DenseBatched_T8x8_R4x4", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Dense_V_L1Cached64", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Gemm_V_L1Cached64", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmBatched_V_L1Cached64", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DenseBatched_V_L1Cached64", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Dense_T16x16_R4x4", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Gemm_T16x16_R4x4", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Dense_T8x8_R4x4", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Gemm_T8x8_R4x4", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmBatched_T16x16_R4x4", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmBatched_T8x8_R4x4", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DenseBatched_T16x16_R4x4", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DenseBatched_T8x8_R4x4", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Dense_V_L1Cached64", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Gemm_V_L1Cached64", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmBatched_V_L1Cached64", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DenseBatched_V_L1Cached64", "stripTimeMs": 0.0021000000000000003}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "ReferenceImpl.GenericA", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Transpose", "stripTimeMs": 0.0088}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: InstanceNormalizationTail", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Transpose", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: InstanceNormalizationTail", "stripTimeMs": 0.0023}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "BitonicSort", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: BitonicSortStep", "stripTimeMs": 0.0098}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: BitonicSortKeyStep", "stripTimeMs": 0.007500000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: BitonicSortStep", "stripTimeMs": 0.0103}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: BitonicSortKeyStep", "stripTimeMs": 0.0047}]}]}, {"inputVariants": 2304, "outputVariants": 2304, "name": "ConvGeneric", "pipelines": [{"inputVariants": 2304, "outputVariants": 2304, "pipeline": "", "variants": [{"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: Conv3D_Generic", "stripTimeMs": 0.2525}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: Conv2D_Generic", "stripTimeMs": 0.1918}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: Conv1D_Generic", "stripTimeMs": 0.19720000000000001}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: Conv3D_1x1_Generic", "stripTimeMs": 0.19390000000000002}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: Conv2D_1x1_Generic", "stripTimeMs": 0.1965}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: Conv1D_1x1_Generic", "stripTimeMs": 0.20020000000000002}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: ConvTranspose3D_Generic", "stripTimeMs": 0.20090000000000002}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: ConvTranspose2D_Generic", "stripTimeMs": 0.1937}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: ConvTranspose1D_Generic", "stripTimeMs": 0.1945}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: ConvTranspose3D_1x1_Generic", "stripTimeMs": 0.1952}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: ConvTranspose2D_1x1_Generic", "stripTimeMs": 0.19720000000000001}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: ConvTranspose1D_1x1_Generic", "stripTimeMs": 0.1955}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: Conv3D_Generic", "stripTimeMs": 0.1928}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: Conv2D_Generic", "stripTimeMs": 0.276}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: Conv1D_Generic", "stripTimeMs": 0.2099}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: Conv3D_1x1_Generic", "stripTimeMs": 0.1963}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: Conv2D_1x1_Generic", "stripTimeMs": 0.1935}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: Conv1D_1x1_Generic", "stripTimeMs": 0.1917}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: ConvTranspose3D_Generic", "stripTimeMs": 0.1933}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: ConvTranspose2D_Generic", "stripTimeMs": 0.3009}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: ConvTranspose1D_Generic", "stripTimeMs": 0.1991}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: ConvTranspose3D_1x1_Generic", "stripTimeMs": 0.1953}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: ConvTranspose2D_1x1_Generic", "stripTimeMs": 0.1937}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: ConvTranspose1D_1x1_Generic", "stripTimeMs": 0.191}]}]}, {"inputVariants": 132, "outputVariants": 132, "name": "Compute.Shaders.Broadcast.gen", "pipelines": [{"inputVariants": 132, "outputVariants": 132, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastPRelu", "stripTimeMs": 0.016800000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastPRelu", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwisePRelu", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastPowFloatFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastPowFloatFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwisePowFloatFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastPowFloatInt", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastPowFloatInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwisePowFloatInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastAddFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastAddFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseAddFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastSubFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastSubFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseSubFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMulFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMulFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMulFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastDivFloat", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastDivFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseDivFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMinFloat", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMinFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMinFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMaxFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMaxFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMaxFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMeanFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMeanFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMeanFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastModFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastModFloat", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseModFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastFModFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastFModFloat", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseFModFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastPowIntFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastPowIntFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwisePowIntFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastPowIntInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastPowIntInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwisePowIntInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastAddInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastAddInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseAddInt", "stripTimeMs": 0.002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastSubInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastSubInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseSubInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMulInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMulInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMulInt", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastDivInt", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastDivInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseDivInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMinInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMinInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMinInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMaxInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMaxInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMaxInt", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastModInt", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastModInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseModInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastFModInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastFModInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseFModInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastPRelu", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastPRelu", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwisePRelu", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastPowFloatFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastPowFloatFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwisePowFloatFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastPowFloatInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastPowFloatInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwisePowFloatInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastAddFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastAddFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseAddFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastSubFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastSubFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseSubFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMulFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMulFloat", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMulFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastDivFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastDivFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseDivFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMinFloat", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMinFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMinFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMaxFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMaxFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMaxFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMeanFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMeanFloat", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMeanFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastModFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastModFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseModFloat", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastFModFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastFModFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseFModFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastPowIntFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastPowIntFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwisePowIntFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastPowIntInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastPowIntInt", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwisePowIntInt", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastAddInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastAddInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseAddInt", "stripTimeMs": 0.0057}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastSubInt", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastSubInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseSubInt", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMulInt", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMulInt", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMulInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastDivInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastDivInt", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseDivInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMinInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMinInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMinInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMaxInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMaxInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMaxInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastModInt", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastModInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseModInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastFModInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastFModInt", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseFModInt", "stripTimeMs": 0.0023}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "SortingOps", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: TopK", "stripTimeMs": 0.0247}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: TopK", "stripTimeMs": 0.0109}]}]}, {"inputVariants": 18, "outputVariants": 18, "name": "CopyOps", "pipelines": [{"inputVariants": 18, "outputVariants": 18, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Split", "stripTimeMs": 0.010700000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MemCopy", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MemCopyStride", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MemSet", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Transpose2D", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CastHalfToFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DequantizeUint8", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Split", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MemCopy", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MemCopyStride", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MemSet", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Transpose2D", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CastHalfToFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DequantizeUint8", "stripTimeMs": 0.0022}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "TextureToTensor", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TextureToTensorExact", "stripTimeMs": 0.0128}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TextureToTensorLinear", "stripTimeMs": 0.009600000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TextureToTensorExact", "stripTimeMs": 0.0077}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TextureToTensorLinear", "stripTimeMs": 0.007200000000000001}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "DepthwiseConv", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: DepthwiseConv2DWinograd", "stripTimeMs": 0.0142}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KernelWinoExpand", "stripTimeMs": 0.009000000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: DepthwiseConv2DDirect", "stripTimeMs": 0.0088}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: DepthwiseConv2DWinograd", "stripTimeMs": 0.008400000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KernelWinoExpand", "stripTimeMs": 0.008}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: DepthwiseConv2DDirect", "stripTimeMs": 0.0089}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Compute.Shaders.ConvTranspose.gen", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ConvTranspose2D_KxK", "stripTimeMs": 0.0092}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ConvTranspose1D_KxK", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ConvTranspose2D_KxK", "stripTimeMs": 0.0044}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ConvTranspose1D_KxK", "stripTimeMs": 0.0045000000000000005}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Pool", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AveragePoolReduce", "stripTimeMs": 0.0064}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalAveragePool", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MaxPoolReduce", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalMaxPool", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AverageVariancePoolReduce", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalAverageVariancePool", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMaxReduce", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalArgMaxReduce", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AveragePoolReduce", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalAveragePool", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MaxPoolReduce", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalMaxPool", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AverageVariancePoolReduce", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalAverageVariancePool", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMaxReduce", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalArgMaxReduce", "stripTimeMs": 0.0022}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "ReferenceImpl.Logical", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Where", "stripTimeMs": 0.0067}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Where", "stripTimeMs": 0.0031000000000000003}]}]}, {"inputVariants": 110, "outputVariants": 110, "name": "Compute.Shaders.PointwiseUnary.gen", "pipelines": [{"inputVariants": 110, "outputVariants": 110, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LeakyRelu", "stripTimeMs": 0.0085}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>sh", "stripTimeMs": 0.005200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0076}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Relu6", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0046}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GeluFast", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: HardSigmoid", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON>nk", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ThresholdedRelu", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Softplus", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0047}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Floor", "stripTimeMs": 0.0053}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Round", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Reciprocal", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Exp", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Log", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Sqrt", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Acos", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Acosh", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Cos", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Sin", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON>", "stripTimeMs": 0.0057}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Softsign", "stripTimeMs": 0.0059}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: HardSwish", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AbsInt", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AbsFloat", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NegInt", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NegFloat", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SquareInt", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SquareFloat", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: IsNaN", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CastIntToFloat", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CastFloatToInt", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignFloat", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignInt", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Not", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClipFloat", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClipInt", "stripTimeMs": 0.0054}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarMadFloat", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarMadInt", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RangeFloat", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RangeInt", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LeakyRelu", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>sh", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Relu6", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GeluFast", "stripTimeMs": 0.0055000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: HardSigmoid", "stripTimeMs": 0.005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0063}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON>nk", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ThresholdedRelu", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Softplus", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Floor", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Round", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Reciprocal", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Exp", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Log", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Sqrt", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Acos", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Acosh", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Cos", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Sin", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0054}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON>", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Softsign", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: HardSwish", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AbsInt", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AbsFloat", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NegInt", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NegFloat", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SquareInt", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SquareFloat", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: IsNaN", "stripTimeMs": 0.0047}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CastIntToFloat", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CastFloatToInt", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignFloat", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignInt", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Not", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClipFloat", "stripTimeMs": 0.0054}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClipInt", "stripTimeMs": 0.008}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarMadFloat", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarMadInt", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RangeFloat", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RangeInt", "stripTimeMs": 0.0039000000000000003}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "ReferenceImpl.PoolA", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MaxPool2D", "stripTimeMs": 0.0118}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AveragePool2D", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MaxPool1D", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AveragePool1D", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MaxPool2D", "stripTimeMs": 0.0046}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AveragePool2D", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MaxPool1D", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AveragePool1D", "stripTimeMs": 0.0022}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "ReferenceImpl.Einsum", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EinsumOne", "stripTimeMs": 0.0071}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EinsumTwo", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EinsumOne", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EinsumTwo", "stripTimeMs": 0.0023}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "LogicalOps", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: OrInt", "stripTimeMs": 0.008700000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AndInt", "stripTimeMs": 0.0053}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: XorInt", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: IsInf", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: OrInt", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AndInt", "stripTimeMs": 0.0058000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: XorInt", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: IsInf", "stripTimeMs": 0.005}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Compute.Shaders.Conv.gen", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: Conv2D_KxK", "stripTimeMs": 0.012}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: Conv2D_1x1", "stripTimeMs": 0.008400000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: Conv1D_KxK", "stripTimeMs": 0.0073}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: Conv1D_1x1", "stripTimeMs": 0.007500000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: Conv2D_KxK", "stripTimeMs": 0.0082}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: Conv2D_1x1", "stripTimeMs": 0.0079}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: Conv1D_KxK", "stripTimeMs": 0.0073}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: Conv1D_1x1", "stripTimeMs": 0.008}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "RoiAlign", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: RoiAlign", "stripTimeMs": 0.0145}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: RoiAlign", "stripTimeMs": 0.0092}]}]}, {"inputVariants": 48, "outputVariants": 48, "name": "GridSample", "pipelines": [{"inputVariants": 48, "outputVariants": 48, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: GridSample2D", "stripTimeMs": 0.0315}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: GridSample3D", "stripTimeMs": 0.0258}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: GridSample2D", "stripTimeMs": 0.024}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: GridSample3D", "stripTimeMs": 0.024}]}]}, {"inputVariants": 76, "outputVariants": 76, "name": "Compute.Shaders.Reduction.gen", "pipelines": [{"inputVariants": 76, "outputVariants": 76, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMaxFloat", "stripTimeMs": 0.0082}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMaxFloat", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMinFloat", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMinFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSumFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSumFloat", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSumSquareFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSumSquareFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMeanSquareFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMeanSquareFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMeanFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMeanFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceProdFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceProdFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceL1Float", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceL1Float", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceL2Float", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceL2Float", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSqrtFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSqrtFloat", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceLogSumFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceLogSumFloat", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceLogSumExpFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceLogSumExpFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSumExpFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSumExpFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMaxInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMaxInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMinInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMinInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSumInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSumInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSumSquareInt", "stripTimeMs": 0.002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSumSquareInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceProdInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceProdInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceL1Int", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceL1Int", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMaxFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMaxFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMinFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMinFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSumFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSumFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSumSquareFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSumSquareFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMeanSquareFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMeanSquareFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMeanFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMeanFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceProdFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceProdFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceL1Float", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceL1Float", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceL2Float", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceL2Float", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSqrtFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSqrtFloat", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceLogSumFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceLogSumFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceLogSumExpFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceLogSumExpFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSumExpFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSumExpFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMaxInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMaxInt", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMinInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMinInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSumInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSumInt", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSumSquareInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSumSquareInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceProdInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceProdInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceL1Int", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceL1Int", "stripTimeMs": 0.0022}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "CompareOps", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GreaterFloat", "stripTimeMs": 0.0118}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GreaterInt", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GreaterOrEqualFloat", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GreaterOrEqualInt", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LessFloat", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LessInt", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LessOrEqualFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LessOrEqualInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EqualFloat", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EqualInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GreaterFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GreaterInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GreaterOrEqualFloat", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GreaterOrEqualInt", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LessFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LessInt", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LessOrEqualFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LessOrEqualInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EqualFloat", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EqualInt", "stripTimeMs": 0.0022}]}]}]}