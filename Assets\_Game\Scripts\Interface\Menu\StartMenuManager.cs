using UnityEngine;
using UnityEngine.UIElements;
using System.Collections.Generic;
using System.Linq;
using System.Collections;

public class StartMenuManager : BaseMenuManager
{
    // Store the original title text to restore it later
    private string originalTitle = "";

    // Loading state management
    private Button continueButton;
    private bool isLoading = false;
    private string originalContinueText = "Continue";
    private Coroutine pulseCoroutine;

    // Fade transition system
    [Header("Fade Transition")]
    [SerializeField] private float fadeFromBlackDuration = 1.0f;
    [SerializeField] private bool enableFadeFromBlack = true;
    private VisualElement fadeOverlay;
    private bool hasFadedIn = false;

    protected override void Awake()
    {
        base.Awake();
        ValidateRequiredComponents();
    }

    protected override void Start()
    {
        base.Start();

        // Store the original title text
        if (titleLabel != null)
        {
            originalTitle = titleLabel.text;
        }

        SetupStartMenuButtons();

        UnityEngine.Cursor.visible = true;
        UnityEngine.Cursor.lockState = CursorLockMode.None;

        // Show main menu buttons
        ShowStartMenu();

        // Start fade-in transition after UI is set up
        if (enableFadeFromBlack && !hasFadedIn)
        {
            StartCoroutine(FadeFromBlack());
        }

    }

    protected override void InitializeUIElements()
    {
        base.InitializeUIElements();

        // Set up menu container visibility
        if (menuContainer != null)
        {
            menuContainer.style.display = DisplayStyle.Flex;
        }

        // Create fade overlay for smooth transition from BootLoader
        CreateFadeOverlay();
    }

    private void OnEnable()
    {
        // Ensure cursor visible when menu is enabled in single scene
        UnityEngine.Cursor.visible = true;
        UnityEngine.Cursor.lockState = CursorLockMode.None;

        // Block player UI overlays
        var toolUI = FindFirstObjectByType<ToolSelectionManager>();
        if (toolUI != null)
        {
            toolUI.ForceHideUI();
        }

        // If this GameObject was disabled during gameplay, UIDocument's panel/root
        // gets recreated when re-enabled. Refresh references and rebind UI if needed.
        if (uiDocument == null)
        {
            uiDocument = GetComponent<UIDocument>();
        }

        var currentRoot = uiDocument != null ? uiDocument.rootVisualElement : null;
        if (currentRoot != null && currentRoot != root)
        {
            // Clear previous bindings tied to the old visual tree
            CleanupButtons();
            CleanupSettingsEvents();

            // Re-initialize UI hierarchy and settings, then rebuild menu buttons
            InitializeUIElements();
            InitializeSettings();
            SetupStartMenuButtons();
            ShowStartMenu();

            // Only fade in if we haven't done so already
            if (enableFadeFromBlack && !hasFadedIn)
            {
                StartCoroutine(FadeFromBlack());
            }
        }

        // Safety: some Unity versions construct UIDocument root after OnEnable.
        // Recheck next frame and rebind if still needed.
        StartCoroutine(RebindIfNeededNextFrame());
    }

    private System.Collections.IEnumerator RebindIfNeededNextFrame()
    {
        yield return null;
        if (uiDocument == null)
        {
            uiDocument = GetComponent<UIDocument>();
        }
        var currentRoot = uiDocument != null ? uiDocument.rootVisualElement : null;
        if (currentRoot != null && currentRoot != root)
        {
            CleanupButtons();
            CleanupSettingsEvents();
            InitializeUIElements();
            InitializeSettings();
            SetupStartMenuButtons();
            ShowStartMenu();

            // Only fade in if we haven't done so already
            if (enableFadeFromBlack && !hasFadedIn)
            {
                StartCoroutine(FadeFromBlack());
            }
        }
    }

    private void SetupStartMenuButtons()
    {
        // Clear existing menu buttons but preserve settings buttons
        var existingSettingsButtons = settingsButtons.ToList();
        menuButtons.Clear();
        settingsButtons = existingSettingsButtons;

        // Setup main menu buttons using our local button setup helper
        SetupButton("continue-button", ContinueGame);
        SetupButton("settings-button", OpenSettings);
        SetupButton("credits-button", ShowCredits);
        SetupButton("quit-game-button", QuitGame);

        // Capture continue button reference for loading state
        continueButton = root.Q<Button>("continue-button");
        if (continueButton != null)
        {
            originalContinueText = continueButton.text;
        }

        // Ensure buttons are visible
        ShowStartMenu();
    }

    protected override void ShowStartMenu()
    {
        base.ShowStartMenu();
        
        // Restore the original title when returning from settings
        if (titleLabel != null)
        {
            titleLabel.text = originalTitle;
        }
    }

    protected override void ShowSettingsMenu()
    {
        base.ShowSettingsMenu();
        if (titleLabel != null)
        {
            titleLabel.text = "Settings";
        }
    }

    private void ValidateRequiredComponents()
    {
        if (GameFlowManager.Instance == null)
        {
            Debug.LogError("GameFlowManager singleton instance is not available in StartMenuManager");
        }
    }

    private void ContinueGame()
    {
        if (isLoading)
        {
            return; // Prevent multiple clicks during loading
        }

        if (GameFlowManager.Instance == null)
        {
            Debug.LogError("Cannot continue game: GameFlowManager is null");
            return;
        }

        // Start loading process
        StartCoroutine(LoadGameplayCoroutine());
    }

    private IEnumerator LoadGameplayCoroutine()
    {
        isLoading = true;

        // Start loading animation
        StartLoadingAnimation();

        // Initialize persistence manager while staying in menu
        if (PersistenceManager.Instance != null)
        {
            Debug.Log("[StartMenu] Starting deferred initialization...");
            Debug.Log($"[StartMenu] Initial IsInitializationComplete: {PersistenceManager.Instance.IsInitializationComplete}");

            // Start the initialization
            PersistenceManager.Instance.InitializeForGameplay();

            // Wait for actual completion instead of arbitrary time
            Debug.Log("[StartMenu] Waiting for initialization to complete...");
            int waitFrames = 0;
            while (PersistenceManager.Instance != null && !PersistenceManager.Instance.IsInitializationComplete && waitFrames < 300)
            {
                waitFrames++;
                yield return null;
            }

            Debug.Log($"[StartMenu] Initialization complete after {waitFrames} frames!");
        }
        else
        {
            Debug.LogError("[StartMenu] PersistenceManager.Instance is null!");
        }

        // Stop loading animation immediately - no artificial delays
        Debug.Log("[StartMenu] Stopping loading animation...");
        StopLoadingAnimation();

        Debug.Log("[StartMenu] Transitioning to gameplay...");

        // Now transition to gameplay
        GameFlowManager.Instance.EnterGameplay();

        isLoading = false;
        Debug.Log("[StartMenu] Loading process complete!");
    }

    private void StartLoadingAnimation()
    {
        if (continueButton != null)
        {
            continueButton.text = "Loading...";
            continueButton.AddToClassList("loading-pulse");
            continueButton.SetEnabled(false);

            // Start pulsing animation
            if (pulseCoroutine != null)
            {
                StopCoroutine(pulseCoroutine);
            }
            pulseCoroutine = StartCoroutine(PulseAnimation());
        }
    }

    #region Fade Transition System

    private void CreateFadeOverlay()
    {
        if (!enableFadeFromBlack || root == null) return;

        // Create a black overlay that covers the entire screen
        fadeOverlay = new VisualElement();
        fadeOverlay.name = "fade-overlay";

        // Style the overlay to cover the entire screen
        fadeOverlay.style.position = Position.Absolute;
        fadeOverlay.style.top = 0;
        fadeOverlay.style.left = 0;
        fadeOverlay.style.right = 0;
        fadeOverlay.style.bottom = 0;
        fadeOverlay.style.backgroundColor = Color.black;
        fadeOverlay.style.opacity = 1.0f; // Start fully opaque

        // Add to root as the topmost element
        root.Add(fadeOverlay);

        Debug.Log("[StartMenu] Created fade overlay for smooth transition");
    }

    private IEnumerator FadeFromBlack()
    {
        if (fadeOverlay == null)
        {
            Debug.LogWarning("[StartMenu] Fade overlay not found, skipping fade transition");
            yield break;
        }

        Debug.Log("[StartMenu] Starting fade from black transition");

        // Wait a brief moment to ensure everything is initialized
        yield return new WaitForSeconds(0.1f);

        float elapsed = 0f;
        float startOpacity = 1.0f;

        while (elapsed < fadeFromBlackDuration)
        {
            elapsed += Time.unscaledDeltaTime;
            float t = elapsed / fadeFromBlackDuration;

            // Use smooth curve for more natural fade
            float opacity = Mathf.Lerp(startOpacity, 0f, Mathf.SmoothStep(0f, 1f, t));
            fadeOverlay.style.opacity = opacity;

            yield return null;
        }

        // Ensure fully transparent and remove overlay
        fadeOverlay.style.opacity = 0f;
        fadeOverlay.RemoveFromHierarchy();
        fadeOverlay = null;
        hasFadedIn = true;

        Debug.Log("[StartMenu] Fade from black transition complete");
    }

    #endregion

    private void StopLoadingAnimation()
    {
        Debug.Log("[StartMenu] StopLoadingAnimation called");
        if (continueButton != null)
        {
            Debug.Log("[StartMenu] Restoring button text and state");
            continueButton.text = originalContinueText;
            continueButton.RemoveFromClassList("loading-pulse");
            continueButton.SetEnabled(true);

            // Stop pulsing animation
            if (pulseCoroutine != null)
            {
                Debug.Log("[StartMenu] Stopping pulse coroutine");
                StopCoroutine(pulseCoroutine);
                pulseCoroutine = null;
            }
        }
        else
        {
            Debug.LogError("[StartMenu] continueButton is null in StopLoadingAnimation!");
        }
        Debug.Log("[StartMenu] StopLoadingAnimation complete");
    }

    private IEnumerator PulseAnimation()
    {
        while (isLoading && continueButton != null)
        {
            // Pulse the text between "Loading..." and "Loading"
            continueButton.text = "Loading...";
            yield return new WaitForSeconds(0.5f);

            if (isLoading && continueButton != null)
            {
                continueButton.text = "Loading";
                yield return new WaitForSeconds(0.5f);
            }
        }
    }

    private void ShowCredits()
    {
        Debug.Log("Credits not yet implemented");
    }

    private void QuitGame()
    {
        if (GameFlowManager.Instance != null)
        {
            GameFlowManager.Instance.QuitGame();
        }
        else
        {
#if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
#else
            Application.Quit();
#endif
        }
    }



    protected override void OnDestroy()
    {
        // Clean up loading animation
        if (pulseCoroutine != null)
        {
            StopCoroutine(pulseCoroutine);
            pulseCoroutine = null;
        }

        base.OnDestroy();
    }
}