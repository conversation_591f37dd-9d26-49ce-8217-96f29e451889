{"context": {"projectPath": "C:/Unity/BLAME/BLAME/Packages", "unityVersion": "6000.2.0f1"}, "inputs": ["C:\\Unity\\BLAME\\BLAME\\Packages\\manifest.json", "C:\\Unity\\BLAME\\BLAME\\Packages\\packages-lock.json", "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\BuiltInPackagesCombined.sha1"], "outputs": {"com.unity.2d.enhancers@1.0.0": {"name": "com.unity.2d.enhancers", "displayName": "2D Enhancers", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.2d.enhancers@1df0eb7756ab", "fingerprint": "1df0eb7756ab7f6eaea96cdc2885716e0714ea21", "editorCompatibility": "6000.2.0a9", "version": "1.0.0", "source": "registry", "testable": false}, "com.unity.2d.sprite@1.0.0": {"name": "com.unity.2d.sprite", "displayName": "2D Sprite", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.2d.sprite@28296e5d02fb", "fingerprint": "28296e5d02fbf3ac81cc11ab1b39667f16a80778", "editorCompatibility": "2019.2.0a1", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.ai.assistant@1.0.0-pre.12": {"name": "com.unity.ai.assistant", "displayName": "Assistant", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.ai.assistant@91c166a13c3b", "fingerprint": "91c166a13c3bef5814c5a13a69fa442a657dd539", "editorCompatibility": "6000.2.0b4", "version": "1.0.0-pre.12", "source": "registry", "testable": false}, "com.unity.ai.generators@1.0.0-pre.19": {"name": "com.unity.ai.generators", "displayName": "Generators", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.ai.generators@49eb4eaccb48", "fingerprint": "49eb4eaccb48853a1ca7a7f6853ae94c01f07ff3", "editorCompatibility": "6000.2.0a1", "version": "1.0.0-pre.19", "source": "registry", "testable": false}, "com.unity.ai.inference@2.3.0": {"name": "com.unity.ai.inference", "displayName": "Inference Engine", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.ai.inference@4ac711cab9a3", "fingerprint": "4ac711cab9a36baa41c7c65b01461f91c0537337", "editorCompatibility": "6000.0.0a1", "version": "2.3.0", "source": "registry", "testable": false}, "com.unity.collab-proxy@2.8.2": {"name": "com.unity.collab-proxy", "displayName": "Version Control", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f", "fingerprint": "c854d1f7d97fbe1905f3e3591ded6fe77d96e654", "editorCompatibility": "2021.3.0f1", "version": "2.8.2", "source": "registry", "testable": false}, "com.unity.feature.development@1.0.2": {"name": "com.unity.feature.development", "displayName": "Engineering", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.feature.development@767aadbc6eb7", "fingerprint": "767aadbc6eb72681a4ca807c8fa248e0230a0cef", "version": "1.0.2", "source": "builtin", "testable": false}, "com.unity.formats.fbx@5.1.3": {"name": "com.unity.formats.fbx", "displayName": "FBX Exporter", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.formats.fbx@db39de05b0db", "fingerprint": "db39de05b0dbacefb3aa3035d97d497649e2e711", "editorCompatibility": "2020.3.0a1", "version": "5.1.3", "source": "registry", "testable": false}, "com.unity.multiplayer.center@1.0.0": {"name": "com.unity.multiplayer.center", "displayName": "Multiplayer Center", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.multiplayer.center@f3fb577b3546", "fingerprint": "f3fb577b3546594b97b8cc34307cd621f60f1c73", "editorCompatibility": "6000.0.0a1", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.probuilder@6.0.6": {"name": "com.unity.probuilder", "displayName": "ProBuilder", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.probuilder@c19679f07bae", "fingerprint": "c19679f07bae845d35ba92f10a1bb2eb889fb607", "editorCompatibility": "6000.0.0a1", "version": "6.0.6", "source": "registry", "testable": false}, "com.unity.progrids@3.0.3-preview.6": {"name": "com.unity.progrids", "displayName": "ProGrids", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.progrids@36b0033bf980", "fingerprint": "36b0033bf980d263ff6d1d156245d91e6e92f6f9", "editorCompatibility": "2018.1.0a1", "version": "3.0.3-preview.6", "source": "registry", "testable": false}, "com.unity.project-auditor@1.0.1": {"name": "com.unity.project-auditor", "displayName": "Project Auditor", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.project-auditor@94c6e4e98816", "fingerprint": "94c6e4e9881619d6fd9744d93bd32bec6a2d676c", "editorCompatibility": "2021.3.0a1", "version": "1.0.1", "source": "registry", "testable": false}, "com.unity.recorder@5.1.2": {"name": "com.unity.recorder", "displayName": "Recorder", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.recorder@979a3db2a781", "fingerprint": "979a3db2a7818d9c4852c886631b01e394d80af0", "editorCompatibility": "2023.1.1f1", "version": "5.1.2", "source": "registry", "testable": false}, "com.unity.render-pipelines.high-definition@17.2.0": {"name": "com.unity.render-pipelines.high-definition", "displayName": "High Definition Render Pipeline", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe", "fingerprint": "28765669b6fee79d765751f370129f832b2c30c4", "editorCompatibility": "6000.2.0a1", "version": "17.2.0", "source": "builtin", "testable": false}, "com.unity.timeline@1.8.7": {"name": "com.unity.timeline", "displayName": "Timeline", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.timeline@c58b4ee65782", "fingerprint": "c58b4ee65782ad38338e29f7ee67787cb6998f04", "editorCompatibility": "2019.3.0a1", "version": "1.8.7", "source": "registry", "testable": false}, "com.unity.ugui@2.0.0": {"name": "com.unity.ugui", "displayName": "Unity UI", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.ugui@423bc642aff1", "fingerprint": "423bc642aff1ba4fed4feef1d7b45461138cbb32", "editorCompatibility": "2019.2.0a1", "version": "2.0.0", "source": "builtin", "testable": false}, "com.unity.visualscripting@1.9.7": {"name": "com.unity.visualscripting", "displayName": "Visual Scripting", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485", "fingerprint": "6279e2b7c4858e56cca7f367cd38c49ef66778c9", "editorCompatibility": "2021.3.0a1", "version": "1.9.7", "source": "registry", "testable": false}, "com.unity.modules.accessibility@1.0.0": {"name": "com.unity.modules.accessibility", "displayName": "Accessibility", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.accessibility", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.ai@1.0.0": {"name": "com.unity.modules.ai", "displayName": "AI", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.ai", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.androidjni@1.0.0": {"name": "com.unity.modules.androidjni", "displayName": "Android JNI", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.androidjni", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.animation@1.0.0": {"name": "com.unity.modules.animation", "displayName": "Animation", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.animation", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.assetbundle@1.0.0": {"name": "com.unity.modules.assetbundle", "displayName": "<PERSON><PERSON>", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.assetbundle", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.audio@1.0.0": {"name": "com.unity.modules.audio", "displayName": "Audio", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.audio", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.cloth@1.0.0": {"name": "com.unity.modules.cloth", "displayName": "<PERSON><PERSON><PERSON>", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.cloth", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.director@1.0.0": {"name": "com.unity.modules.director", "displayName": "Director", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.director", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.imageconversion@1.0.0": {"name": "com.unity.modules.imageconversion", "displayName": "Image Conversion", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.imageconversion", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.imgui@1.0.0": {"name": "com.unity.modules.imgui", "displayName": "IMGUI", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.imgui", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.jsonserialize@1.0.0": {"name": "com.unity.modules.jsonserialize", "displayName": "JSONSerialize", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.jsonserialize", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.particlesystem@1.0.0": {"name": "com.unity.modules.particlesystem", "displayName": "Particle System", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.particlesystem", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.physics@1.0.0": {"name": "com.unity.modules.physics", "displayName": "Physics", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.physics", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.physics2d@1.0.0": {"name": "com.unity.modules.physics2d", "displayName": "Physics 2D", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.physics2d", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.screencapture@1.0.0": {"name": "com.unity.modules.screencapture", "displayName": "Screen Capture", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.screencapture", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.terrain@1.0.0": {"name": "com.unity.modules.terrain", "displayName": "Terrain", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.terrain", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.terrainphysics@1.0.0": {"name": "com.unity.modules.terrainphysics", "displayName": "Terrain Physics", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.terrainphysics", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.tilemap@1.0.0": {"name": "com.unity.modules.tilemap", "displayName": "Tilemap", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.tilemap", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.ui@1.0.0": {"name": "com.unity.modules.ui", "displayName": "UI", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.ui", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.uielements@1.0.0": {"name": "com.unity.modules.uielements", "displayName": "UIElements", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.uielements", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.umbra@1.0.0": {"name": "com.unity.modules.umbra", "displayName": "Umbra", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.umbra", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unityanalytics@1.0.0": {"name": "com.unity.modules.unityanalytics", "displayName": "Unity Analytics", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unityanalytics", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequest@1.0.0": {"name": "com.unity.modules.unitywebrequest", "displayName": "Unity Web Request", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequest", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequestassetbundle@1.0.0": {"name": "com.unity.modules.unitywebrequestassetbundle", "displayName": "Unity Web Request Asset Bundle", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequestassetbundle", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequestaudio@1.0.0": {"name": "com.unity.modules.unitywebrequestaudio", "displayName": "Unity Web Request Audio", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequestaudio", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequesttexture@1.0.0": {"name": "com.unity.modules.unitywebrequesttexture", "displayName": "Unity Web Request Texture", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequesttexture", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequestwww@1.0.0": {"name": "com.unity.modules.unitywebrequestwww", "displayName": "Unity Web Request WWW", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequestwww", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.vehicles@1.0.0": {"name": "com.unity.modules.vehicles", "displayName": "Vehicles", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.vehicles", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.video@1.0.0": {"name": "com.unity.modules.video", "displayName": "Video", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.video", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.vr@1.0.0": {"name": "com.unity.modules.vr", "displayName": "VR", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.vr", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.wind@1.0.0": {"name": "com.unity.modules.wind", "displayName": "Wind", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.wind", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.xr@1.0.0": {"name": "com.unity.modules.xr", "displayName": "XR", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.xr", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.subsystems@1.0.0": {"name": "com.unity.modules.subsystems", "displayName": "Subsystems", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.subsystems", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.hierarchycore@1.0.0": {"name": "com.unity.modules.hierarchycore", "displayName": "Hierarchy Core", "resolvedPath": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.hierarchycore", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.render-pipelines.core@17.2.0": {"name": "com.unity.render-pipelines.core", "displayName": "Scriptable Render Pipeline Core", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.render-pipelines.core@bd0e8186c2bc", "fingerprint": "bd0e8186c2bcfea109ee5981b813a1b39a6382fb", "editorCompatibility": "6000.2.0a1", "version": "17.2.0", "source": "builtin", "testable": false}, "com.unity.shadergraph@17.2.0": {"name": "com.unity.shadergraph", "displayName": "Shader Graph", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.shadergraph@f8b69e83dfdd", "fingerprint": "f8b69e83dfddc9d7bbdcb1b4c60f079ffee88676", "editorCompatibility": "6000.2.0a1", "version": "17.2.0", "source": "builtin", "testable": false}, "com.unity.visualeffectgraph@17.2.0": {"name": "com.unity.visualeffectgraph", "displayName": "Visual Effect Graph", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.visualeffectgraph@c8dcb84572f2", "fingerprint": "c8dcb84572f2d0cfeef21465bb35296970fa0aa9", "editorCompatibility": "6000.2.0a1", "version": "17.2.0", "source": "builtin", "testable": false}, "com.unity.render-pipelines.high-definition-config@17.2.0": {"name": "com.unity.render-pipelines.high-definition-config", "displayName": "High Definition Render Pipeline Config", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.render-pipelines.high-definition-config@f7c893e8c254", "fingerprint": "f7c893e8c254a6aa8b789a59d79275671ed7d3b9", "editorCompatibility": "6000.2.0a1", "version": "17.2.0", "source": "builtin", "testable": false}, "com.unity.collections@2.5.7": {"name": "com.unity.collections", "displayName": "Collections", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.collections@d49facba0036", "fingerprint": "d49facba0036aa0e1508296262f9d93b44d1ab3b", "editorCompatibility": "2022.3.20f1", "version": "2.5.7", "source": "registry", "testable": false}, "com.unity.bindings.openimageio@1.0.0": {"name": "com.unity.bindings.openimageio", "displayName": "OpenImageIO Bindings", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.bindings.openimageio@3229d2aa5c76", "fingerprint": "3229d2aa5c76c70269dc58c79d5864ec84fa4a74", "editorCompatibility": "2023.1.0a1", "version": "1.0.0", "source": "registry", "testable": false}, "com.unity.nuget.mono-cecil@1.11.5": {"name": "com.unity.nuget.mono-cecil", "displayName": "Mono Cecil", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.nuget.mono-cecil@d78732e851eb", "fingerprint": "d78732e851eb42273c8e4d97993d3a94b207e570", "editorCompatibility": "2019.4.0a1", "version": "1.11.5", "source": "registry", "testable": false}, "com.unity.nuget.newtonsoft-json@3.2.1": {"name": "com.unity.nuget.newtonsoft-json", "displayName": "Newtonsoft Json", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.nuget.newtonsoft-json@74deb55db2a0", "fingerprint": "74deb55db2a0c29ddfda576608bcb86abbd13ee6", "editorCompatibility": "2018.4.0a1", "version": "3.2.1", "source": "registry", "testable": false}, "com.unity.settings-manager@2.1.0": {"name": "com.unity.settings-manager", "displayName": "Settings Manager", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.settings-manager@41738c275190", "fingerprint": "41738c27519039c335849eb78949382f4d7a3544", "editorCompatibility": "2022.3.0a1", "version": "2.1.0", "source": "registry", "testable": false}, "com.autodesk.fbx@5.1.1": {"name": "com.autodesk.fbx", "displayName": "Autodesk FBX SDK for Unity", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7", "fingerprint": "5797ff6b31c7c66189bffe1adc9cbf0ad5a9cbbf", "editorCompatibility": "2020.3.0a1", "version": "5.1.1", "source": "registry", "testable": false}, "com.unity.ide.visualstudio@2.0.23": {"name": "com.unity.ide.visualstudio", "displayName": "Visual Studio Editor", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.ide.visualstudio@198cdf337d13", "fingerprint": "198cdf337d13c83ca953581515630d66b779e92b", "editorCompatibility": "2019.4.25f1", "version": "2.0.23", "source": "registry", "testable": false}, "com.unity.ide.rider@3.0.36": {"name": "com.unity.ide.rider", "displayName": "JetBrains Rider Editor", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.ide.rider@4d374c7eb6db", "fingerprint": "4d374c7eb6db6907c7e6925e3086c3c73f926e13", "editorCompatibility": "2019.4.6f1", "version": "3.0.36", "source": "registry", "testable": false}, "com.unity.editorcoroutines@1.0.0": {"name": "com.unity.editorcoroutines", "displayName": "Editor Coroutines", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.editorcoroutines@7d48783e7b8c", "fingerprint": "7d48783e7b8cfcee5f8ef9ba787ed0d9dad4ebca", "editorCompatibility": "2018.1.0a1", "version": "1.0.0", "source": "registry", "testable": false}, "com.unity.performance.profile-analyzer@1.2.3": {"name": "com.unity.performance.profile-analyzer", "displayName": "Profile Analyzer", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.performance.profile-analyzer@a68e7bc84997", "fingerprint": "a68e7bc849973d943853204178d08a2bc7656ffe", "editorCompatibility": "2020.3.0a1", "version": "1.2.3", "source": "registry", "testable": false}, "com.unity.test-framework@1.5.1": {"name": "com.unity.test-framework", "displayName": "Test Framework", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c", "fingerprint": "a6f5be5f149c799640cce1ac6aa4057426adc0c7", "editorCompatibility": "2022.3.0a1", "version": "1.5.1", "source": "builtin", "testable": false}, "com.unity.testtools.codecoverage@1.2.6": {"name": "com.unity.testtools.codecoverage", "displayName": "Code Coverage", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.testtools.codecoverage@205a02cbcb39", "fingerprint": "205a02cbcb39584f20b51c49b853047aceb3a3a7", "editorCompatibility": "2019.3.0a1", "version": "1.2.6", "source": "registry", "testable": false}, "com.unity.burst@1.8.23": {"name": "com.unity.burst", "displayName": "<PERSON><PERSON><PERSON>", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.burst@6aff1dd08a0c", "fingerprint": "6aff1dd08a0c2f92ddb7f56ec033a5cb88967056", "editorCompatibility": "2021.3.0a1", "version": "1.8.23", "source": "registry", "testable": false}, "com.unity.dt.app-ui@1.3.1": {"name": "com.unity.dt.app-ui", "displayName": "App UI", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.dt.app-ui@7b87c8225c06", "fingerprint": "7b87c8225c066f90ed48f3fbee5375942c16f476", "editorCompatibility": "2021.3.0a1", "version": "1.3.1", "source": "registry", "testable": false}, "com.unity.ai.toolkit@1.0.0-pre.19": {"name": "com.unity.ai.toolkit", "displayName": "AI Toolkit", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.ai.toolkit@493fe336b8c1", "fingerprint": "493fe336b8c1049873d9a70a76ba3c04a33dd870", "editorCompatibility": "6000.2.0a1", "version": "1.0.0-pre.19", "source": "registry", "testable": false}, "com.unity.mathematics@1.3.2": {"name": "com.unity.mathematics", "displayName": "Mathematics", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.mathematics@8017b507cc74", "fingerprint": "8017b507cc74bf0a1dd14b18aa860569f807314d", "editorCompatibility": "2021.3.0a1", "version": "1.3.2", "source": "registry", "testable": false}, "com.unity.serialization@3.1.2": {"name": "com.unity.serialization", "displayName": "Serialization", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.serialization@582cbf30bbfd", "fingerprint": "582cbf30bbfd3c90a738e49c2ad69eafd8803f1d", "editorCompatibility": "2022.2.0a18", "version": "3.1.2", "source": "registry", "testable": false}, "com.unity.2d.common@11.0.1": {"name": "com.unity.2d.common", "displayName": "2D Common", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.2d.common@dd402daace1b", "fingerprint": "dd402daace1b7dbeb70cb9e7ae044cc4b10f989a", "editorCompatibility": "6000.2.0a9", "version": "11.0.1", "source": "registry", "testable": false}, "com.unity.searcher@4.9.3": {"name": "com.unity.searcher", "displayName": "Searcher", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.searcher@1e17ce91558d", "fingerprint": "1e17ce91558d1d9127554adc03d275f39a7466a2", "editorCompatibility": "2019.1.0a1", "version": "4.9.3", "source": "registry", "testable": false}, "com.unity.rendering.light-transport@1.0.1": {"name": "com.unity.rendering.light-transport", "displayName": "Unity Light Transport Library", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.rendering.light-transport@2c9279f90d7c", "fingerprint": "2c9279f90d7c8dfb6e76e3f90b6b3be1cafabfbc", "editorCompatibility": "2023.3.0b1", "version": "1.0.1", "source": "builtin", "testable": false}, "com.unity.test-framework.performance@3.1.0": {"name": "com.unity.test-framework.performance", "displayName": "Performance testing API", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.test-framework.performance@92d1d09a72ed", "fingerprint": "92d1d09a72ed696fa23fd76c675b29d211664b50", "editorCompatibility": "2020.3.0a1", "version": "3.1.0", "source": "registry", "testable": false}, "com.unity.ext.nunit@2.0.5": {"name": "com.unity.ext.nunit", "displayName": "Custom NUnit", "resolvedPath": "C:\\Unity\\BLAME\\BLAME\\Library\\PackageCache\\com.unity.ext.nunit@031a54704bff", "fingerprint": "031a54704bffe39e6a0324909f8eaa4565bdebf2", "editorCompatibility": "2019.4.0a1", "version": "2.0.5", "source": "builtin", "testable": false}}}