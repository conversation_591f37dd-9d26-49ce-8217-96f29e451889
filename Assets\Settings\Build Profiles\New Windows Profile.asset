%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 15003, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: New Windows Profile
  m_EditorClassIdentifier: 
  m_AssetVersion: 1
  m_BuildTarget: 19
  m_Subtarget: 2
  m_PlatformId: 4e3c793746204150860bf175a9a41a05
  m_PlatformBuildProfile:
    rid: 6426552317248798720
  m_OverrideGlobalSceneList: 0
  m_Scenes:
  - m_enabled: 1
    m_path: Assets/_Game/Scenes/Start.unity
  - m_enabled: 1
    m_path: Assets/_Game/Scenes/Main.unity
  m_ScriptingDefines: []
  m_PlayerSettingsYaml:
    m_Settings:
    - line: '| PlayerSettings:'
    - line: '|   m_ObjectHideFlags: 0'
    - line: '|   serializedVersion: 28'
    - line: '|   productGUID: c8bb55f2850ede1408c448eefb72fce8'
    - line: '|   AndroidProfiler: 0'
    - line: '|   AndroidFilterTouchesWhenObscured: 0'
    - line: '|   AndroidEnableSustainedPerformanceMode: 0'
    - line: '|   defaultScreenOrientation: 4'
    - line: '|   targetDevice: 2'
    - line: '|   useOnDemandResources: 0'
    - line: '|   accelerometerFrequency: 60'
    - line: '|   companyName: Gridbased'
    - line: '|   productName: Persistent Object'
    - line: '|   defaultCursor: {instanceID: 0}'
    - line: '|   cursorHotspot: {x: 0, y: 0}'
    - line: '|   m_SplashScreenBackgroundColor: {r: 0, g: 0, b: 0, a: 1}'
    - line: '|   m_ShowUnitySplashScreen: 1'
    - line: '|   m_ShowUnitySplashLogo: 0'
    - line: '|   m_SplashScreenOverlayOpacity: 1'
    - line: '|   m_SplashScreenAnimation: 1'
    - line: '|   m_SplashScreenLogoStyle: 1'
    - line: '|   m_SplashScreenDrawMode: 0'
    - line: '|   m_SplashScreenBackgroundAnimationZoom: 1'
    - line: '|   m_SplashScreenLogoAnimationZoom: 1'
    - line: '|   m_SplashScreenBackgroundLandscapeAspect: 1'
    - line: '|   m_SplashScreenBackgroundPortraitAspect: 1'
    - line: '|   m_SplashScreenBackgroundLandscapeUvs:'
    - line: '|     serializedVersion: 2'
    - line: '|     x: 0'
    - line: '|     y: 0'
    - line: '|     width: 1'
    - line: '|     height: 1'
    - line: '|   m_SplashScreenBackgroundPortraitUvs:'
    - line: '|     serializedVersion: 2'
    - line: '|     x: 0'
    - line: '|     y: 0'
    - line: '|     width: 1'
    - line: '|     height: 1'
    - line: '|   m_SplashScreenLogos:'
    - line: '|   - logo: {fileID: 21300000, guid: 9c9b8a80c27e82c48b90c5ab0ddd0c5d,
        type: 3}'
    - line: '|     duration: 2'
    - line: '|   m_VirtualRealitySplashScreen: {instanceID: 0}'
    - line: '|   m_HolographicTrackingLossScreen: {instanceID: 0}'
    - line: '|   defaultScreenWidth: 1920'
    - line: '|   defaultScreenHeight: 1080'
    - line: '|   defaultScreenWidthWeb: 960'
    - line: '|   defaultScreenHeightWeb: 600'
    - line: '|   m_StereoRenderingPath: 0'
    - line: '|   m_ActiveColorSpace: 1'
    - line: '|   unsupportedMSAAFallback: 0'
    - line: '|   m_SpriteBatchMaxVertexCount: 65535'
    - line: '|   m_SpriteBatchVertexThreshold: 300'
    - line: '|   m_MTRendering: 1'
    - line: '|   mipStripping: 0'
    - line: '|   numberOfMipsStripped: 0'
    - line: '|   numberOfMipsStrippedPerMipmapLimitGroup: {}'
    - line: '|   m_StackTraceTypes: 010000000100000001000000010000000100000001000000'
    - line: '|   iosShowActivityIndicatorOnLoading: -1'
    - line: '|   androidShowActivityIndicatorOnLoading: -1'
    - line: '|   iosUseCustomAppBackgroundBehavior: 0'
    - line: '|   allowedAutorotateToPortrait: 1'
    - line: '|   allowedAutorotateToPortraitUpsideDown: 1'
    - line: '|   allowedAutorotateToLandscapeRight: 1'
    - line: '|   allowedAutorotateToLandscapeLeft: 1'
    - line: '|   useOSAutorotation: 1'
    - line: '|   use32BitDisplayBuffer: 1'
    - line: '|   preserveFramebufferAlpha: 0'
    - line: '|   disableDepthAndStencilBuffers: 0'
    - line: '|   androidStartInFullscreen: 1'
    - line: '|   androidRenderOutsideSafeArea: 1'
    - line: '|   androidUseSwappy: 0'
    - line: '|   androidBlitType: 0'
    - line: '|   androidResizeableActivity: 1'
    - line: '|   androidDefaultWindowWidth: 1920'
    - line: '|   androidDefaultWindowHeight: 1080'
    - line: '|   androidMinimumWindowWidth: 400'
    - line: '|   androidMinimumWindowHeight: 300'
    - line: '|   androidFullscreenMode: 1'
    - line: '|   androidAutoRotationBehavior: 1'
    - line: '|   androidPredictiveBackSupport: 0'
    - line: '|   androidApplicationEntry: 2'
    - line: '|   defaultIsNativeResolution: 1'
    - line: '|   macRetinaSupport: 1'
    - line: '|   runInBackground: 1'
    - line: '|   muteOtherAudioSources: 0'
    - line: '|   Prepare IOS For Recording: 0'
    - line: '|   Force IOS Speakers When Recording: 0'
    - line: '|   deferSystemGesturesMode: 0'
    - line: '|   hideHomeButton: 0'
    - line: '|   submitAnalytics: 1'
    - line: '|   usePlayerLog: 1'
    - line: '|   dedicatedServerOptimizations: 0'
    - line: '|   bakeCollisionMeshes: 0'
    - line: '|   forceSingleInstance: 0'
    - line: '|   useFlipModelSwapchain: 1'
    - line: '|   resizableWindow: 0'
    - line: '|   useMacAppStoreValidation: 0'
    - line: '|   macAppStoreCategory: public.app-category.games'
    - line: '|   gpuSkinning: 1'
    - line: '|   meshDeformation: 2'
    - line: '|   xboxPIXTextureCapture: 0'
    - line: '|   xboxEnableAvatar: 0'
    - line: '|   xboxEnableKinect: 0'
    - line: '|   xboxEnableKinectAutoTracking: 0'
    - line: '|   xboxEnableFitness: 0'
    - line: '|   visibleInBackground: 1'
    - line: '|   allowFullscreenSwitch: 1'
    - line: '|   fullscreenMode: 1'
    - line: '|   xboxSpeechDB: 0'
    - line: '|   xboxEnableHeadOrientation: 0'
    - line: '|   xboxEnableGuest: 0'
    - line: '|   xboxEnablePIXSampling: 0'
    - line: '|   metalFramebufferOnly: 0'
    - line: '|   xboxOneResolution: 0'
    - line: '|   xboxOneSResolution: 0'
    - line: '|   xboxOneXResolution: 3'
    - line: '|   xboxOneMonoLoggingLevel: 0'
    - line: '|   xboxOneLoggingLevel: 1'
    - line: '|   xboxOneDisableEsram: 0'
    - line: '|   xboxOneEnableTypeOptimization: 0'
    - line: '|   xboxOnePresentImmediateThreshold: 0'
    - line: '|   switchQueueCommandMemory: 1048576'
    - line: '|   switchQueueControlMemory: 16384'
    - line: '|   switchQueueComputeMemory: 262144'
    - line: '|   switchNVNShaderPoolsGranularity: 33554432'
    - line: '|   switchNVNDefaultPoolsGranularity: ********'
    - line: '|   switchNVNOtherPoolsGranularity: ********'
    - line: '|   switchGpuScratchPoolGranularity: 2097152'
    - line: '|   switchAllowGpuScratchShrinking: 0'
    - line: '|   switchNVNMaxPublicTextureIDCount: 0'
    - line: '|   switchNVNMaxPublicSamplerIDCount: 0'
    - line: '|   switchMaxWorkerMultiple: 8'
    - line: '|   switchNVNGraphicsFirmwareMemory: 32'
    - line: '|   switchGraphicsJobsSyncAfterKick: 1'
    - line: '|   vulkanNumSwapchainBuffers: 3'
    - line: '|   vulkanEnableSetSRGBWrite: 0'
    - line: '|   vulkanEnablePreTransform: 0'
    - line: '|   vulkanEnableLateAcquireNextImage: 0'
    - line: '|   vulkanEnableCommandBufferRecycling: 1'
    - line: '|   loadStoreDebugModeEnabled: 0'
    - line: '|   visionOSBundleVersion: 1.0'
    - line: '|   tvOSBundleVersion: 1.0'
    - line: '|   bundleVersion: 0.3'
    - line: '|   preloadedAssets: []'
    - line: '|   metroInputSource: 0'
    - line: '|   wsaTransparentSwapchain: 0'
    - line: '|   m_HolographicPauseOnTrackingLoss: 1'
    - line: '|   xboxOneDisableKinectGpuReservation: 1'
    - line: '|   xboxOneEnable7thCore: 1'
    - line: '|   vrSettings:'
    - line: '|     enable360StereoCapture: 0'
    - line: '|   isWsaHolographicRemotingEnabled: 0'
    - line: '|   enableFrameTimingStats: 0'
    - line: '|   enableOpenGLProfilerGPURecorders: 1'
    - line: '|   allowHDRDisplaySupport: 0'
    - line: '|   useHDRDisplay: 0'
    - line: '|   hdrBitDepth: 0'
    - line: '|   m_ColorGamuts: 00000000'
    - line: '|   targetPixelDensity: 30'
    - line: '|   resolutionScalingMode: 0'
    - line: '|   resetResolutionOnWindowResize: 0'
    - line: '|   androidSupportedAspectRatio: 1'
    - line: '|   androidMaxAspectRatio: 2.4'
    - line: '|   androidMinAspectRatio: 1'
    - line: '|   applicationIdentifier:'
    - line: '|     Standalone: com.Unity-Technologies.com.unity.template.hdrp-blank'
    - line: '|   buildNumber:'
    - line: '|     Standalone: 0'
    - line: '|     VisionOS: 0'
    - line: '|     iPhone: 0'
    - line: '|     tvOS: 0'
    - line: '|   overrideDefaultApplicationIdentifier: 1'
    - line: '|   AndroidBundleVersionCode: 1'
    - line: '|   AndroidMinSdkVersion: 23'
    - line: '|   AndroidTargetSdkVersion: 0'
    - line: '|   AndroidPreferredInstallLocation: 1'
    - line: '|   aotOptions: '
    - line: '|   stripEngineCode: 1'
    - line: '|   iPhoneStrippingLevel: 0'
    - line: '|   iPhoneScriptCallOptimization: 0'
    - line: '|   ForceInternetPermission: 0'
    - line: '|   ForceSDCardPermission: 0'
    - line: '|   CreateWallpaper: 0'
    - line: '|   androidSplitApplicationBinary: 0'
    - line: '|   keepLoadedShadersAlive: 0'
    - line: '|   StripUnusedMeshComponents: 0'
    - line: '|   strictShaderVariantMatching: 0'
    - line: '|   VertexChannelCompressionMask: 4054'
    - line: '|   iPhoneSdkVersion: 988'
    - line: '|   iOSSimulatorArchitecture: 0'
    - line: '|   iOSTargetOSVersionString: 13.0'
    - line: '|   tvOSSdkVersion: 0'
    - line: '|   tvOSSimulatorArchitecture: 0'
    - line: '|   tvOSRequireExtendedGameController: 0'
    - line: '|   tvOSTargetOSVersionString: 13.0'
    - line: '|   VisionOSSdkVersion: 0'
    - line: '|   VisionOSTargetOSVersionString: 1.0'
    - line: '|   uIPrerenderedIcon: 0'
    - line: '|   uIRequiresPersistentWiFi: 0'
    - line: '|   uIRequiresFullScreen: 1'
    - line: '|   uIStatusBarHidden: 1'
    - line: '|   uIExitOnSuspend: 0'
    - line: '|   uIStatusBarStyle: 0'
    - line: '|   appleTVSplashScreen: {instanceID: 0}'
    - line: '|   appleTVSplashScreen2x: {instanceID: 0}'
    - line: '|   tvOSSmallIconLayers: []'
    - line: '|   tvOSSmallIconLayers2x: []'
    - line: '|   tvOSLargeIconLayers: []'
    - line: '|   tvOSLargeIconLayers2x: []'
    - line: '|   tvOSTopShelfImageLayers: []'
    - line: '|   tvOSTopShelfImageLayers2x: []'
    - line: '|   tvOSTopShelfImageWideLayers: []'
    - line: '|   tvOSTopShelfImageWideLayers2x: []'
    - line: '|   iOSLaunchScreenType: 0'
    - line: '|   iOSLaunchScreenPortrait: {instanceID: 0}'
    - line: '|   iOSLaunchScreenLandscape: {instanceID: 0}'
    - line: '|   iOSLaunchScreenBackgroundColor:'
    - line: '|     serializedVersion: 2'
    - line: '|     rgba: 0'
    - line: '|   iOSLaunchScreenFillPct: 100'
    - line: '|   iOSLaunchScreenSize: 100'
    - line: '|   iOSLaunchScreeniPadType: 0'
    - line: '|   iOSLaunchScreeniPadImage: {instanceID: 0}'
    - line: '|   iOSLaunchScreeniPadBackgroundColor:'
    - line: '|     serializedVersion: 2'
    - line: '|     rgba: 0'
    - line: '|   iOSLaunchScreeniPadFillPct: 100'
    - line: '|   iOSLaunchScreeniPadSize: 100'
    - line: '|   iOSLaunchScreenCustomStoryboardPath: '
    - line: '|   iOSLaunchScreeniPadCustomStoryboardPath: '
    - line: '|   iOSDeviceRequirements: []'
    - line: '|   iOSURLSchemes: []'
    - line: '|   macOSURLSchemes: []'
    - line: '|   iOSBackgroundModes: 0'
    - line: '|   iOSMetalForceHardShadows: 0'
    - line: '|   metalEditorSupport: 1'
    - line: '|   metalAPIValidation: 1'
    - line: '|   metalCompileShaderBinary: 0'
    - line: '|   iOSRenderExtraFrameOnPause: 0'
    - line: '|   iosCopyPluginsCodeInsteadOfSymlink: 0'
    - line: '|   appleDeveloperTeamID: '
    - line: '|   iOSManualSigningProvisioningProfileID: '
    - line: '|   tvOSManualSigningProvisioningProfileID: '
    - line: '|   VisionOSManualSigningProvisioningProfileID: '
    - line: '|   iOSManualSigningProvisioningProfileType: 0'
    - line: '|   tvOSManualSigningProvisioningProfileType: 0'
    - line: '|   VisionOSManualSigningProvisioningProfileType: 0'
    - line: '|   appleEnableAutomaticSigning: 0'
    - line: '|   iOSRequireARKit: 0'
    - line: '|   iOSAutomaticallyDetectAndAddCapabilities: 1'
    - line: '|   appleEnableProMotion: 0'
    - line: '|   shaderPrecisionModel: 0'
    - line: '|   clonedFromGUID: c71a6e77368cc6048998f34f4bbe2b86'
    - line: '|   templatePackageId: com.unity.template.hdrp-blank@17.0.5'
    - line: '|   templateDefaultScene: Assets/OutdoorsScene.unity'
    - line: '|   useCustomMainManifest: 0'
    - line: '|   useCustomLauncherManifest: 0'
    - line: '|   useCustomMainGradleTemplate: 0'
    - line: '|   useCustomLauncherGradleManifest: 0'
    - line: '|   useCustomBaseGradleTemplate: 0'
    - line: '|   useCustomGradlePropertiesTemplate: 0'
    - line: '|   useCustomGradleSettingsTemplate: 0'
    - line: '|   useCustomProguardFile: 0'
    - line: '|   AndroidTargetArchitectures: 2'
    - line: '|   AndroidSplashScreenScale: 0'
    - line: '|   androidSplashScreen: {instanceID: 0}'
    - line: '|   AndroidKeystoreName: '
    - line: '|   AndroidKeyaliasName: '
    - line: '|   AndroidEnableArmv9SecurityFeatures: 0'
    - line: '|   AndroidEnableArm64MTE: 0'
    - line: '|   AndroidBuildApkPerCpuArchitecture: 0'
    - line: '|   AndroidTVCompatibility: 0'
    - line: '|   AndroidIsGame: 1'
    - line: '|   androidAppCategory: 3'
    - line: '|   useAndroidAppCategory: 1'
    - line: '|   androidAppCategoryOther: '
    - line: '|   AndroidEnableTango: 0'
    - line: '|   androidEnableBanner: 1'
    - line: '|   androidUseLowAccuracyLocation: 0'
    - line: '|   androidUseCustomKeystore: 0'
    - line: '|   m_AndroidBanners:'
    - line: '|   - width: 320'
    - line: '|     height: 180'
    - line: '|     banner: {instanceID: 0}'
    - line: '|   androidGamepadSupportLevel: 0'
    - line: '|   AndroidMinifyRelease: 0'
    - line: '|   AndroidMinifyDebug: 0'
    - line: '|   AndroidValidateAppBundleSize: 1'
    - line: '|   AndroidAppBundleSizeToValidate: 150'
    - line: '|   AndroidReportGooglePlayAppDependencies: 1'
    - line: '|   androidSymbolsSizeThreshold: 800'
    - line: '|   m_BuildTargetIcons: []'
    - line: '|   m_BuildTargetPlatformIcons:'
    - line: '|   - m_BuildTarget: iPhone'
    - line: '|     m_Icons:'
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 180'
    - line: '|       m_Height: 180'
    - line: '|       m_Kind: 0'
    - line: '|       m_SubKind: iPhone'
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 120'
    - line: '|       m_Height: 120'
    - line: '|       m_Kind: 0'
    - line: '|       m_SubKind: iPhone'
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 167'
    - line: '|       m_Height: 167'
    - line: '|       m_Kind: 0'
    - line: '|       m_SubKind: iPad'
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 152'
    - line: '|       m_Height: 152'
    - line: '|       m_Kind: 0'
    - line: '|       m_SubKind: iPad'
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 76'
    - line: '|       m_Height: 76'
    - line: '|       m_Kind: 0'
    - line: '|       m_SubKind: iPad'
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 120'
    - line: '|       m_Height: 120'
    - line: '|       m_Kind: 3'
    - line: '|       m_SubKind: iPhone'
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 80'
    - line: '|       m_Height: 80'
    - line: '|       m_Kind: 3'
    - line: '|       m_SubKind: iPhone'
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 80'
    - line: '|       m_Height: 80'
    - line: '|       m_Kind: 3'
    - line: '|       m_SubKind: iPad'
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 40'
    - line: '|       m_Height: 40'
    - line: '|       m_Kind: 3'
    - line: '|       m_SubKind: iPad'
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 87'
    - line: '|       m_Height: 87'
    - line: '|       m_Kind: 1'
    - line: '|       m_SubKind: iPhone'
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 58'
    - line: '|       m_Height: 58'
    - line: '|       m_Kind: 1'
    - line: '|       m_SubKind: iPhone'
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 29'
    - line: '|       m_Height: 29'
    - line: '|       m_Kind: 1'
    - line: '|       m_SubKind: iPhone'
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 58'
    - line: '|       m_Height: 58'
    - line: '|       m_Kind: 1'
    - line: '|       m_SubKind: iPad'
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 29'
    - line: '|       m_Height: 29'
    - line: '|       m_Kind: 1'
    - line: '|       m_SubKind: iPad'
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 60'
    - line: '|       m_Height: 60'
    - line: '|       m_Kind: 2'
    - line: '|       m_SubKind: iPhone'
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 40'
    - line: '|       m_Height: 40'
    - line: '|       m_Kind: 2'
    - line: '|       m_SubKind: iPhone'
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 40'
    - line: '|       m_Height: 40'
    - line: '|       m_Kind: 2'
    - line: '|       m_SubKind: iPad'
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 20'
    - line: '|       m_Height: 20'
    - line: '|       m_Kind: 2'
    - line: '|       m_SubKind: iPad'
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 1024'
    - line: '|       m_Height: 1024'
    - line: '|       m_Kind: 4'
    - line: '|       m_SubKind: App Store'
    - line: '|   - m_BuildTarget: Android'
    - line: '|     m_Icons:'
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 432'
    - line: '|       m_Height: 432'
    - line: '|       m_Kind: 2'
    - line: '|       m_SubKind: '
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 324'
    - line: '|       m_Height: 324'
    - line: '|       m_Kind: 2'
    - line: '|       m_SubKind: '
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 216'
    - line: '|       m_Height: 216'
    - line: '|       m_Kind: 2'
    - line: '|       m_SubKind: '
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 162'
    - line: '|       m_Height: 162'
    - line: '|       m_Kind: 2'
    - line: '|       m_SubKind: '
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 108'
    - line: '|       m_Height: 108'
    - line: '|       m_Kind: 2'
    - line: '|       m_SubKind: '
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 81'
    - line: '|       m_Height: 81'
    - line: '|       m_Kind: 2'
    - line: '|       m_SubKind: '
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 192'
    - line: '|       m_Height: 192'
    - line: '|       m_Kind: 1'
    - line: '|       m_SubKind: '
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 144'
    - line: '|       m_Height: 144'
    - line: '|       m_Kind: 1'
    - line: '|       m_SubKind: '
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 96'
    - line: '|       m_Height: 96'
    - line: '|       m_Kind: 1'
    - line: '|       m_SubKind: '
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 72'
    - line: '|       m_Height: 72'
    - line: '|       m_Kind: 1'
    - line: '|       m_SubKind: '
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 48'
    - line: '|       m_Height: 48'
    - line: '|       m_Kind: 1'
    - line: '|       m_SubKind: '
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 36'
    - line: '|       m_Height: 36'
    - line: '|       m_Kind: 1'
    - line: '|       m_SubKind: '
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 192'
    - line: '|       m_Height: 192'
    - line: '|       m_Kind: 0'
    - line: '|       m_SubKind: '
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 144'
    - line: '|       m_Height: 144'
    - line: '|       m_Kind: 0'
    - line: '|       m_SubKind: '
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 96'
    - line: '|       m_Height: 96'
    - line: '|       m_Kind: 0'
    - line: '|       m_SubKind: '
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 72'
    - line: '|       m_Height: 72'
    - line: '|       m_Kind: 0'
    - line: '|       m_SubKind: '
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 48'
    - line: '|       m_Height: 48'
    - line: '|       m_Kind: 0'
    - line: '|       m_SubKind: '
    - line: '|     - m_Textures: []'
    - line: '|       m_Width: 36'
    - line: '|       m_Height: 36'
    - line: '|       m_Kind: 0'
    - line: '|       m_SubKind: '
    - line: '|   m_BuildTargetBatching:'
    - line: '|   - m_BuildTarget: Standalone'
    - line: '|     m_StaticBatching: 1'
    - line: '|     m_DynamicBatching: 0'
    - line: '|   m_BuildTargetShaderSettings: []'
    - line: '|   m_BuildTargetGraphicsJobs:'
    - line: '|   - m_BuildTarget: MacStandaloneSupport'
    - line: '|     m_GraphicsJobs: 0'
    - line: '|   - m_BuildTarget: Switch'
    - line: '|     m_GraphicsJobs: 1'
    - line: '|   - m_BuildTarget: MetroSupport'
    - line: '|     m_GraphicsJobs: 1'
    - line: '|   - m_BuildTarget: AppleTVSupport'
    - line: '|     m_GraphicsJobs: 0'
    - line: '|   - m_BuildTarget: BJMSupport'
    - line: '|     m_GraphicsJobs: 1'
    - line: '|   - m_BuildTarget: LinuxStandaloneSupport'
    - line: '|     m_GraphicsJobs: 1'
    - line: '|   - m_BuildTarget: PS4Player'
    - line: '|     m_GraphicsJobs: 1'
    - line: '|   - m_BuildTarget: iOSSupport'
    - line: '|     m_GraphicsJobs: 0'
    - line: '|   - m_BuildTarget: WindowsStandaloneSupport'
    - line: '|     m_GraphicsJobs: 1'
    - line: '|   - m_BuildTarget: XboxOnePlayer'
    - line: '|     m_GraphicsJobs: 1'
    - line: '|   - m_BuildTarget: LuminSupport'
    - line: '|     m_GraphicsJobs: 0'
    - line: '|   - m_BuildTarget: AndroidPlayer'
    - line: '|     m_GraphicsJobs: 0'
    - line: '|   - m_BuildTarget: WebGLSupport'
    - line: '|     m_GraphicsJobs: 0'
    - line: '|   m_BuildTargetGraphicsJobMode: []'
    - line: '|   m_BuildTargetGraphicsAPIs:'
    - line: '|   - m_BuildTarget: LinuxStandaloneSupport'
    - line: '|     m_APIs: 15000000'
    - line: '|     m_Automatic: 0'
    - line: '|   - m_BuildTarget: MacStandaloneSupport'
    - line: '|     m_APIs: 10000000'
    - line: '|     m_Automatic: 0'
    - line: '|   - m_BuildTarget: WindowsStandaloneSupport'
    - line: '|     m_APIs: 0200000012000000'
    - line: '|     m_Automatic: 0'
    - line: '|   - m_BuildTarget: iOSSupport'
    - line: '|     m_APIs: 10000000'
    - line: '|     m_Automatic: 1'
    - line: '|   - m_BuildTarget: AndroidPlayer'
    - line: '|     m_APIs: 0b000000'
    - line: '|     m_Automatic: 0'
    - line: '|   m_BuildTargetVRSettings: []'
    - line: '|   m_DefaultShaderChunkSizeInMB: 16'
    - line: '|   m_DefaultShaderChunkCount: 0'
    - line: '|   openGLRequireES31: 0'
    - line: '|   openGLRequireES31AEP: 0'
    - line: '|   openGLRequireES32: 0'
    - line: '|   m_TemplateCustomTags: {}'
    - line: '|   mobileMTRendering:'
    - line: '|     Android: 1'
    - line: '|     iPhone: 1'
    - line: '|     tvOS: 1'
    - line: '|   m_BuildTargetGroupLightmapEncodingQuality:'
    - line: '|   - serializedVersion: 2'
    - line: '|     m_BuildTarget: Standalone'
    - line: '|     m_EncodingQuality: 2'
    - line: '|   - serializedVersion: 2'
    - line: '|     m_BuildTarget: Android'
    - line: '|     m_EncodingQuality: 2'
    - line: '|   - serializedVersion: 2'
    - line: '|     m_BuildTarget: Lumin'
    - line: '|     m_EncodingQuality: 2'
    - line: '|   - serializedVersion: 2'
    - line: '|     m_BuildTarget: WindowsStoreApps'
    - line: '|     m_EncodingQuality: 2'
    - line: '|   - serializedVersion: 2'
    - line: '|     m_BuildTarget: iOS'
    - line: '|     m_EncodingQuality: 2'
    - line: '|   - serializedVersion: 2'
    - line: '|     m_BuildTarget: Switch'
    - line: '|     m_EncodingQuality: 2'
    - line: '|   m_BuildTargetGroupHDRCubemapEncodingQuality: []'
    - line: '|   m_BuildTargetGroupLightmapSettings: []'
    - line: '|   m_BuildTargetGroupLoadStoreDebugModeSettings: []'
    - line: '|   m_BuildTargetNormalMapEncoding: []'
    - line: '|   m_BuildTargetDefaultTextureCompressionFormat: []'
    - line: '|   playModeTestRunnerEnabled: 0'
    - line: '|   runPlayModeTestAsEditModeTest: 0'
    - line: '|   actionOnDotNetUnhandledException: 1'
    - line: '|   editorGfxJobOverride: 1'
    - line: '|   enableInternalProfiler: 0'
    - line: '|   logObjCUncaughtExceptions: 1'
    - line: '|   enableCrashReportAPI: 0'
    - line: '|   cameraUsageDescription: '
    - line: '|   locationUsageDescription: '
    - line: '|   microphoneUsageDescription: '
    - line: '|   bluetoothUsageDescription: '
    - line: '|   macOSTargetOSVersion: 11.0'
    - line: '|   switchNMETAOverride: '
    - line: '|   switchNetLibKey: '
    - line: '|   switchSocketMemoryPoolSize: 6144'
    - line: '|   switchSocketAllocatorPoolSize: 128'
    - line: '|   switchSocketConcurrencyLimit: 14'
    - line: '|   switchScreenResolutionBehavior: 2'
    - line: '|   switchUseCPUProfiler: 0'
    - line: '|   switchEnableFileSystemTrace: 0'
    - line: '|   switchLTOSetting: 0'
    - line: '|   switchApplicationID: 0x01004b9000490000'
    - line: '|   switchNSODependencies: '
    - line: '|   switchCompilerFlags: '
    - line: '|   switchTitleNames_0: '
    - line: '|   switchTitleNames_1: '
    - line: '|   switchTitleNames_2: '
    - line: '|   switchTitleNames_3: '
    - line: '|   switchTitleNames_4: '
    - line: '|   switchTitleNames_5: '
    - line: '|   switchTitleNames_6: '
    - line: '|   switchTitleNames_7: '
    - line: '|   switchTitleNames_8: '
    - line: '|   switchTitleNames_9: '
    - line: '|   switchTitleNames_10: '
    - line: '|   switchTitleNames_11: '
    - line: '|   switchTitleNames_12: '
    - line: '|   switchTitleNames_13: '
    - line: '|   switchTitleNames_14: '
    - line: '|   switchTitleNames_15: '
    - line: '|   switchPublisherNames_0: '
    - line: '|   switchPublisherNames_1: '
    - line: '|   switchPublisherNames_2: '
    - line: '|   switchPublisherNames_3: '
    - line: '|   switchPublisherNames_4: '
    - line: '|   switchPublisherNames_5: '
    - line: '|   switchPublisherNames_6: '
    - line: '|   switchPublisherNames_7: '
    - line: '|   switchPublisherNames_8: '
    - line: '|   switchPublisherNames_9: '
    - line: '|   switchPublisherNames_10: '
    - line: '|   switchPublisherNames_11: '
    - line: '|   switchPublisherNames_12: '
    - line: '|   switchPublisherNames_13: '
    - line: '|   switchPublisherNames_14: '
    - line: '|   switchPublisherNames_15: '
    - line: '|   switchIcons_0: {instanceID: 0}'
    - line: '|   switchIcons_1: {instanceID: 0}'
    - line: '|   switchIcons_2: {instanceID: 0}'
    - line: '|   switchIcons_3: {instanceID: 0}'
    - line: '|   switchIcons_4: {instanceID: 0}'
    - line: '|   switchIcons_5: {instanceID: 0}'
    - line: '|   switchIcons_6: {instanceID: 0}'
    - line: '|   switchIcons_7: {instanceID: 0}'
    - line: '|   switchIcons_8: {instanceID: 0}'
    - line: '|   switchIcons_9: {instanceID: 0}'
    - line: '|   switchIcons_10: {instanceID: 0}'
    - line: '|   switchIcons_11: {instanceID: 0}'
    - line: '|   switchIcons_12: {instanceID: 0}'
    - line: '|   switchIcons_13: {instanceID: 0}'
    - line: '|   switchIcons_14: {instanceID: 0}'
    - line: '|   switchIcons_15: {instanceID: 0}'
    - line: '|   switchSmallIcons_0: {instanceID: 0}'
    - line: '|   switchSmallIcons_1: {instanceID: 0}'
    - line: '|   switchSmallIcons_2: {instanceID: 0}'
    - line: '|   switchSmallIcons_3: {instanceID: 0}'
    - line: '|   switchSmallIcons_4: {instanceID: 0}'
    - line: '|   switchSmallIcons_5: {instanceID: 0}'
    - line: '|   switchSmallIcons_6: {instanceID: 0}'
    - line: '|   switchSmallIcons_7: {instanceID: 0}'
    - line: '|   switchSmallIcons_8: {instanceID: 0}'
    - line: '|   switchSmallIcons_9: {instanceID: 0}'
    - line: '|   switchSmallIcons_10: {instanceID: 0}'
    - line: '|   switchSmallIcons_11: {instanceID: 0}'
    - line: '|   switchSmallIcons_12: {instanceID: 0}'
    - line: '|   switchSmallIcons_13: {instanceID: 0}'
    - line: '|   switchSmallIcons_14: {instanceID: 0}'
    - line: '|   switchSmallIcons_15: {instanceID: 0}'
    - line: '|   switchManualHTML: '
    - line: '|   switchAccessibleURLs: '
    - line: '|   switchLegalInformation: '
    - line: '|   switchMainThreadStackSize: 1048576'
    - line: '|   switchPresenceGroupId: '
    - line: '|   switchLogoHandling: 0'
    - line: '|   switchReleaseVersion: 0'
    - line: '|   switchDisplayVersion: 1.0.0'
    - line: '|   switchStartupUserAccount: 0'
    - line: '|   switchSupportedLanguagesMask: 0'
    - line: '|   switchLogoType: 0'
    - line: '|   switchApplicationErrorCodeCategory: '
    - line: '|   switchUserAccountSaveDataSize: 0'
    - line: '|   switchUserAccountSaveDataJournalSize: 0'
    - line: '|   switchApplicationAttribute: 0'
    - line: '|   switchCardSpecSize: -1'
    - line: '|   switchCardSpecClock: -1'
    - line: '|   switchRatingsMask: 0'
    - line: '|   switchRatingsInt_0: 0'
    - line: '|   switchRatingsInt_1: 0'
    - line: '|   switchRatingsInt_2: 0'
    - line: '|   switchRatingsInt_3: 0'
    - line: '|   switchRatingsInt_4: 0'
    - line: '|   switchRatingsInt_5: 0'
    - line: '|   switchRatingsInt_6: 0'
    - line: '|   switchRatingsInt_7: 0'
    - line: '|   switchRatingsInt_8: 0'
    - line: '|   switchRatingsInt_9: 0'
    - line: '|   switchRatingsInt_10: 0'
    - line: '|   switchRatingsInt_11: 0'
    - line: '|   switchRatingsInt_12: 0'
    - line: '|   switchLocalCommunicationIds_0: '
    - line: '|   switchLocalCommunicationIds_1: '
    - line: '|   switchLocalCommunicationIds_2: '
    - line: '|   switchLocalCommunicationIds_3: '
    - line: '|   switchLocalCommunicationIds_4: '
    - line: '|   switchLocalCommunicationIds_5: '
    - line: '|   switchLocalCommunicationIds_6: '
    - line: '|   switchLocalCommunicationIds_7: '
    - line: '|   switchParentalControl: 0'
    - line: '|   switchAllowsScreenshot: 1'
    - line: '|   switchAllowsVideoCapturing: 1'
    - line: '|   switchAllowsRuntimeAddOnContentInstall: 0'
    - line: '|   switchDataLossConfirmation: 0'
    - line: '|   switchUserAccountLockEnabled: 0'
    - line: '|   switchSystemResourceMemory: ********'
    - line: '|   switchSupportedNpadStyles: 22'
    - line: '|   switchNativeFsCacheSize: 32'
    - line: '|   switchIsHoldTypeHorizontal: 0'
    - line: '|   switchSupportedNpadCount: 8'
    - line: '|   switchEnableTouchScreen: 1'
    - line: '|   switchSocketConfigEnabled: 0'
    - line: '|   switchTcpInitialSendBufferSize: 32'
    - line: '|   switchTcpInitialReceiveBufferSize: 64'
    - line: '|   switchTcpAutoSendBufferSizeMax: 256'
    - line: '|   switchTcpAutoReceiveBufferSizeMax: 256'
    - line: '|   switchUdpSendBufferSize: 9'
    - line: '|   switchUdpReceiveBufferSize: 42'
    - line: '|   switchSocketBufferEfficiency: 4'
    - line: '|   switchSocketInitializeEnabled: 1'
    - line: '|   switchNetworkInterfaceManagerInitializeEnabled: 1'
    - line: '|   switchDisableHTCSPlayerConnection: 0'
    - line: '|   switchUseNewStyleFilepaths: 0'
    - line: '|   switchUseLegacyFmodPriorities: 0'
    - line: '|   switchUseMicroSleepForYield: 1'
    - line: '|   switchEnableRamDiskSupport: 0'
    - line: '|   switchMicroSleepForYieldTime: 25'
    - line: '|   switchRamDiskSpaceSize: 12'
    - line: '|   switchUpgradedPlayerSettingsToNMETA: 0'
    - line: '|   ps4NPAgeRating: 12'
    - line: '|   ps4NPTitleSecret: '
    - line: '|   ps4NPTrophyPackPath: '
    - line: '|   ps4ParentalLevel: 11'
    - line: '|   ps4ContentID: ED1633-NPXX51362_00-0000000000000000'
    - line: '|   ps4Category: 0'
    - line: '|   ps4MasterVersion: 01.00'
    - line: '|   ps4AppVersion: 01.00'
    - line: '|   ps4AppType: 0'
    - line: '|   ps4ParamSfxPath: '
    - line: '|   ps4VideoOutPixelFormat: 0'
    - line: '|   ps4VideoOutInitialWidth: 1920'
    - line: '|   ps4VideoOutBaseModeInitialWidth: 1920'
    - line: '|   ps4VideoOutReprojectionRate: 60'
    - line: '|   ps4PronunciationXMLPath: '
    - line: '|   ps4PronunciationSIGPath: '
    - line: '|   ps4BackgroundImagePath: '
    - line: '|   ps4StartupImagePath: '
    - line: '|   ps4StartupImagesFolder: '
    - line: '|   ps4IconImagesFolder: '
    - line: '|   ps4SaveDataImagePath: '
    - line: '|   ps4SdkOverride: '
    - line: '|   ps4BGMPath: '
    - line: '|   ps4ShareFilePath: '
    - line: '|   ps4ShareOverlayImagePath: '
    - line: '|   ps4PrivacyGuardImagePath: '
    - line: '|   ps4ExtraSceSysFile: '
    - line: '|   ps4NPtitleDatPath: '
    - line: '|   ps4RemotePlayKeyAssignment: -1'
    - line: '|   ps4RemotePlayKeyMappingDir: '
    - line: '|   ps4PlayTogetherPlayerCount: 0'
    - line: '|   ps4EnterButtonAssignment: 2'
    - line: '|   ps4ApplicationParam1: 0'
    - line: '|   ps4ApplicationParam2: 0'
    - line: '|   ps4ApplicationParam3: 0'
    - line: '|   ps4ApplicationParam4: 0'
    - line: '|   ps4DownloadDataSize: 0'
    - line: '|   ps4GarlicHeapSize: 2048'
    - line: '|   ps4ProGarlicHeapSize: 2560'
    - line: '|   playerPrefsMaxSize: 32768'
    - line: '|   ps4Passcode: frAQBc8Wsa1xVPfvJcrgRYwTiizs2trQ'
    - line: '|   ps4pnSessions: 1'
    - line: '|   ps4pnPresence: 1'
    - line: '|   ps4pnFriends: 1'
    - line: '|   ps4pnGameCustomData: 1'
    - line: '|   playerPrefsSupport: 0'
    - line: '|   enableApplicationExit: 0'
    - line: '|   resetTempFolder: 1'
    - line: '|   restrictedAudioUsageRights: 0'
    - line: '|   ps4UseResolutionFallback: 0'
    - line: '|   ps4ReprojectionSupport: 0'
    - line: '|   ps4UseAudio3dBackend: 0'
    - line: '|   ps4UseLowGarlicFragmentationMode: 1'
    - line: '|   ps4SocialScreenEnabled: 0'
    - line: '|   ps4ScriptOptimizationLevel: 2'
    - line: '|   ps4Audio3dVirtualSpeakerCount: 14'
    - line: '|   ps4attribCpuUsage: 0'
    - line: '|   ps4PatchPkgPath: '
    - line: '|   ps4PatchLatestPkgPath: '
    - line: '|   ps4PatchChangeinfoPath: '
    - line: '|   ps4PatchDayOne: 0'
    - line: '|   ps4attribUserManagement: 0'
    - line: '|   ps4attribMoveSupport: 0'
    - line: '|   ps4attrib3DSupport: 0'
    - line: '|   ps4attribShareSupport: 0'
    - line: '|   ps4attribExclusiveVR: 0'
    - line: '|   ps4disableAutoHideSplash: 0'
    - line: '|   ps4videoRecordingFeaturesUsed: 0'
    - line: '|   ps4contentSearchFeaturesUsed: 0'
    - line: '|   ps4CompatibilityPS5: 0'
    - line: '|   ps4AllowPS5Detection: 0'
    - line: '|   ps4GPU800MHz: 1'
    - line: '|   ps4attribEyeToEyeDistanceSettingVR: 0'
    - line: '|   ps4IncludedModules: []'
    - line: '|   ps4attribVROutputEnabled: 0'
    - line: '|   monoEnv: '
    - line: '|   splashScreenBackgroundSourceLandscape: {instanceID: 0}'
    - line: '|   splashScreenBackgroundSourcePortrait: {instanceID: 0}'
    - line: '|   blurSplashScreenBackground: 0'
    - line: '|   spritePackerPolicy: '
    - line: '|   webGLMemorySize: 32'
    - line: '|   webGLExceptionSupport: 1'
    - line: '|   webGLNameFilesAsHashes: 0'
    - line: '|   webGLShowDiagnostics: 0'
    - line: '|   webGLDataCaching: 1'
    - line: '|   webGLDebugSymbols: 0'
    - line: '|   webGLEmscriptenArgs: '
    - line: '|   webGLModulesDirectory: '
    - line: '|   webGLTemplate: APPLICATION:Default'
    - line: '|   webGLAnalyzeBuildSize: 0'
    - line: '|   webGLUseEmbeddedResources: 0'
    - line: '|   webGLCompressionFormat: 0'
    - line: '|   webGLWasmArithmeticExceptions: 0'
    - line: '|   webGLLinkerTarget: 1'
    - line: '|   webGLThreadsSupport: 0'
    - line: '|   webGLDecompressionFallback: 0'
    - line: '|   webGLInitialMemorySize: 32'
    - line: '|   webGLMaximumMemorySize: 2048'
    - line: '|   webGLMemoryGrowthMode: 2'
    - line: '|   webGLMemoryLinearGrowthStep: 16'
    - line: '|   webGLMemoryGeometricGrowthStep: 0.2'
    - line: '|   webGLMemoryGeometricGrowthCap: 96'
    - line: '|   webGLPowerPreference: 2'
    - line: '|   webGLWebAssemblyTable: 0'
    - line: '|   webGLWebAssemblyBigInt: 0'
    - line: '|   webGLCloseOnQuit: 0'
    - line: '|   webWasm2023: 0'
    - line: '|   webEnableSubmoduleStrippingCompatibility: 0'
    - line: '|   scriptingDefineSymbols:'
    - line: '|     Android: RealtimeCSG;RealtimeCSG_1;RealtimeCSG_1_6;RealtimeCSG_1_6_01'
    - line: '|     EmbeddedLinux: RealtimeCSG;RealtimeCSG_1;RealtimeCSG_1_6;RealtimeCSG_1_6_01'
    - line: '|     GameCoreXboxOne: RealtimeCSG;RealtimeCSG_1;RealtimeCSG_1_6;RealtimeCSG_1_6_01'
    - line: '|     PS4: RealtimeCSG;RealtimeCSG_1;RealtimeCSG_1_6;RealtimeCSG_1_6_01'
    - line: '|     PS5: RealtimeCSG;RealtimeCSG_1;RealtimeCSG_1_6;RealtimeCSG_1_6_01'
    - line: '|     QNX: RealtimeCSG;RealtimeCSG_1;RealtimeCSG_1_6;RealtimeCSG_1_6_01'
    - line: '|     ReservedCFE: RealtimeCSG;RealtimeCSG_1;RealtimeCSG_1_6;RealtimeCSG_1_6_01'
    - line: '|     Standalone: MIRROR;MIRROR_81_OR_NEWER;MIRROR_82_OR_NEWER;MIRROR_83_OR_NEWER;MIRROR_84_OR_NEWER;MIRROR_85_OR_NEWER;MIRROR_86_OR_NEWER;MIRROR_89_OR_NEWER;MIRROR_90_OR_NEWER;MIRROR_93_OR_NEWER;EDGEGAP_PLUGIN_SERVERS;RealtimeCSG;RealtimeCSG_1;RealtimeCSG_1_6;RealtimeCSG_1_6_01;BAKERY_INCLUDED'
    - line: '|     VisionOS: RealtimeCSG;RealtimeCSG_1;RealtimeCSG_1_6;RealtimeCSG_1_6_01'
    - line: '|     WebGL: RealtimeCSG;RealtimeCSG_1;RealtimeCSG_1_6;RealtimeCSG_1_6_01'
    - line: '|     XboxOne: RealtimeCSG;RealtimeCSG_1;RealtimeCSG_1_6;RealtimeCSG_1_6_01'
    - line: '|     tvOS: RealtimeCSG;RealtimeCSG_1;RealtimeCSG_1_6;RealtimeCSG_1_6_01'
    - line: '|   additionalCompilerArguments: {}'
    - line: '|   platformArchitecture: {}'
    - line: '|   scriptingBackend:'
    - line: '|     Android: 1'
    - line: '|     Standalone: 0'
    - line: '|   il2cppCompilerConfiguration: {}'
    - line: '|   il2cppCodeGeneration: {}'
    - line: '|   il2cppStacktraceInformation: {}'
    - line: '|   managedStrippingLevel:'
    - line: '|     Android: 1'
    - line: '|     EmbeddedLinux: 1'
    - line: '|     GameCoreScarlett: 1'
    - line: '|     GameCoreXboxOne: 1'
    - line: '|     Nintendo Switch: 1'
    - line: '|     PS4: 1'
    - line: '|     PS5: 1'
    - line: '|     QNX: 1'
    - line: '|     VisionOS: 1'
    - line: '|     WebGL: 1'
    - line: '|     Windows Store Apps: 1'
    - line: '|     XboxOne: 1'
    - line: '|     iPhone: 1'
    - line: '|     tvOS: 1'
    - line: '|   incrementalIl2cppBuild: {}'
    - line: '|   suppressCommonWarnings: 1'
    - line: '|   allowUnsafeCode: 0'
    - line: '|   useDeterministicCompilation: 1'
    - line: '|   additionalIl2CppArgs: '
    - line: '|   scriptingRuntimeVersion: 1'
    - line: '|   gcIncremental: 1'
    - line: '|   gcWBarrierValidation: 0'
    - line: '|   apiCompatibilityLevelPerPlatform: {}'
    - line: '|   editorAssembliesCompatibilityLevel: 1'
    - line: '|   m_RenderingPath: 1'
    - line: '|   m_MobileRenderingPath: 1'
    - line: '|   metroPackageName: BLAME'
    - line: '|   metroPackageVersion: '
    - line: '|   metroCertificatePath: '
    - line: '|   metroCertificatePassword: '
    - line: '|   metroCertificateSubject: '
    - line: '|   metroCertificateIssuer: '
    - line: '|   metroCertificateNotAfter: 0000000000000000'
    - line: '|   metroApplicationDescription: BLAME'
    - line: '|   wsaImages: {}'
    - line: '|   metroTileShortName: '
    - line: '|   metroTileShowName: 0'
    - line: '|   metroMediumTileShowName: 0'
    - line: '|   metroLargeTileShowName: 0'
    - line: '|   metroWideTileShowName: 0'
    - line: '|   metroSupportStreamingInstall: 0'
    - line: '|   metroLastRequiredScene: 0'
    - line: '|   metroDefaultTileSize: 1'
    - line: '|   metroTileForegroundText: 2'
    - line: '|   metroTileBackgroundColor: {r: 0.13333334, g: 0.17254902, b: 0.21568628,
        a: 0}'
    - line: '|   metroSplashScreenBackgroundColor: {r: 0.12941177, g: 0.17254902,
        b: 0.21568628, a: 1}'
    - line: '|   metroSplashScreenUseBackgroundColor: 0'
    - line: '|   syncCapabilities: 0'
    - line: '|   platformCapabilities: {}'
    - line: '|   metroTargetDeviceFamilies: {}'
    - line: '|   metroFTAName: '
    - line: '|   metroFTAFileTypes: []'
    - line: '|   metroProtocolName: '
    - line: '|   vcxProjDefaultLanguage: '
    - line: '|   XboxOneProductId: '
    - line: '|   XboxOneUpdateKey: '
    - line: '|   XboxOneSandboxId: '
    - line: '|   XboxOneContentId: '
    - line: '|   XboxOneTitleId: '
    - line: '|   XboxOneSCId: '
    - line: '|   XboxOneGameOsOverridePath: '
    - line: '|   XboxOnePackagingOverridePath: '
    - line: '|   XboxOneAppManifestOverridePath: '
    - line: '|   XboxOneVersion: *******'
    - line: '|   XboxOnePackageEncryption: 0'
    - line: '|   XboxOnePackageUpdateGranularity: 2'
    - line: '|   XboxOneDescription: '
    - line: '|   XboxOneLanguage:'
    - line: '|   - enus'
    - line: '|   XboxOneCapability: []'
    - line: '|   XboxOneGameRating: {}'
    - line: '|   XboxOneIsContentPackage: 0'
    - line: '|   XboxOneEnhancedXboxCompatibilityMode: 0'
    - line: '|   XboxOneEnableGPUVariability: 1'
    - line: '|   XboxOneSockets: {}'
    - line: '|   XboxOneSplashScreen: {instanceID: 0}'
    - line: '|   XboxOneAllowedProductIds: []'
    - line: '|   XboxOnePersistentLocalStorageSize: 0'
    - line: '|   XboxOneXTitleMemory: 8'
    - line: '|   XboxOneOverrideIdentityName: '
    - line: '|   XboxOneOverrideIdentityPublisher: '
    - line: '|   vrEditorSettings: {}'
    - line: '|   cloudServicesEnabled: {}'
    - line: '|   luminIcon:'
    - line: '|     m_Name: '
    - line: '|     m_ModelFolderPath: '
    - line: '|     m_PortalFolderPath: '
    - line: '|   luminCert:'
    - line: '|     m_CertPath: '
    - line: '|     m_SignPackage: 1'
    - line: '|   luminIsChannelApp: 0'
    - line: '|   luminVersion:'
    - line: '|     m_VersionCode: 1'
    - line: '|     m_VersionName: '
    - line: '|   hmiPlayerDataPath: '
    - line: '|   hmiForceSRGBBlit: 1'
    - line: '|   embeddedLinuxEnableGamepadInput: 0'
    - line: '|   hmiCpuConfiguration: '
    - line: '|   hmiLogStartupTiming: 0'
    - line: '|   qnxGraphicConfPath: '
    - line: '|   apiCompatibilityLevel: 6'
    - line: '|   captureStartupLogs: {}'
    - line: '|   activeInputHandler: 2'
    - line: '|   windowsGamepadBackendHint: 0'
    - line: '|   cloudProjectId: 7c9b7749-5edc-4357-8dc7-2e6754f22067'
    - line: '|   framebufferDepthMemorylessMode: 0'
    - line: '|   qualitySettingsNames: []'
    - line: '|   projectName: BLAME'
    - line: '|   organizationId: ghena936'
    - line: '|   cloudEnabled: 0'
    - line: '|   legacyClampBlendShapeWeights: 0'
    - line: '|   hmiLoadingImage: {instanceID: 0}'
    - line: '|   platformRequiresReadableAssets: 0'
    - line: '|   virtualTexturingSupportEnabled: 0'
    - line: '|   insecureHttpOption: 0'
    - line: '|   androidVulkanDenyFilterList: []'
    - line: '|   androidVulkanAllowFilterList: []'
    - line: '|   androidVulkanDeviceFilterListAsset: {instanceID: 0}'
    - line: '| '
  references:
    version: 2
    RefIds:
    - rid: 6426552317248798720
      type: {class: WindowsPlatformSettings, ns: UnityEditor.WindowsStandalone, asm: UnityEditor.WindowsStandalone.Extensions}
      data:
        m_Development: 0
        m_ConnectProfiler: 0
        m_BuildWithDeepProfilingSupport: 0
        m_AllowDebugging: 0
        m_WaitForManagedDebugger: 0
        m_ManagedDebuggerFixedPort: 0
        m_ExplicitNullChecks: 0
        m_ExplicitDivideByZeroChecks: 0
        m_ExplicitArrayBoundsChecks: 0
        m_CompressionType: 0
        m_InstallInBuildFolder: 0
        m_InsightsSettingsContainer:
          m_BuildProfileEngineDiagnosticsState: 2
        m_WindowsBuildAndRunDeployTarget: 0
        m_Architecture: 0
        m_CreateSolution: 0
        m_CopyPDBFiles: 0
        m_WindowsDevicePortalAddress: 
        m_WindowsDevicePortalUsername: 
