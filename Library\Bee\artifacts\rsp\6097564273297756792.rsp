-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Assembly-CSharp.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Autodesk.Fbx.BuildTestAssets.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Autodesk.Fbx.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\BakeryRuntimeAssembly.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Domain_Reload.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Tayx.Graphy.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.2D.Common.Runtime.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.AI.Animate.Motion.Runtime.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.AppUI.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.AppUI.InternalAPIBridge.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.AppUI.MVVM.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.AppUI.Navigation.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.AppUI.Redux.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.AppUI.Undo.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.Burst.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.Collections.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.Formats.Fbx.Runtime.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.InferenceEngine.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.InferenceEngine.iOSBLAS.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.InferenceEngine.MacBLAS.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.InternalAPIEngineBridge.001.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.Mathematics.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.Multiplayer.Center.Common.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.PerformanceTesting.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.ProBuilder.Csg.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.ProBuilder.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.ProBuilder.KdTree.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.ProBuilder.Poly2Tri.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.ProBuilder.Stl.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.ProGrids.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.Recorder.Base.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.Recorder.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.Rendering.LightTransport.Runtime.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.RenderPipelines.Core.Runtime.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.RenderPipelines.Core.Runtime.Shared.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.RenderPipelines.Core.Samples.Runtime.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.RenderPipelines.Core.ShaderLibrary.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.RenderPipelines.GPUDriven.Runtime.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.RenderPipelines.HighDefinition.Config.Runtime.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.RenderPipelines.HighDefinition.Runtime.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.Serialization.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.TextMeshPro.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.Timeline.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.VisualEffectGraph.Runtime.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.VisualScripting.Core.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.VisualScripting.Flow.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\Unity.VisualScripting.State.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\UnityEngine.TestRunner.dll"
-a="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies\UnityEngine.UI.dll"
-a="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.ai.assistant@91c166a13c3b\Plugins\CodeAnalysis\Microsoft.CodeAnalysis.CSharp.dll"
-a="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.ai.assistant@91c166a13c3b\Plugins\CodeAnalysis\Microsoft.CodeAnalysis.dll"
-a="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.ai.assistant@91c166a13c3b\Plugins\CodeAnalysis\System.Collections.Immutable.dll"
-a="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.ai.assistant@91c166a13c3b\Plugins\CodeAnalysis\System.Reflection.Metadata.dll"
-a="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.ai.assistant@91c166a13c3b\Plugins\Markdig\Markdig.dll"
-a="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.burst@6aff1dd08a0c\Unity.Burst.CodeGen\Unity.Burst.Cecil.dll"
-a="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.burst@6aff1dd08a0c\Unity.Burst.CodeGen\Unity.Burst.Cecil.Mdb.dll"
-a="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.burst@6aff1dd08a0c\Unity.Burst.CodeGen\Unity.Burst.Cecil.Pdb.dll"
-a="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.burst@6aff1dd08a0c\Unity.Burst.CodeGen\Unity.Burst.Cecil.Rocks.dll"
-a="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.burst@6aff1dd08a0c\Unity.Burst.Unsafe.dll"
-a="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.collections@d49facba0036\Unity.Collections.LowLevel.ILSupport\Unity.Collections.LowLevel.ILSupport.dll"
-a="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.collections@d49facba0036\Unity.Collections.Tests\System.IO.Hashing\System.IO.Hashing.dll"
-a="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.collections@d49facba0036\Unity.Collections.Tests\System.Runtime.CompilerServices.Unsafe\System.Runtime.CompilerServices.Unsafe.dll"
-a="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.ext.nunit@031a54704bff\net40\unity-custom\nunit.framework.dll"
-a="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.nuget.mono-cecil@d78732e851eb\Mono.Cecil.dll"
-a="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.nuget.mono-cecil@d78732e851eb\Mono.Cecil.Mdb.dll"
-a="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.nuget.mono-cecil@d78732e851eb\Mono.Cecil.Pdb.dll"
-a="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.nuget.mono-cecil@d78732e851eb\Mono.Cecil.Rocks.dll"
-a="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.nuget.newtonsoft-json@74deb55db2a0\Runtime\Newtonsoft.Json.dll"
-a="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.testtools.codecoverage@205a02cbcb39\lib\ReportGenerator\ReportGeneratorMerged.dll"
-a="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Flow\Dependencies\NCalc\Unity.VisualScripting.Antlr3.Runtime.dll"
-s="C:\Unity\Editors\6000.2.0f1\Editor\Data\Managed\UnityEngine"
-s="C:\Unity\Editors\6000.2.0f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx"
-s="C:\Unity\Editors\6000.2.0f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard"
-s="C:\Unity\Editors\6000.2.0f1\Editor\Data\NetStandard\EditorExtensions"
-s="C:\Unity\Editors\6000.2.0f1\Editor\Data\NetStandard\Extensions\2.0.0"
-s="C:\Unity\Editors\6000.2.0f1\Editor\Data\NetStandard\ref\2.1.0"
-s="C:\Unity\BLAME\BLAME\Library\ScriptAssemblies"
-s="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.ai.assistant@91c166a13c3b\Plugins\CodeAnalysis"
-s="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.ai.assistant@91c166a13c3b\Plugins\Markdig"
-s="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.burst@6aff1dd08a0c"
-s="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.burst@6aff1dd08a0c\Unity.Burst.CodeGen"
-s="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.collections@d49facba0036\Unity.Collections.LowLevel.ILSupport"
-s="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.collections@d49facba0036\Unity.Collections.Tests\System.IO.Hashing"
-s="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.collections@d49facba0036\Unity.Collections.Tests\System.Runtime.CompilerServices.Unsafe"
-s="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.ext.nunit@031a54704bff\net40\unity-custom"
-s="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.nuget.mono-cecil@d78732e851eb"
-s="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.nuget.newtonsoft-json@74deb55db2a0\Runtime"
-s="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.testtools.codecoverage@205a02cbcb39\lib\ReportGenerator"
-s="C:\Unity\BLAME\BLAME\Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Flow\Dependencies\NCalc"
-o="Library/BuildPlayerData/Editor"
-rn=""
-tn="TypeDb-All.json"