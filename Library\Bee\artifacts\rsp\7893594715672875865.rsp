--allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AIModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AMDModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ARModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AccessibilityModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AndroidJNIModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AnimationModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AssetBundleModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AudioModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClothModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClusterInputModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClusterRendererModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ContentLoadModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.CoreModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.CrashReportingModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.DSPGraphModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.DirectorModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GIModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GameCenterModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GraphicsStateCollectionSerializerModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GridModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.HierarchyCoreModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.HotReloadModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.IMGUIModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.IdentifiersModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ImageConversionModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputForUIModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputLegacyModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InsightsModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.JSONSerializeModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.LocalizationModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.MarshallingModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.MultiplayerModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.NVIDIAModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ParticleSystemModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PerformanceReportingModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.Physics2DModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PhysicsModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PropertiesModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ScreenCaptureModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ShaderVariantAnalyticsModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SharedInternalsModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SpriteMaskModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SpriteShapeModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.StreamingModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SubstanceModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SubsystemsModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TLSModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TerrainModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TerrainPhysicsModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextCoreFontEngineModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextCoreTextEngineModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextRenderingModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TilemapModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UIElementsModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UIModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UmbraModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityAnalyticsCommonModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityAnalyticsModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityConnectModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityConsentModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityCurlModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityTestProtocolModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestAudioModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestTextureModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestWWWModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VFXModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VRModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VehiclesModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VideoModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VirtualTexturingModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.WindModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.XRModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Autodesk.Fbx.BuildTestAssets.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Autodesk.Fbx.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/BakeryRuntimeAssembly.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Domain_Reload.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Tayx.Graphy.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.2D.Common.Runtime.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.InternalAPIBridge.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.MVVM.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.Navigation.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.Redux.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.Undo.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Burst.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Collections.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Formats.Fbx.Runtime.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.InferenceEngine.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.InternalAPIEngineBridge.001.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Csg.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.KdTree.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Poly2Tri.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Stl.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProGrids.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Recorder.Base.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Recorder.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Samples.Runtime.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Runtime.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Serialization.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.VisualEffectGraph.Runtime.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.IO.Hashing/System.IO.Hashing.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.burst@6aff1dd08a0c/Unity.Burst.Unsafe.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/AOT/Newtonsoft.Json.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Accessibility.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Commons.Xml.Relaxng.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/CustomMarshalers.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/Microsoft.Win32.Primitives.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/Microsoft.Win32.Registry.AccessControl.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/Microsoft.Win32.Registry.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.AppContext.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Buffers.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Collections.Concurrent.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Collections.NonGeneric.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Collections.Specialized.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Collections.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ComponentModel.Annotations.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ComponentModel.EventBasedAsync.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ComponentModel.Primitives.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ComponentModel.TypeConverter.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ComponentModel.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Console.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Data.Common.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Data.SqlClient.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.Contracts.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.Debug.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.FileVersionInfo.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.Process.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.StackTrace.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.TextWriterTraceListener.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.Tools.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.TraceEvent.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.TraceSource.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.Tracing.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Drawing.Primitives.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Dynamic.Runtime.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Globalization.Calendars.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Globalization.Extensions.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Globalization.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.Compression.ZipFile.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.FileSystem.AccessControl.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.FileSystem.DriveInfo.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.FileSystem.Primitives.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.FileSystem.Watcher.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.FileSystem.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.IsolatedStorage.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.MemoryMappedFiles.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.Pipes.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.UnmanagedMemoryStream.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Linq.Expressions.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Linq.Parallel.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Linq.Queryable.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Linq.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Memory.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.AuthenticationManager.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Cache.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Http.Rtc.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.HttpListener.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Mail.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.NameResolution.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.NetworkInformation.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Ping.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Primitives.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Requests.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Security.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.ServicePoint.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Sockets.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Utilities.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.WebHeaderCollection.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.WebSockets.Client.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.WebSockets.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ObjectModel.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.DispatchProxy.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.Emit.ILGeneration.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.Emit.Lightweight.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.Emit.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.Extensions.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.Primitives.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.TypeExtensions.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Resources.Reader.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Resources.ReaderWriter.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Resources.ResourceManager.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Resources.Writer.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.CompilerServices.VisualC.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Extensions.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Handles.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.InteropServices.RuntimeInformation.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.InteropServices.WindowsRuntime.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.InteropServices.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Loader.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Numerics.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Serialization.Formatters.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Serialization.Json.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Serialization.Primitives.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Serialization.Xml.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.AccessControl.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Claims.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Algorithms.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Cng.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Csp.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.DeriveBytes.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Encoding.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Encryption.Aes.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Encryption.ECDiffieHellman.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Encryption.ECDsa.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Encryption.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Hashing.Algorithms.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Hashing.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.OpenSsl.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Pkcs.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Primitives.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.ProtectedData.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.RSA.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.RandomNumberGenerator.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.X509Certificates.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Principal.Windows.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Principal.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.SecureString.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ServiceModel.Duplex.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ServiceModel.Http.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ServiceModel.NetTcp.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ServiceModel.Primitives.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ServiceModel.Security.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ServiceProcess.ServiceController.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Text.Encoding.CodePages.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Text.Encoding.Extensions.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Text.Encoding.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Text.RegularExpressions.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.AccessControl.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.Overlapped.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.Tasks.Extensions.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.Tasks.Parallel.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.Tasks.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.Thread.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.ThreadPool.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.Timer.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ValueTuple.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.ReaderWriter.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.XDocument.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.XPath.XDocument.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.XPath.XmlDocument.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.XPath.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.XmlDocument.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.XmlSerializer.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.Xsl.Primitives.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/netstandard.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/I18N.CJK.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/I18N.MidEast.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/I18N.Other.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/I18N.Rare.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/I18N.West.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/I18N.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/IBM.Data.DB2.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/ICSharpCode.SharpZipLib.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.Build.Engine.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.Build.Framework.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.Build.Tasks.v4.0.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.Build.Utilities.v4.0.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.Build.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.CSharp.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.VisualC.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.Web.Infrastructure.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Btls.Interface.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.C5.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.CSharp.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Cairo.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.CompilerServices.SymbolWriter.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Data.Sqlite.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Data.Tds.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Http.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Management.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Messaging.RabbitMQ.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Messaging.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Options.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Parallel.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Posix.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Profiler.Log.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Security.Win32.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Security.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Simd.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Tasklets.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.WebBrowser.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.XBuild.Tasks.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Novell.Directory.Ldap.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/PEAPI.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/RabbitMQ.Client.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/SMDiagnostics.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ComponentModel.Composition.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ComponentModel.DataAnnotations.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Configuration.Install.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Configuration.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Core.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.DataSetExtensions.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.Entity.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.Linq.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.OracleClient.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.Services.Client.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.Services.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Deployment.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Design.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.DirectoryServices.Protocols.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.DirectoryServices.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Drawing.Design.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Drawing.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Dynamic.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.EnterpriseServices.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.IO.Compression.FileSystem.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.IO.Compression.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.IdentityModel.Selectors.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.IdentityModel.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Json.Microsoft.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Json.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Management.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Messaging.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Net.Http.Formatting.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Net.Http.WebRequest.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Net.Http.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Net.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Numerics.Vectors.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Numerics.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Core.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Debugger.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Experimental.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Interfaces.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Linq.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Observable.Aliases.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.PlatformServices.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Providers.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Runtime.Remoting.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Windows.Forms.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Windows.Threading.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reflection.Context.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Runtime.Caching.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Runtime.DurableInstancing.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Runtime.Remoting.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Runtime.Serialization.Formatters.Soap.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Runtime.Serialization.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Security.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.Activation.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.Discovery.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.Internals.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.Routing.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.Web.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceProcess.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Threading.Tasks.Dataflow.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Transactions.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Abstractions.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.ApplicationServices.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.DynamicData.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Extensions.Design.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Extensions.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Http.SelfHost.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Http.WebHost.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Http.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Mobile.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Mvc.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Razor.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.RegularExpressions.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Routing.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Services.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.WebPages.Deployment.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.WebPages.Razor.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.WebPages.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Windows.Forms.DataVisualization.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Windows.Forms.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Windows.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Workflow.Activities.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Workflow.ComponentModel.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Workflow.Runtime.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Xaml.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Xml.Linq.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Xml.Serialization.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Xml.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/SystemWebTestShim.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/WebMatrix.Data.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/WindowsBase.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/cscompmgd.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/monodoc.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/mscorlib.dll" --out="Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped" --include-link-xml="C:/Unity/BLAME/BLAME/Library/Bee/artifacts/UnityLinkerInputs/MethodsToPreserve.xml" --include-link-xml="C:/Unity/BLAME/BLAME/Library/Bee/artifacts/UnityLinkerInputs/TypesInScenes.xml" --include-link-xml="C:/Unity/BLAME/BLAME/Library/Bee/artifacts/UnityLinkerInputs/SerializedTypes.xml" --include-directory="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed" --include-directory="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies" --include-directory="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.LowLevel.ILSupport" --include-directory="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.IO.Hashing" --include-directory="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Runtime/VisualScripting.Flow/Dependencies/NCalc" --include-directory="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.burst@6aff1dd08a0c" --include-directory="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/AOT" --include-directory="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe" --include-directory="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32" --include-directory="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades" --rule-set=Copy --profiler-report --profiler-output-file="C:/Unity/BLAME/BLAME/Library/Bee/artifacts/unitylinker_dwek.traceevents" --dotnetprofile=unityaot-win32 --dotnetruntime=Mono --platform=WindowsDesktop --use-editor-options --engine-modules-asset-file="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/modules.asset" --editor-data-file="C:/Unity/BLAME/BLAME/Library/Bee/artifacts/UnityLinkerInputs/EditorToUnityLinkerData.json" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AIModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AMDModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ARModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AccessibilityModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AndroidJNIModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AnimationModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AssetBundleModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AudioModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClothModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClusterInputModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClusterRendererModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ContentLoadModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.CoreModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.CrashReportingModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.DSPGraphModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.DirectorModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GIModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GameCenterModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GraphicsStateCollectionSerializerModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GridModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.HierarchyCoreModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.HotReloadModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.IMGUIModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.IdentifiersModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ImageConversionModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputForUIModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputLegacyModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InsightsModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.JSONSerializeModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.LocalizationModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.MarshallingModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.MultiplayerModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.NVIDIAModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ParticleSystemModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PerformanceReportingModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.Physics2DModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PhysicsModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PropertiesModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ScreenCaptureModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ShaderVariantAnalyticsModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SharedInternalsModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SpriteMaskModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SpriteShapeModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.StreamingModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SubstanceModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SubsystemsModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TLSModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TerrainModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TerrainPhysicsModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextCoreFontEngineModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextCoreTextEngineModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextRenderingModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TilemapModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UIElementsModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UIModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UmbraModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityAnalyticsCommonModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityAnalyticsModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityConnectModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityConsentModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityCurlModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityTestProtocolModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestAudioModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestTextureModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestWWWModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VFXModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VRModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VehiclesModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VideoModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VirtualTexturingModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.WindModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.XRModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Autodesk.Fbx.BuildTestAssets.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Autodesk.Fbx.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/BakeryRuntimeAssembly.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Domain_Reload.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Tayx.Graphy.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.2D.Common.Runtime.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.InternalAPIBridge.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.MVVM.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.Navigation.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.Redux.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.Undo.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Burst.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Collections.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Formats.Fbx.Runtime.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.InferenceEngine.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.InternalAPIEngineBridge.001.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Csg.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.KdTree.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Poly2Tri.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Stl.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProGrids.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Recorder.Base.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Recorder.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Samples.Runtime.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Runtime.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Serialization.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.VisualEffectGraph.Runtime.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.IO.Hashing/System.IO.Hashing.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.burst@6aff1dd08a0c/Unity.Burst.Unsafe.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/AOT/Newtonsoft.Json.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll" --print-command-line --enable-analytics