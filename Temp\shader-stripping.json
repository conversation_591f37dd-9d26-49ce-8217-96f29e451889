{"totalVariantsIn": 320933, "totalVariantsOut": 5770, "shaders": [{"inputVariants": 6, "outputVariants": 6, "name": "Shader Graphs/PhysicallyBasedSky", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "PBR Sky cubemap (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 11.0699}, {"inputVariants": 2, "outputVariants": 2, "variantName": "PBR Sky cubemap (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1396}, {"inputVariants": 1, "outputVariants": 1, "variantName": "PBR Sky (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0632}, {"inputVariants": 2, "outputVariants": 2, "variantName": "PBR Sky (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06330000000000001}]}]}, {"inputVariants": 434, "outputVariants": 122, "name": "Shader Graphs/Water", "pipelines": [{"inputVariants": 434, "outputVariants": 122, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 24, "outputVariants": 12, "variantName": "WaterGBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.0808}, {"inputVariants": 72, "outputVariants": 12, "variantName": "WaterGBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.5636}, {"inputVariants": 24, "outputVariants": 12, "variantName": "WaterGBufferTessellation (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5843}, {"inputVariants": 72, "outputVariants": 12, "variantName": "WaterGBufferTessellation (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.7181000000000001}, {"inputVariants": 24, "outputVariants": 12, "variantName": "WaterGBufferTessellation (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Hull)", "stripTimeMs": 0.4273}, {"inputVariants": 24, "outputVariants": 12, "variantName": "WaterGBufferTessellation (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Domain)", "stripTimeMs": 0.277}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LowRes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.043000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LowRes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.044700000000000004}, {"inputVariants": 48, "outputVariants": 12, "variantName": "WaterMask (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2843}, {"inputVariants": 48, "outputVariants": 12, "variantName": "WaterMask (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.3909}, {"inputVariants": 48, "outputVariants": 12, "variantName": "WaterMaskLowRes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2796}, {"inputVariants": 48, "outputVariants": 12, "variantName": "WaterMaskLowRes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.5078}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Shader Graphs/Water Decal", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Deformation (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5589000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Deformation (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0721}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Foam (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0954}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Foam (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0567}]}]}, {"inputVariants": 37, "outputVariants": 16, "name": "Shader Graphs/SolidColor", "pipelines": [{"inputVariants": 37, "outputVariants": 16, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5690000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0673}, {"inputVariants": 2, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0721}, {"inputVariants": 4, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0591}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.053200000000000004}, {"inputVariants": 4, "outputVariants": 2, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.056400000000000006}, {"inputVariants": 4, "outputVariants": 2, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0568}, {"inputVariants": 4, "outputVariants": 2, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.056}, {"inputVariants": 2, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0046}, {"inputVariants": 2, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0345}, {"inputVariants": 2, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0494}, {"inputVariants": 2, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0313}, {"inputVariants": 2, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0318}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0463}, {"inputVariants": 2, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0315}]}]}, {"inputVariants": 9, "outputVariants": 9, "name": "HDRP/DefaultFogVolume", "pipelines": [{"inputVariants": 9, "outputVariants": 9, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "FogVolumeVoxelize (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5553}, {"inputVariants": 2, "outputVariants": 2, "variantName": "FogVolumeVoxelize (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0925}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ShaderGraphPreview (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0729}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShaderGraphPreview (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1013}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VolumetricFogVFXOverdrawDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.034300000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "VolumetricFogVFXOverdrawDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.09770000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Shader Graphs/Sample Water Decal", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Deformation (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.1346}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Deformation (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0538}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Foam (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.053000000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Foam (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0644}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Skybox/Cubemap", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.034800000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0211}]}]}, {"inputVariants": 144, "outputVariants": 144, "name": "Hidden/HDRP/Sky/CloudLayer", "pipelines": [{"inputVariants": 144, "outputVariants": 144, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 24, "outputVariants": 24, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5678000000000001}, {"inputVariants": 24, "outputVariants": 24, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.2301}, {"inputVariants": 48, "outputVariants": 48, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4484}, {"inputVariants": 48, "outputVariants": 48, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.5701}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/PostProcessing/Debug/Waveform", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0119}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/DLSSBiasColorMask", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5391}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.041600000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/WaterCaustics", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4248}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.034300000000000004}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/ApplyDistortion", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4798}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0315}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/CameraMotionVectors", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.49460000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0315}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/HDRP/CompositeUI", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4803}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0847}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/App UI/CanvasBackground", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "GridBackground (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4328}, {"inputVariants": 2, "outputVariants": 2, "variantName": "GridBackground (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0325}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/VoxelizeShader", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3594}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0285}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0199}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0234}]}]}, {"inputVariants": 4, "outputVariants": 0, "name": "Hidden/HDRP/DebugExposure", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0057}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.004200000000000001}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/HDRP/GGXConvolve", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3648}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0339}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/preIntegratedFGD_GGXDisneyDiffuse", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3504}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0245}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/CoreSRP/CoreCopy", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Copy (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.43960000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Copy (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0485}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CopyMS (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.022000000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "CopyMS (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0403}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/HDRP/UpsampleTransparent", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4067}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.020800000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.024300000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0402}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0213}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.043500000000000004}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/Sky/GradientSky", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5352}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0218}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.025900000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0274}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/CombineLighting", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4803}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0263}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.024200000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.023}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/ColorPyramidPS", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.38530000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0201}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.027200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.022500000000000003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/PreIntegratedFGD_<PERSON>ner", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.39080000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0218}]}]}, {"inputVariants": 44, "outputVariants": 24, "name": "Hidden/HDRP/OpaqueAtmosphericScattering", "pipelines": [{"inputVariants": 44, "outputVariants": 24, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.35250000000000004}, {"inputVariants": 4, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0369}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0268}, {"inputVariants": 4, "outputVariants": 2, "variantName": "MSAA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.049}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Polychromatic Alpha (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.021}, {"inputVariants": 16, "outputVariants": 8, "variantName": "Polychromatic Alpha (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.13340000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA + Polychromatic Alpha (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.019}, {"inputVariants": 16, "outputVariants": 8, "variantName": "MSAA + Polychromatic Alpha (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1311}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/ClearBlack", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5183}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0357}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/VFX/Empty", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.49060000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0307}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/FallbackError", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4561}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0309}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/MaterialLoading", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5009}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0504}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/ProbeVolumeFragmentationDebug", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4808}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0347}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/Core/ProbeVolumeDebug", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "ForwardOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/preIntegratedFGD_CharlieFabricLambert", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4091}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.024800000000000003}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/HDRP/CopyStencilBuffer", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 - Copy stencilRef to output (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.44530000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 - Copy stencilRef to output (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.026500000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 - Write 1 if value different from stencilRef to output (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0228}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 - Write 1 if value different from stencilRef to output (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0247}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 - Export HTILE for stencilRef to output (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0262}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 - Export HTILE for stencilRef to output (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.021500000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 - Initialize Stencil UAV copy with 1 if value different from stencilRef to output (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07400000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 - Initialize Stencil UAV copy with 1 if value different from stencilRef to output (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0256}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 4 - Update Stencil UAV copy with Stencil Ref (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0247}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 4 - Update Stencil UAV copy with Stencil Ref (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0267}]}]}, {"inputVariants": 1284, "outputVariants": 1284, "name": "Hidden/HDRP/TemporalAA", "pipelines": [{"inputVariants": 1284, "outputVariants": 1284, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "TAA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4249}, {"inputVariants": 320, "outputVariants": 320, "variantName": "TAA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 4.0545}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Excluded From TAA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.046}, {"inputVariants": 320, "outputVariants": 320, "variantName": "Excluded From TAA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 3.3215000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "TAAU (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0539}, {"inputVariants": 320, "outputVariants": 320, "variantName": "TAAU (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 2.8735}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Copy History (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0489}, {"inputVariants": 320, "outputVariants": 320, "variantName": "Copy History (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 3.0427}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/CopyDepthBuffer", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Copy Depth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6376000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Copy Depth (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0456}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/DebugViewMaterialGBuffer", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0055000000000000005}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/XROcclusionMesh", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3945}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0367}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/IntegrateHDRI", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3795}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0244}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Shader/ChromaKeying", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ChromaKeying (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5205000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ChromaKeying (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0297}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Renderers/Thickness", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "ComputeThicknessOpaque (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.33580000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ComputeThicknessOpaque (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029500000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ComputeThicknessTransparent (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0275}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ComputeThicknessTransparent (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0359}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Shader/AlphaInjection", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "AlphaInjection (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3638}, {"inputVariants": 1, "outputVariants": 1, "variantName": "AlphaInjection (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0218}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/DebugVTBlit", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0033}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/CharlieConvolve", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0046}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/CubeToPano", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.39490000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0234}]}]}, {"inputVariants": 769, "outputVariants": 257, "name": "Hidden/HDRP/FinalPass", "pipelines": [{"inputVariants": 769, "outputVariants": 257, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4053}, {"inputVariants": 768, "outputVariants": 256, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 4.0705}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/VrsVisualization", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "VrsVisualization (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.40240000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VrsVisualization (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.030100000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Outline", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Custom Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.46640000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Custom Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.024900000000000002}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/HDRP/DownsampleDepth", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5114000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.058100000000000006}]}]}, {"inputVariants": 288790, "outputVariants": 486, "name": "HDRP/Lit", "pipelines": [{"inputVariants": 288790, "outputVariants": 486, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 120, "outputVariants": 48, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.2564}, {"inputVariants": 5184, "outputVariants": 240, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 12.4963}, {"inputVariants": 12, "outputVariants": 12, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1595}, {"inputVariants": 12, "outputVariants": 12, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.24020000000000002}, {"inputVariants": 144, "outputVariants": 24, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.7578}, {"inputVariants": 360, "outputVariants": 30, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1.1117000000000001}, {"inputVariants": 72, "outputVariants": 12, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3749}, {"inputVariants": 144, "outputVariants": 12, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.42800000000000005}, {"inputVariants": 36, "outputVariants": 6, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.20720000000000002}, {"inputVariants": 36, "outputVariants": 6, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1558}, {"inputVariants": 144, "outputVariants": 12, "variantName": "TransparentBackface (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.48260000000000003}, {"inputVariants": 93312, "outputVariants": 24, "variantName": "TransparentBackface (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 136.4219}, {"inputVariants": 144, "outputVariants": 12, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.3875}, {"inputVariants": 186624, "outputVariants": 24, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 295.4663}, {"inputVariants": 18, "outputVariants": 6, "variantName": "TransparentDepthPostpass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.18860000000000002}, {"inputVariants": 18, "outputVariants": 6, "variantName": "TransparentDepthPostpass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1068}, {"inputVariants": 4, "outputVariants": 0, "variantName": "RayTracingPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0067}, {"inputVariants": 12, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.031400000000000004}, {"inputVariants": 864, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 2.5477000000000003}, {"inputVariants": 432, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 1.3742}, {"inputVariants": 864, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 2.7579000000000002}, {"inputVariants": 12, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1345}, {"inputVariants": 72, "outputVariants": 0, "variantName": "SubSurfaceDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.43670000000000003}, {"inputVariants": 6, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0901}, {"inputVariants": 144, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.5648000000000001}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Hidden/HDRP/DepthValues", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.41100000000000003}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0519}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0196}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0522}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.044700000000000004}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.055}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0184}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.049600000000000005}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/App UI/CircularProgress", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.36250000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.049600000000000005}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/MaterialError", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3393}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0499}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/PostProcessing/Debug/Vectorscope", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0044}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/HDRP/CustomPassUtils", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Copy (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.41850000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Copy (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CopyDepth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.022500000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CopyDepth (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.021400000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Downsample (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.022000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Downsample (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0357}, {"inputVariants": 1, "outputVariants": 1, "variantName": "HorizontalBlur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0205}, {"inputVariants": 1, "outputVariants": 1, "variantName": "HorizontalBlur (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027800000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VerticalBlur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0285}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VerticalBlur (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.031200000000000002}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/DebugColorPicker", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0039000000000000003}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/HDRP/DebugLocalVolumetricFogAtlas", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0038}]}]}, {"inputVariants": 26, "outputVariants": 26, "name": "Hidden/HDRP/LensFlareDataDriven", "pipelines": [{"inputVariants": 26, "outputVariants": 26, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlareAdditive (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5028}, {"inputVariants": 4, "outputVariants": 4, "variantName": "LensFlareAdditive (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.07980000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlareScreen (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0335}, {"inputVariants": 4, "outputVariants": 4, "variantName": "LensFlareScreen (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.044700000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlarePremultiply (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0364}, {"inputVariants": 4, "outputVariants": 4, "variantName": "LensFlarePremultiply (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.045200000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlareLerp (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0363}, {"inputVariants": 4, "outputVariants": 4, "variantName": "LensFlareLerp (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.052500000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareOcclusion (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0182}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareOcclusion (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0238}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/FallbackError", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4283}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.034}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/HDRP/CompositeLines", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeAll (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3179}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeAll (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0378}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeColorOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.018500000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeColorOnly (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.028300000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeDepthMovecOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0307}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeDepthMovecOnly (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0332}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/HDRP/DebugLightVolumes", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0039000000000000003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/ClearStencilBuffer", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4162}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0287}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/ScriptableRenderPipeline/DebugDisplayHDShadowMap", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "RegularShadow (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3665}, {"inputVariants": 1, "outputVariants": 1, "variantName": "RegularShadow (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.028200000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VarianceShadow (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0184}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VarianceShadow (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0222}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/PostProcessing/SubpixelMorphologicalAntialiasing", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Edge detection (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4217}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Edge detection (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0471}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blend Weights Calculation (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0182}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Blend Weights Calculation (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.041100000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Neighborhood Blending (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.018500000000000003}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Neighborhood Blending (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0434}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/HDRP/LensFlareScreenSpace", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpac Prefilter (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3766}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpac Prefilter (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.019100000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Downsample (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0187}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Downsample (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0181}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Upsample (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0175}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Upsample (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.023700000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Composition (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0183}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Composition (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0178}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Write to BloomTexture (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Write to BloomTexture (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.018600000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/App UI/SVSquare", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3783}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0251}]}]}, {"inputVariants": 3, "outputVariants": 0, "name": "Hidden/HDRP/DebugHDR", "pipelines": [{"inputVariants": 3, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0032}]}]}, {"inputVariants": 868, "outputVariants": 28, "name": "Hidden/HDRP/Sky/HDRISky", "pipelines": [{"inputVariants": 868, "outputVariants": 28, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "FragBaking (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.30510000000000004}, {"inputVariants": 216, "outputVariants": 6, "variantName": "FragBaking (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.8013}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FragRender (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0263}, {"inputVariants": 216, "outputVariants": 6, "variantName": "FragRender (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.7462000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FragRenderBackplate (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0223}, {"inputVariants": 216, "outputVariants": 6, "variantName": "FragRenderBackplate (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.42810000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FragRenderBackplateDepth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.026000000000000002}, {"inputVariants": 216, "outputVariants": 6, "variantName": "FragRenderBackplateDepth (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.6181}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/CustomClear", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ClearColorAndStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.36660000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ClearColorAndStencil (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DrawTextureAndClearStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0183}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DrawTextureAndClearStencil (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0223}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/HDRP/Sky/PbrSky", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "PBRSky Cubemap (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.32220000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "PBRSky Cubemap (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.045200000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "PBRSky (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0221}, {"inputVariants": 2, "outputVariants": 2, "variantName": "PBRSky (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0361}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/PreIntegratedFGD_CookTorrance", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3437}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0415}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/App UI/ColorSwatch", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3689}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0269}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.019}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0181}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Hidden/HDRP/BlitColorAndDepth", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "ColorOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3694}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ColorOnly (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0792}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ColorAndDepth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0709}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ColorAndDepth (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.049600000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/DebugOccluder", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "DebugOccluder (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3306}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DebugOccluder (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0359}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/App UI/LinearProgress", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5126000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0442}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/WaterExclusion", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "StencilTag (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.42960000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "StencilTag (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0631}]}]}, {"inputVariants": 4, "outputVariants": 3, "name": "Hidden/Core/ProbeVolumeSamplingDebug", "pipelines": [{"inputVariants": 4, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ForwardOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3583}, {"inputVariants": 3, "outputVariants": 2, "variantName": "ForwardOnly (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0324}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/ScreenSpaceShadows", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0056}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/DebugDisplayLatlong", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0089}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/CoreResources/FilterAreaLightCookies", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3134}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.018000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0179}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.030100000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.017400000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.017400000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.022500000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027600000000000003}]}]}, {"inputVariants": 86, "outputVariants": 36, "name": "HDRP/Unlit", "pipelines": [{"inputVariants": 86, "outputVariants": 36, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3401}, {"inputVariants": 12, "outputVariants": 6, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.083}, {"inputVariants": 3, "outputVariants": 3, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.048600000000000004}, {"inputVariants": 12, "outputVariants": 6, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1158}, {"inputVariants": 6, "outputVariants": 3, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0497}, {"inputVariants": 12, "outputVariants": 6, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.09140000000000001}, {"inputVariants": 3, "outputVariants": 3, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0614}, {"inputVariants": 6, "outputVariants": 6, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06620000000000001}, {"inputVariants": 3, "outputVariants": 0, "variantName": "DistortionVectors (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.018000000000000002}, {"inputVariants": 6, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0227}, {"inputVariants": 4, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.059300000000000005}, {"inputVariants": 4, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.059300000000000005}, {"inputVariants": 2, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0407}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.08410000000000001}, {"inputVariants": 2, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0397}, {"inputVariants": 4, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0519}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/App UI/ColorWheel", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.43620000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.023200000000000002}]}]}, {"inputVariants": 16, "outputVariants": 0, "name": "Hidden/HDRP/DebugViewTiles", "pipelines": [{"inputVariants": 16, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 16, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.029900000000000003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ScriptableRenderPipeline/ShadowBlit", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "BlitShadows (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BlitShadows (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.028800000000000003}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Hidden/HDRP/CustomPassRenderersUtils", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "DepthToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.46940000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.035300000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.035300000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.034800000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "NormalToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0383}, {"inputVariants": 2, "outputVariants": 2, "variantName": "NormalToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0339}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TangentToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0495}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TangentToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.041100000000000005}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/HDRP/DebugBlitQuad", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0039000000000000003}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/HDRP/WaterDecal", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "DeformationDecal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3582}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DeformationDecal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0264}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FoamDecal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0218}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FoamDecal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0285}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MaskDecal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.022000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MaskDecal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0201}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LargeCurrentDecal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.023200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LargeCurrentDecal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.02}, {"inputVariants": 1, "outputVariants": 1, "variantName": "RipplesCurrentDecal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.020800000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "RipplesCurrentDecal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0234}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FoamAttenuation (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.02}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FoamAttenuation (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0318}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCubemap", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.30970000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0327}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/PreIntegratedFGD_Ward", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.364}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0229}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/DebugOcclusionTest", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3582}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0233}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/SRP/BlitCubeTextureFace", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3562}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.025900000000000003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Shader Graph/FallbackError", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.37420000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0273}]}]}, {"inputVariants": 4, "outputVariants": 3, "name": "Hidden/Core/ProbeVolumeOffsetDebug", "pipelines": [{"inputVariants": 4, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ForwardOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3528}, {"inputVariants": 3, "outputVariants": 2, "variantName": "ForwardOnly (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0437}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/HDRP/ColorResolve", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA1X (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3665}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA1X (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0313}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA2X (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.023700000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA2X (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.025500000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA4X (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.025500000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA4X (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.020900000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA8X (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0205}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA8X (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0213}]}]}, {"inputVariants": 22, "outputVariants": 22, "name": "Hidden/App UI/Box", "pipelines": [{"inputVariants": 22, "outputVariants": 22, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Clear (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3674}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Clear (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.022500000000000003}, {"inputVariants": 2, "outputVariants": 2, "variantName": "BoxShadows (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0431}, {"inputVariants": 2, "outputVariants": 2, "variantName": "BoxShadows (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0339}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BackgroundColor (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.021400000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BackgroundColor (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.020800000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BackgroundImage (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0281}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BackgroundImage (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0253}, {"inputVariants": 2, "outputVariants": 2, "variantName": "InsetBoxShadows (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0339}, {"inputVariants": 2, "outputVariants": 2, "variantName": "InsetBoxShadows (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.037200000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Border (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0383}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Border (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0376}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Outline (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0398}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Outline (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0531}]}]}, {"inputVariants": 200, "outputVariants": 200, "name": "Hidden/HDRP/Blit", "pipelines": [{"inputVariants": 200, "outputVariants": 200, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Nearest (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4067}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Nearest (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.05}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Bilinear (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0456}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Bilinear (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0536}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuad (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.057600000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuad (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0444}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuad (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0546}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuad (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0584}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0589}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPadding (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.055600000000000004}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0629}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPadding (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0591}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0616}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingRepeat (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.07880000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0931}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingRepeat (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0567}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingOctahedral (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.061700000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingOctahedral (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0466}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingAlphaBlend (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.057800000000000004}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingAlphaBlend (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.056900000000000006}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlend (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.10070000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlend (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0538}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingAlphaBlendRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0443}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingAlphaBlendRepeat (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06280000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlendRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07300000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlendRepeat (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0443}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlendOctahedral (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06330000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlendOctahedral (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.050800000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedral (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.077}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedral (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06430000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralLuminance (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0767}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralLuminance (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0704}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralAlpha (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.060000000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralAlpha (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0976}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralRed (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0453}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralRed (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.14150000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadLuminance (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0756}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadLuminance (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0903}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadAlpha (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0611}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadAlpha (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0728}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadRed (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.064}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadRed (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0497}, {"inputVariants": 8, "outputVariants": 8, "variantName": "NearestCubeToOctahedralPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1164}, {"inputVariants": 8, "outputVariants": 8, "variantName": "NearestCubeToOctahedralPadding (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0815}, {"inputVariants": 8, "outputVariants": 8, "variantName": "BilinearCubeToOctahedralPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0984}, {"inputVariants": 8, "outputVariants": 8, "variantName": "BilinearCubeToOctahedralPadding (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.10930000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ScriptableRenderPipeline/ShadowClear", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ClearShadow (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4516}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ClearShadow (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.026000000000000002}]}]}, {"inputVariants": 5, "outputVariants": 3, "name": "Hidden/HDRP/XRMirrorView", "pipelines": [{"inputVariants": 5, "outputVariants": 3, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4083}, {"inputVariants": 4, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0392}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/DebugFullScreen", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.016900000000000002}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/HDRP/MotionVecResolve", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4244}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.036500000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0201}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0223}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0213}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0269}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0172}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0171}]}]}, {"inputVariants": 3, "outputVariants": 2, "name": "Hidden/HDRP/Material/Decal/DecalNormalBuffer", "pipelines": [{"inputVariants": 3, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.31120000000000003}, {"inputVariants": 2, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0497}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/App UI/Mask", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3527}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.022500000000000003}]}]}, {"inputVariants": 73, "outputVariants": 28, "name": "Shader Graphs/TIPS_Mesh 2", "pipelines": [{"inputVariants": 73, "outputVariants": 28, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5264}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.10400000000000001}, {"inputVariants": 4, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.056100000000000004}, {"inputVariants": 8, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0555}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0825}, {"inputVariants": 8, "outputVariants": 4, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0893}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08950000000000001}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.09090000000000001}, {"inputVariants": 4, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0081}, {"inputVariants": 4, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0528}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0779}, {"inputVariants": 4, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.049100000000000005}, {"inputVariants": 4, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0522}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.027700000000000002}, {"inputVariants": 4, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0489}]}]}, {"inputVariants": 59, "outputVariants": 20, "name": "Shader Graphs/TIPS_Mesh", "pipelines": [{"inputVariants": 59, "outputVariants": 20, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4137}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.055400000000000005}, {"inputVariants": 4, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.069}, {"inputVariants": 8, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1283}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08940000000000001}, {"inputVariants": 4, "outputVariants": 2, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0825}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.16090000000000002}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.08850000000000001}, {"inputVariants": 2, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0046}, {"inputVariants": 4, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0698}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.091}, {"inputVariants": 4, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.050100000000000006}, {"inputVariants": 2, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0316}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0286}, {"inputVariants": 4, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0487}]}]}, {"inputVariants": 22211, "outputVariants": 56, "name": "Samples/SamplesLit_Inter", "pipelines": [{"inputVariants": 22211, "outputVariants": 56, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.8401000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0995}, {"inputVariants": 8, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1018}, {"inputVariants": 32, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.16790000000000002}, {"inputVariants": 4, "outputVariants": 2, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08360000000000001}, {"inputVariants": 4, "outputVariants": 2, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.059800000000000006}, {"inputVariants": 4, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0079}, {"inputVariants": 8, "outputVariants": 4, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1696}, {"inputVariants": 48, "outputVariants": 4, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.23650000000000002}, {"inputVariants": 16, "outputVariants": 4, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3647}, {"inputVariants": 576, "outputVariants": 16, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1.7129}, {"inputVariants": 16, "outputVariants": 4, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.18460000000000001}, {"inputVariants": 20736, "outputVariants": 8, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 33.0844}, {"inputVariants": 2, "outputVariants": 0, "variantName": "RayTracingPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0085}, {"inputVariants": 288, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 1.4851}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1721}, {"inputVariants": 24, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.35150000000000003}, {"inputVariants": 288, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 1.4614}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0488}, {"inputVariants": 144, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.7491}]}, {"inputVariants": 0, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn Forward (ForwardBase) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0002}, {"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn ForwardAdd (ForwardAdd) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0002}, {"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn Deferred (Deferred) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0001}, {"inputVariants": 0, "outputVariants": 0, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Unlit/Texture", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0461}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0269}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/ReduceExpBias", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5054000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027700000000000002}]}]}, {"inputVariants": 86, "outputVariants": 86, "name": "Hidden/InferenceEngine/Activation", "pipelines": [{"inputVariants": 86, "outputVariants": 86, "pipeline": "", "variants": [{"inputVariants": 43, "outputVariants": 43, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.1046}, {"inputVariants": 43, "outputVariants": 43, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.5212}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/Dense", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3805}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1899}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Hidden/InferenceEngine/Pad", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4605}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0804}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/Mat<PERSON>ul", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.35600000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.018500000000000003}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Transpose", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.35600000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0453}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/InferenceEngine/ActivationInt", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 6, "outputVariants": 6, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4828}, {"inputVariants": 6, "outputVariants": 6, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.13520000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Trilu", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5982000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.037000000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/InstanceNormalizationTail", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3662}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0318}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Tile", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3415}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0442}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "Hidden/InferenceEngine/TextureConversion/TensorToTexture", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6211}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1194}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/InferenceEngine/TextureTensorDataDownload", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3402}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.038700000000000005}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Hidden/InferenceEngine/CumSum", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4046}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.081}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/LayerNormalizationTail", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.30760000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.023100000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/IsInfNaN", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3931}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.031100000000000003}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/GatherElements", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3683}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0591}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "Hidden/InferenceEngine/TextureConversion/TextureToTensor", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4227}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1202}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "Hidden/InferenceEngine/DepthwiseConv", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.41050000000000003}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.116}]}]}, {"inputVariants": 1296, "outputVariants": 1296, "name": "Hidden/InferenceEngine/ConvTranspose", "pipelines": [{"inputVariants": 1296, "outputVariants": 1296, "pipeline": "", "variants": [{"inputVariants": 648, "outputVariants": 648, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.1054}, {"inputVariants": 648, "outputVariants": 648, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 7.9911}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/DepthToSpace", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6106}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0852}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "FullScreen/test", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Custom Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6527000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Custom Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0334}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/SpaceToDepth", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5105000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.044700000000000004}]}]}, {"inputVariants": 40, "outputVariants": 40, "name": "Hidden/InferenceEngine/ScatterND", "pipelines": [{"inputVariants": 40, "outputVariants": 40, "pipeline": "", "variants": [{"inputVariants": 20, "outputVariants": 20, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.753}, {"inputVariants": 20, "outputVariants": 20, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.3265}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "TextMeshPro/Distance Field", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6890000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.08850000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/Reshape", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5386000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0911}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "Hidden/InferenceEngine/Conv", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6749}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1955}]}]}, {"inputVariants": 72, "outputVariants": 72, "name": "Hidden/InferenceEngine/GridSample", "pipelines": [{"inputVariants": 72, "outputVariants": 72, "pipeline": "", "variants": [{"inputVariants": 36, "outputVariants": 36, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.9038000000000002}, {"inputVariants": 36, "outputVariants": 36, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.6667000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/TextureConversion/ComputeBufferToTexture", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5447000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0709}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Gather", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.48610000000000003}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0509}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Cast", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5192}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0523}]}]}, {"inputVariants": 70, "outputVariants": 70, "name": "Hidden/InferenceEngine/Broadcast", "pipelines": [{"inputVariants": 70, "outputVariants": 70, "pipeline": "", "variants": [{"inputVariants": 35, "outputVariants": 35, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.0723}, {"inputVariants": 35, "outputVariants": 35, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.46630000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/BatchNormalization", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4404}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.030100000000000002}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/Gemm", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.375}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0587}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ProBuilder/EdgePicker", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Edges (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3297}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Edges (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027100000000000003}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/TextureTensorDataUpload", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.47800000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0368}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "FullScreen/TIPS", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Compositing (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.36100000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Compositing (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0194}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0176}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blur (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0179}]}]}, {"inputVariants": 36, "outputVariants": 36, "name": "Hidden/InferenceEngine/Reduce", "pipelines": [{"inputVariants": 36, "outputVariants": 36, "pipeline": "", "variants": [{"inputVariants": 18, "outputVariants": 18, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4727}, {"inputVariants": 18, "outputVariants": 18, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.18530000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Softmax", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.44380000000000003}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0427}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/RoiAlign", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3915}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0461}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/Random", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.39390000000000003}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06330000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/HardmaxEnd", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.39940000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.035}]}]}, {"inputVariants": 80, "outputVariants": 80, "name": "Hidden/InferenceEngine/ScatterElements", "pipelines": [{"inputVariants": 80, "outputVariants": 80, "pipeline": "", "variants": [{"inputVariants": 40, "outputVariants": 40, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6863}, {"inputVariants": 40, "outputVariants": 40, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.8923000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/GatherND", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5712}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.065}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/ConstantOfShape", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4965}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.041600000000000005}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Where", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4707}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0466}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Copy", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.39390000000000003}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.030000000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/RMSNormalizationTail", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.34900000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.020900000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ProBuilder/VertexPicker", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Vertices (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3548}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Vertices (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.021}]}]}, {"inputVariants": 18, "outputVariants": 18, "name": "Hidden/InferenceEngine/Upsample", "pipelines": [{"inputVariants": 18, "outputVariants": 18, "pipeline": "", "variants": [{"inputVariants": 9, "outputVariants": 9, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5008}, {"inputVariants": 9, "outputVariants": 9, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1643}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/SliceSet", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3806}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.09680000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Expand", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5538000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0673}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/InferenceEngine/GlobalPool", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4983}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0623}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ProBuilder/FacePicker", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Base (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.515}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Base (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0344}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "TextMeshPro/Sprite", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.43570000000000003}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0458}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ProBuilder/HideVertices", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.456}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0235}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "Hidden/InferenceEngine/GroupedConv", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4988}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.12190000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/Split", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4652}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0766}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Range", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.35750000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0287}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/OneHot", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3267}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.030600000000000002}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/InferenceEngine/Resize1D", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3668}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0386}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/LocalPool", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.35400000000000004}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.07}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/ScalarMad", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.37}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029500000000000002}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Hidden/InferenceEngine/ReduceIndices", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.38180000000000003}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0888}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/Slice", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4253}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0763}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/ScaleBias", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3594}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0456}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/LayoutSwitchBlockedAxis", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4359}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0375}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/TopP", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.31120000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0245}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "TextMeshPro/Mobile/Distance Field", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5122}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.2068}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Legacy Shaders/Particles/Alpha Blended", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.052000000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0446}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TextCore/Sprite", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0218}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0194}]}]}, {"inputVariants": 1108, "outputVariants": 32, "name": "ProBuilder6/Standard Vertex Color", "pipelines": [{"inputVariants": 1108, "outputVariants": 32, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6945}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0682}, {"inputVariants": 4, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0908}, {"inputVariants": 16, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1432}, {"inputVariants": 2, "outputVariants": 0, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0074}, {"inputVariants": 2, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0043}, {"inputVariants": 4, "outputVariants": 2, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0918}, {"inputVariants": 24, "outputVariants": 2, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1283}, {"inputVariants": 8, "outputVariants": 4, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1197}, {"inputVariants": 288, "outputVariants": 16, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.8047000000000001}, {"inputVariants": 8, "outputVariants": 0, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1014}, {"inputVariants": 1, "outputVariants": 0, "variantName": "RayTracingPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0033}, {"inputVariants": 288, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.8293}, {"inputVariants": 2, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0529}, {"inputVariants": 24, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.3085}, {"inputVariants": 288, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.9745}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0316}, {"inputVariants": 144, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.4098}]}, {"inputVariants": 0, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn Forward (ForwardBase) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0002}, {"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn ForwardAdd (ForwardAdd) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0001}, {"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn Deferred (Deferred) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0001}, {"inputVariants": 0, "outputVariants": 0, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0001}, {"inputVariants": 0, "outputVariants": 0, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0001}]}]}, {"inputVariants": 1783, "outputVariants": 78, "name": "Shader Graphs/BakeryVolumeGraph", "pipelines": [{"inputVariants": 1783, "outputVariants": 78, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 2.5622000000000003}, {"inputVariants": 3, "outputVariants": 3, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1338}, {"inputVariants": 6, "outputVariants": 3, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2286}, {"inputVariants": 48, "outputVariants": 6, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.6374000000000001}, {"inputVariants": 3, "outputVariants": 0, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0221}, {"inputVariants": 3, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0129}, {"inputVariants": 6, "outputVariants": 3, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2609}, {"inputVariants": 72, "outputVariants": 6, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.6563}, {"inputVariants": 12, "outputVariants": 6, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.313}, {"inputVariants": 864, "outputVariants": 48, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 4.4595}, {"inputVariants": 12, "outputVariants": 0, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.328}, {"inputVariants": 2, "outputVariants": 0, "variantName": "RayTracingPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0067}, {"inputVariants": 288, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 1.3723}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1731}, {"inputVariants": 24, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.3548}, {"inputVariants": 288, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 1.3595000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0475}, {"inputVariants": 144, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.6742}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "HDRPSamples/LocalClouds", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "FogVolumeVoxelize (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.1472}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FogVolumeVoxelize (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.08080000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ShaderGraphPreview (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0781}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ShaderGraphPreview (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0591}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VolumetricFogVFXOverdrawDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0446}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VolumetricFogVFXOverdrawDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0539}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "CustomPass_SG/Outline", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "DrawProcedural (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.8572000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DrawProcedural (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0655}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blit (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0471}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blit (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.043500000000000004}]}]}, {"inputVariants": 59, "outputVariants": 20, "name": "Shader Graphs/Glitch_SG", "pipelines": [{"inputVariants": 59, "outputVariants": 20, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4733}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.052000000000000005}, {"inputVariants": 4, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0683}, {"inputVariants": 8, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.061500000000000006}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.049100000000000005}, {"inputVariants": 4, "outputVariants": 2, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.09680000000000001}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.12840000000000001}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1499}, {"inputVariants": 2, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0081}, {"inputVariants": 4, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1023}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1525}, {"inputVariants": 4, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.091}, {"inputVariants": 2, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0522}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0459}, {"inputVariants": 4, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.09040000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TerrainEngine/BillboardTree", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0349}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0347}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Nature/Tree Soft Occlusion Bark Rendertex", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0347}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0359}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Nature/Tree Soft Occlusion Leaves Rendertex", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0346}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.024}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TerrainEngine/CameraFacingBillboardTree", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0341}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.033800000000000004}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Nature/Tree Creator Albedo Rendertex", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.04}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.033}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Nature/Tree Creator Normal Rendertex", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0344}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0332}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/TerrainEngine/Splatmap/Standard-BaseGen", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0323}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.030100000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0323}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.031100000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0315}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029400000000000003}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/TerrainEngine/PaintHeight", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Raise/Lower Heights (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.031}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Raise/Lower Heights (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0328}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Stamp Heights (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0307}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Stamp Heights (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.030000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Set Heights (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.028800000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Set Heights (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0268}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Smooth Heights (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Smooth Heights (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.030500000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Paint Texture (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0263}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Paint Texture (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.031}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Paint Holes (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.029400000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Paint Holes (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0239}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TerrainEngine/HeightBlitCopy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.032600000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0309}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TerrainEngine/GenerateNormalmap", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0296}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/TerrainEngine/TerrainLayerUtils", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Get Terrain Layer Channel (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0303}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Get Terrain Layer Channel (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029400000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Set Terrain Layer Channel (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0279}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Set Terrain Layer Channel (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0284}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blit Copy Highest Mip (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.031900000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blit Copy Highest Mip (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.034}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TerrainEngine/CrossBlendNeighbors", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0286}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.033600000000000005}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Graphy/Graph Mobile", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5698}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.038900000000000004}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Graphy/Graph Standard", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5408000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.055200000000000006}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Legacy Shaders/VertexLit", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0883}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0431}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (VertexLM) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0224}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (VertexLM) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0216}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0328}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0304}]}]}, {"inputVariants": 46, "outputVariants": 46, "name": "Legacy Shaders/Diffuse", "pipelines": [{"inputVariants": 46, "outputVariants": 46, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1207}, {"inputVariants": 8, "outputVariants": 8, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.085}, {"inputVariants": 5, "outputVariants": 5, "variantName": "FORWARD (ForwardAdd) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0838}, {"inputVariants": 5, "outputVariants": 5, "variantName": "FORWARD (ForwardAdd) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.063}, {"inputVariants": 8, "outputVariants": 8, "variantName": "DEFERRED (Deferred) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0824}, {"inputVariants": 8, "outputVariants": 8, "variantName": "DEFERRED (Deferred) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.08460000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-StencilWrite", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.024900000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.020900000000000002}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "Hidden/Internal-DepthNormalsTexture", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0287}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.026600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0261}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.034300000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.020800000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Fragment)", "stripTimeMs": 0.0207}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.0195}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Fragment)", "stripTimeMs": 0.0198}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Vertex)", "stripTimeMs": 0.0292}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Fragment)", "stripTimeMs": 0.0204}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.0204}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.0308}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.0205}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.0205}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Vertex)", "stripTimeMs": 0.025500000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Fragment)", "stripTimeMs": 0.0291}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Vertex)", "stripTimeMs": 0.0201}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Fragment)", "stripTimeMs": 0.0253}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Vertex)", "stripTimeMs": 0.0246}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Fragment)", "stripTimeMs": 0.022000000000000002}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "Hidden/Internal-ScreenSpaceShadows", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0717}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.05}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0485}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0517}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0502}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Fragment)", "stripTimeMs": 0.05}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.0505}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Fragment)", "stripTimeMs": 0.07930000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-CombineDepthNormals", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.029500000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.030100000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0257}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0253}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopyDepth", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0347}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0256}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ConvertTexture", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.021500000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0252}]}]}, {"inputVariants": 54, "outputVariants": 54, "name": "Hidden/Internal-DeferredShading", "pipelines": [{"inputVariants": 54, "outputVariants": 54, "pipeline": "", "variants": [{"inputVariants": 26, "outputVariants": 26, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2939}, {"inputVariants": 26, "outputVariants": 26, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.24480000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.030500000000000003}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-DeferredReflections", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.029}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0304}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.050100000000000006}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0489}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-MotionVectors", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.022600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.022600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0205}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0261}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.021500000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0218}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-Flare", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0222}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.022000000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-Halo", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.030000000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopyWithDepth", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0298}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0216}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitToDepth", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0307}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0285}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitToDepth_MSAA", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0304}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0219}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopyHDRTonemap", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0216}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0246}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-DebugPattern", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Target Color and DepthStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0239}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target Color and DepthStencil (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0207}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only Color (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.021}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only Color (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0196}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only DepthStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0194}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only DepthStencil (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0233}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITextureClip", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0256}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.024}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.028200000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0228}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITextureClipText", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0257}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.020300000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.024200000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0199}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITexture", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.024800000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0252}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.022600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0284}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITextureBlit", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0252}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.020800000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.027800000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0302}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUIRoundedRect", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.025900000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0261}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0198}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.020900000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUIRoundedRectWithColorPerBorder", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.022000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.031400000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0292}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0199}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-UIRDefault", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0316}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0325}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-UIRAtlasBlitCopy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0224}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.021400000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-UIRDefaultWorld", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0211}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.02}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Sprites/Default", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0475}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.05}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Sprites/Mask", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.046900000000000004}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0787}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "UI/Default", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0592}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.048100000000000004}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "UI/DefaultETC1", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.060500000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.047900000000000005}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/CubeBlur", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0218}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.025500000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.023700000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0246}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/CubeCopy", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.024900000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0236}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0235}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/CubeBlend", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0267}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0263}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0247}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0198}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/VR/BlitTexArraySlice", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0205}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0201}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "Hidden/Internal-ODSWorldTexture", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.025400000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0257}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0217}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0198}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0217}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Fragment)", "stripTimeMs": 0.020200000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.019200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Fragment)", "stripTimeMs": 0.0247}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Vertex)", "stripTimeMs": 0.0206}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Fragment)", "stripTimeMs": 0.02}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.0201}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.0244}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.0199}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.023700000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Vertex)", "stripTimeMs": 0.0247}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Fragment)", "stripTimeMs": 0.0247}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Vertex)", "stripTimeMs": 0.0235}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Fragment)", "stripTimeMs": 0.0235}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Vertex)", "stripTimeMs": 0.0261}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Fragment)", "stripTimeMs": 0.022000000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-CubemapToEquirect", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0263}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0251}]}]}, {"inputVariants": 5, "outputVariants": 5, "name": "Hidden/VR/BlitFromTex2DToTexArraySlice", "pipelines": [{"inputVariants": 5, "outputVariants": 5, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0257}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.020900000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.024}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.023200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Geometry)", "stripTimeMs": 0.0234}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/VideoComposite", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0245}]}]}, {"inputVariants": 30, "outputVariants": 30, "name": "Hidden/VideoDecode", "pipelines": [{"inputVariants": 30, "outputVariants": 30, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCr_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.038}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCr_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.037200000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCrA_To_RGBAFull (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0292}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCrA_To_RGBAFull (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.028300000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCrA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0366}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCrA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.037000000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0543}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0574}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_RGBASplit_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.053700000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_RGBASplit_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0368}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_NV12_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0354}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_NV12_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0316}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_NV12_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0385}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_NV12_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0368}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Flip_P010_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.02}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Flip_P010_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.028200000000000003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Compositing", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Mix_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0217}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Mix_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.019100000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TextCore/Distance Field SSD", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.020200000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0251}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Outline", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Custom Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0253}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Custom Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0284}]}]}]}