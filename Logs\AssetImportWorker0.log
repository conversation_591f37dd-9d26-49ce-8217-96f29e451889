[Licensing::Module] Trying to connect to existing licensing client channel...
Built from '6000.2/respin/6000.2.0f1-517f89d850d1' branch; Version is '6000.2.0f1 (eed1c594c913) revision 15651269'; Using compiler version '194234433'; Build Type 'Release'
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-ilias" at "2025-08-15T19:51:23.3193997Z"
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 65462 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-08-15T19:51:23Z

COMMAND LINE ARGUMENTS:
C:\Unity\Editors\6000.2.0f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
C:/Unity/BLAME/BLAME
-logFile
Logs/AssetImportWorker0.log
-srvPort
55728
-licensingIpc
LicenseClient-ilias
-job-worker-count
5
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Unity/BLAME/BLAME
C:/Unity/BLAME/BLAME
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [288776]  Target information:

Player connection [288776]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2788620808 [EditorId] 2788620808 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [288776]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2788620808 [EditorId] 2788620808 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [288776]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2788620808 [EditorId] 2788620808 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [288776] Host joined multi-casting on [***********:54997]...
Player connection [288776] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 5
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 18644, path: "C:/Program Files/Unity Hub/UnityLicensingClient_V1/Unity.Licensing.Client.exe")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Error: HandshakeResponse reported an error:
	ResponseCode: 505
	ResponseStatus: Unsupported protocol version '1.17.1'.
[Licensing::Module] Error: Failed to handshake to channel: "LicenseClient-ilias"
[Licensing::IpcConnector] LicenseClient-ilias channel disconnected successfully.
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-ilias-6000.2.0" at "2025-08-15T19:51:23.432867Z"
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 290512, path: "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/Licensing/Client/Unity.Licensing.Client.exe")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.17.1+ae67fbc
  Session Id:              62128434d6ef4f0d8ebc269b12f6d70b
  Correlation Id:          5f91f71ccf6c9acad3d19ab3e1610c0c
  External correlation Id: 2018274968829726391
  Machine Id:              /GaE4A2aXaDfTh/RbExLPR8H29M=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-ilias-6000.2.0" (connect: 0.00s, validation: 0.01s, handshake: 0.02s)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-ilias-6000.2.0-notifications" at "2025-08-15T19:51:23.4597989Z"
[Licensing::Module] Licensing Background thread has ended after 0.14s
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Active build profile path: `Assets/Settings/Build Profiles/New Windows Profile.asset`
Got setting from build profile YAML, name `m_BuildTarget`, value `19`
Got setting from build profile YAML, name `m_Subtarget`, value `2`
Refreshing native plugins compatible for Editor in 1480.30 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.2.0f1 (eed1c594c913)
[Subsystems] Discovering subsystems at path C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Unity/BLAME/BLAME/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3060 Ti (ID=0x2489)
    Vendor:   NVIDIA
    VRAM:     8024 MB
    Driver:   32.0.15.8088
Initialize mono
Mono path[0] = 'C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed'
Mono path[1] = 'C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56772
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.021180 seconds.
- Loaded All Assemblies, in  3.325 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.978 seconds
Domain Reload Profiling: 5264ms
	BeginReloadAssembly (1580ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (207ms)
	RebuildNativeTypeToScriptingClass (63ms)
	initialDomainReloadingComplete (502ms)
	LoadAllAssembliesAndSetupDomain (924ms)
		LoadAssemblies (1334ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (903ms)
			TypeCache.Refresh (901ms)
				TypeCache.ScanAssembly (862ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1988ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1857ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (238ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (543ms)
			ProcessInitializeOnLoadAttributes (727ms)
			ProcessInitializeOnLoadMethodAttributes (341ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Got setting from build profile YAML, name `m_Development`, value `0`
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Launched and connected shader compiler UnityShaderCompiler.exe after 0.26 seconds
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 10.075 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 48.46 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-Persistent Object
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 42.981 seconds
Domain Reload Profiling: 52946ms
	BeginReloadAssembly (3588ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (1861ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (142ms)
	RebuildCommonClasses (155ms)
	RebuildNativeTypeToScriptingClass (28ms)
	initialDomainReloadingComplete (631ms)
	LoadAllAssembliesAndSetupDomain (5562ms)
		LoadAssemblies (5138ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1080ms)
			TypeCache.Refresh (915ms)
				TypeCache.ScanAssembly (879ms)
			BuildScriptInfoCaches (134ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (42982ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (42159ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (15ms)
			BeforeProcessingInitializeOnLoad (683ms)
			ProcessInitializeOnLoadAttributes (39147ms)
			ProcessInitializeOnLoadMethodAttributes (2224ms)
			AfterProcessingInitializeOnLoad (88ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (71ms)
Refreshing native plugins compatible for Editor in 37.61 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7983 unused Assets / (4.2 MB). Loaded Objects now: 9030.
Memory consumption went from 296.2 MB to 292.0 MB.
Total: 115.346800 ms (FindLiveObjects: 1.373500 ms CreateObjectMapping: 1.147400 ms MarkObjects: 107.398800 ms  DeleteObjects: 5.425900 ms)

========================================================================
Received Import Request.
  Time since last request: 507204.405418 seconds.
  path: Assets/Buttonroom.fbx
  artifactKey: Guid(1343a24e4a302564ea1c95eebe44e160) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Buttonroom.fbx using Guid(1343a24e4a302564ea1c95eebe44e160) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Launched and connected shader compiler UnityShaderCompiler.exe after 0.20 seconds
Launched and connected shader compiler UnityShaderCompiler.exe after 0.22 seconds
Launched and connected shader compiler UnityShaderCompiler.exe after 0.06 seconds
Launched and connected shader compiler UnityShaderCompiler.exe after 5.22 seconds
Launched and connected shader compiler UnityShaderCompiler.exe after 14.05 seconds
 -> (artifact id: '11bd558619e095b3a5b7e6db4afbd82b') in 251.93684619999999 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 50

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Leak Detected : Persistent allocates 8 individual allocations. To find out more please enable 'Preferences > Jobs > Leak Detection Level > Enabled With Stack Trace' and reproduce the leak again.
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.721 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 29.04 ms, found 35 plugins.
Native extension for WindowsStandalone target not found
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <6460d52f11fa4eaeabfca1cf2e9eca8f>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  6.496 seconds
Domain Reload Profiling: 9214ms
	BeginReloadAssembly (572ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (84ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (114ms)
	RebuildCommonClasses (72ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (74ms)
	LoadAllAssembliesAndSetupDomain (1980ms)
		LoadAssemblies (1394ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (777ms)
			TypeCache.Refresh (421ms)
				TypeCache.ScanAssembly (401ms)
			BuildScriptInfoCaches (318ms)
			ResolveRequiredComponents (27ms)
	FinalizeReload (6497ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (5801ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (33ms)
			BeforeProcessingInitializeOnLoad (3586ms)
			ProcessInitializeOnLoadAttributes (1473ms)
			ProcessInitializeOnLoadMethodAttributes (670ms)
			AfterProcessingInitializeOnLoad (34ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (83ms)
Refreshing native plugins compatible for Editor in 47.41 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 4.58 ms.
Unloading 146 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10655 unused Assets / (22.4 MB). Loaded Objects now: 11706.
Memory consumption went from 379.7 MB to 357.4 MB.
Total: 74.062700 ms (FindLiveObjects: 2.454700 ms CreateObjectMapping: 4.874500 ms MarkObjects: 49.862100 ms  DeleteObjects: 16.869800 ms)

Prepare: number of updated asset objects reloaded= 1
========================================================================
Received Import Request.
  Time since last request: 1161.563709 seconds.
  path: Assets/Settings/HDRPDefaultResources/DefaultSettingsVolumeProfile.asset
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Settings/HDRPDefaultResources/DefaultSettingsVolumeProfile.asset using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9929d03d7f90fc64eb7ec6be73be7fe1') in 0.0247566 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 15.854590 seconds.
  path: Assets/_Game/Scenes/Main/Black.mat
  artifactKey: Guid(71183bf04f91bd84ca2f6e3d82c5d4d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/Black.mat using Guid(71183bf04f91bd84ca2f6e3d82c5d4d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4b96424088913c7d030f1dbd9c4c2b3c') in 0.9914514 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/_Game/Scenes/Main/IndoorsSceneProfile.asset
  artifactKey: Guid(e21ba01aa32ab4d4299b42d6bb7ad35d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/IndoorsSceneProfile.asset using Guid(e21ba01aa32ab4d4299b42d6bb7ad35d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd39df6baf898d8aaf7a8ce2d2acbc8fe') in 0.0043348 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 24.140916 seconds.
  path: Assets/_Game/Scenes/Main/IndoorsSceneProfile.asset
  artifactKey: Guid(e21ba01aa32ab4d4299b42d6bb7ad35d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/IndoorsSceneProfile.asset using Guid(e21ba01aa32ab4d4299b42d6bb7ad35d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4c16936b39ec4ab94c7da5732d4b8a18') in 0.0010545 seconds
Number of updated asset objects reloaded before import = 45Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 926.737507 seconds.
  path: Assets/_Game/Models/catch.fbx
  artifactKey: Guid(3864a45c40c053e48baacd1b141ccd80) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/catch.fbx using Guid(3864a45c40c053e48baacd1b141ccd80) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6814a4b4937a22bf1a61ce79d750a249') in 0.1883017 seconds
Number of updated asset objects reloaded before import = 1Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/_Game/Models/towerl.fbx
  artifactKey: Guid(5cd625be56ee7e047b588b2261e976f7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/towerl.fbx using Guid(5cd625be56ee7e047b588b2261e976f7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd30f10e2a8ff0ff7b3cbae430be129ff') in 0.1191625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/_Game/Models/Crate.fbx
  artifactKey: Guid(3a1014d16b19c0a45bbd731953c30284) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Crate.fbx using Guid(3a1014d16b19c0a45bbd731953c30284) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '678a3dc514b3342f0b15a663581d57f6') in 0.1050973 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/_Game/Models/RagDoll.fbx
  artifactKey: Guid(860e4346e25d0a94a9b6a25d71c4f318) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/RagDoll.fbx using Guid(860e4346e25d0a94a9b6a25d71c4f318) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd76d19819570c3a8e48284bc596aa34e') in 0.0289966 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 2.289404 seconds.
  path: Assets/_Game/Shaders/JPEGCompressionShader/JPEGMP4Compression.compute
  artifactKey: Guid(795d453efbfb0e24bb4e990cd6c4aa37) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Shaders/JPEGCompressionShader/JPEGMP4Compression.compute using Guid(795d453efbfb0e24bb4e990cd6c4aa37) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '08ca063c38ba5e21f76bff4f9bfb5616') in 0.0028431 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Leak Detected : Persistent allocates 8 individual allocations. To find out more please enable 'Preferences > Jobs > Leak Detection Level > Enabled With Stack Trace' and reproduce the leak again.
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.319 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 24.40 ms, found 35 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <6460d52f11fa4eaeabfca1cf2e9eca8f>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.548 seconds
Domain Reload Profiling: 3830ms
	BeginReloadAssembly (662ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (112ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (123ms)
	RebuildCommonClasses (47ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (71ms)
	LoadAllAssembliesAndSetupDomain (1483ms)
		LoadAssemblies (1184ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (498ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (463ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1549ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1267ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (371ms)
			ProcessInitializeOnLoadAttributes (603ms)
			ProcessInitializeOnLoadMethodAttributes (256ms)
			AfterProcessingInitializeOnLoad (26ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (46ms)
Refreshing native plugins compatible for Editor in 26.33 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.19 ms.
Unloading 146 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10655 unused Assets / (23.6 MB). Loaded Objects now: 11822.
Memory consumption went from 381.0 MB to 357.5 MB.
Total: 23.335900 ms (FindLiveObjects: 1.288600 ms CreateObjectMapping: 1.264000 ms MarkObjects: 10.139100 ms  DeleteObjects: 10.642500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.534 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 27.09 ms, found 35 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <6460d52f11fa4eaeabfca1cf2e9eca8f>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.681 seconds
Domain Reload Profiling: 6178ms
	BeginReloadAssembly (867ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (43ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (188ms)
	RebuildCommonClasses (59ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (92ms)
	LoadAllAssembliesAndSetupDomain (3458ms)
		LoadAssemblies (2906ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (961ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (916ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1681ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1332ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (528ms)
			ProcessInitializeOnLoadAttributes (522ms)
			ProcessInitializeOnLoadMethodAttributes (249ms)
			AfterProcessingInitializeOnLoad (23ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (48ms)
Refreshing native plugins compatible for Editor in 25.47 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.21 ms.
Unloading 146 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10652 unused Assets / (24.5 MB). Loaded Objects now: 11823.
Memory consumption went from 380.9 MB to 356.4 MB.
Total: 22.868800 ms (FindLiveObjects: 1.682300 ms CreateObjectMapping: 2.571900 ms MarkObjects: 8.975700 ms  DeleteObjects: 9.637000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 808.304272 seconds.
  path: Assets/_Game/Models/Area-1/Catenary.fbx
  artifactKey: Guid(6f7fcd0989223da4c8b395fcc48f57d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Catenary.fbx using Guid(6f7fcd0989223da4c8b395fcc48f57d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '14bb0165cf498f8bdbf4c6b2231c9e8d') in 1.584969 seconds
Number of updated asset objects reloaded before import = 1Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/_Game/Models/Area-1/Cube_005.fbx
  artifactKey: Guid(3296d88f71260e747ae6c5bfa867caea) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_005.fbx using Guid(3296d88f71260e747ae6c5bfa867caea) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ac2a2370e09deeb920adb712c82c32fd') in 0.0287279 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/_Game/Models/Area-1/Cube_015.fbx
  artifactKey: Guid(36ad3872d8d474545ae31ff89494253b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_015.fbx using Guid(36ad3872d8d474545ae31ff89494253b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '18abdafb3bcc377de5965ed5a163150c') in 0.026773 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/_Game/Models/Area-1/Cube_007.fbx
  artifactKey: Guid(820e2128f59ff224fa32c34fda8fcdff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_007.fbx using Guid(820e2128f59ff224fa32c34fda8fcdff) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '90592f938d056c28d8c214c71b8de3e9') in 0.0351173 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/_Game/Models/Area-1/Cube_010.fbx
  artifactKey: Guid(98f1683a319ccc841ae4fd30f981eedd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_010.fbx using Guid(98f1683a319ccc841ae4fd30f981eedd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c9984b4457db551f1be0760eb798b900') in 0.0306212 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000093 seconds.
  path: Assets/_Game/Models/Area-1/Cylinder_001.fbx
  artifactKey: Guid(24f5eed8f1f226b4383680528bdf86d1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cylinder_001.fbx using Guid(24f5eed8f1f226b4383680528bdf86d1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8abf561d5c35f79a59edc1392a73e4aa') in 0.0375399 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/_Game/Models/Area-1/Debrey2.fbx
  artifactKey: Guid(373d13c7bf4e5034abaf31e7c5419de4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Debrey2.fbx using Guid(373d13c7bf4e5034abaf31e7c5419de4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '99222ee2b28aa4ebcd13de291f793ea5') in 0.0326689 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/_Game/Models/Area-1/Cube_011.fbx
  artifactKey: Guid(aa2e9303712cfa94897cf164d1ac6ee4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_011.fbx using Guid(aa2e9303712cfa94897cf164d1ac6ee4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0f4d7d6c130d44d672844affc0025169') in 0.0345833 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/_Game/Models/Area-1/Catenary_001.fbx
  artifactKey: Guid(d58d1f88f0aae4243b1c6f36326687a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Catenary_001.fbx using Guid(d58d1f88f0aae4243b1c6f36326687a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '951350a40a64fa948492e2484816d9fe') in 0.0292507 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/_Game/Models/Area-1/ElevatorBase.fbx
  artifactKey: Guid(1526d3f257cc59649bcbcac15120f899) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/ElevatorBase.fbx using Guid(1526d3f257cc59649bcbcac15120f899) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1581f0285064dfbf1519f914ab2c6f07') in 0.0278578 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/_Game/Models/Area-1/Cube_002.fbx
  artifactKey: Guid(68a6402db6aff4c46a2849e484ac291c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_002.fbx using Guid(68a6402db6aff4c46a2849e484ac291c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '73fb47f170a135f8b03baee329025f9e') in 0.0272135 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/_Game/Models/Area-1/Cube_003.fbx
  artifactKey: Guid(c67f21040a45a98469a6e5b740133b45) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_003.fbx using Guid(c67f21040a45a98469a6e5b740133b45) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3104d4585944faeda954e0634313a089') in 0.0311704 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/_Game/Models/Area-1/Cube_018.fbx
  artifactKey: Guid(d522ee34b7e1c934a9c636b5fc32be2f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_018.fbx using Guid(d522ee34b7e1c934a9c636b5fc32be2f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7492c3d20f68f801f1760ad7c36b8139') in 0.0664788 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/_Game/Models/Area-1/Cube_0171.fbx
  artifactKey: Guid(92e835e92c3380142a48f412230b643a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_0171.fbx using Guid(92e835e92c3380142a48f412230b643a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '03cd4e86f3f664365154559668ce250f') in 0.032938 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/_Game/Models/Area-1/FirstGroundDepth.fbx
  artifactKey: Guid(efaf0c85b5c0fa241b33b76f9bf4002f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/FirstGroundDepth.fbx using Guid(efaf0c85b5c0fa241b33b76f9bf4002f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '57e0f432438773ccc66aad13ae2fec0c') in 0.0299551 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/_Game/Models/Area-1/Plane.fbx
  artifactKey: Guid(2417ffa783c950b46a649d116ccc8a12) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Plane.fbx using Guid(2417ffa783c950b46a649d116ccc8a12) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0d14eb48f5f97d85bf167d6a27715946') in 0.0296035 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/_Game/Models/Area-1/OLDTUBE.fbx
  artifactKey: Guid(087a5638ebdfdfb47a8a930c4262840a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/OLDTUBE.fbx using Guid(087a5638ebdfdfb47a8a930c4262840a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0603ccbd75ce9c39bfc325955e526fec') in 0.0496183 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/_Game/Models/Area-1/Blockout_0Stairs.fbx
  artifactKey: Guid(3d47fcca79145a74397b0fadfcc06fb4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Blockout_0Stairs.fbx using Guid(3d47fcca79145a74397b0fadfcc06fb4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1cdbfeba50ff3a18d9270d87050e288b') in 0.0309726 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/_Game/Models/Area-1/mess2.fbx
  artifactKey: Guid(ca78f13d108ff7c4c93df8a80ac5a2f3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/mess2.fbx using Guid(ca78f13d108ff7c4c93df8a80ac5a2f3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '211067be2cdaa67482dd840728fe83c4') in 0.7134536 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 201

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/_Game/Models/Area-1/RagDoll.fbx
  artifactKey: Guid(ec48fde52c2bac543bcb8a030c8920c3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/RagDoll.fbx using Guid(ec48fde52c2bac543bcb8a030c8920c3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '846bd2128e65d048da3e41c58704943b') in 0.0386548 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/_Game/Models/Area-1/ACube_005.fbx
  artifactKey: Guid(acc6127c6afa48d45b07cead6e5c8b20) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/ACube_005.fbx using Guid(acc6127c6afa48d45b07cead6e5c8b20) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e4bcb0be6317c0b8a564e8cd7d169e26') in 0.0357419 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/_Game/Models/Area-1/A_Cube_017.fbx
  artifactKey: Guid(6cf0a16942287ac4c819adfa7fd21e08) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/A_Cube_017.fbx using Guid(6cf0a16942287ac4c819adfa7fd21e08) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '23c47dae5025da6c5dc040d99eb35608') in 0.0297938 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/_Game/Models/Area-1/FirstGroundWall.fbx
  artifactKey: Guid(82b2a29f678ed6c4694b210d2bcecc95) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/FirstGroundWall.fbx using Guid(82b2a29f678ed6c4694b210d2bcecc95) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2df250d8ac43bc28e86ab0204c36d3e2') in 0.041001 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/_Game/Models/Area-1/Awall.fbx
  artifactKey: Guid(3a6aef8fd45fa4e4d8d081844f547922) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Awall.fbx using Guid(3a6aef8fd45fa4e4d8d081844f547922) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '006f22ba730eea0341b3b5ce1c1a4248') in 0.031917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000834 seconds.
  path: Assets/_Game/Models/Area-1/cable_001.fbx
  artifactKey: Guid(d40d18e5f84cb8b44924b05eae77c7ba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/cable_001.fbx using Guid(d40d18e5f84cb8b44924b05eae77c7ba) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5bf4628d67b1062bd6945effc62c4c1e') in 1.9396793 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/_Game/Models/Area-1/Point_007.fbx
  artifactKey: Guid(b50e22f633b7455458fc79ff0bb135c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Point_007.fbx using Guid(b50e22f633b7455458fc79ff0bb135c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8acde380d405c21f6bf9fe381f3f123b') in 0.0027498 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/_Game/Models/Area-1/A_Cube_005.fbx
  artifactKey: Guid(85b2375b9cc7aa64981269aecea75c79) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/A_Cube_005.fbx using Guid(85b2375b9cc7aa64981269aecea75c79) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4e28704832076305be8b8ac0c864671b') in 0.0302231 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0