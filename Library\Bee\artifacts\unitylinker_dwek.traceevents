{ "pid": 295124, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "UnityLinker" } },
{ "pid": 295124, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 295124, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 295124, "tid": 1, "ts": 1755294149660754, "dur": 413803, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 295124, "tid": 1, "ts": 1755294149662846, "dur": 48445, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 295124, "tid": 1, "ts": 1755294149669246, "dur": 41409, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 295124, "tid": 1, "ts": 1755294149731996, "dur": 14170, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 295124, "tid": 1, "ts": 1755294149747631, "dur": 70126, "ph": "X", "name": "CopyModeStep", "args": {} },
{ "pid": 295124, "tid": 1, "ts": 1755294149817790, "dur": 18319, "ph": "X", "name": "LoadReferencesStep", "args": {} },
{ "pid": 295124, "tid": 1, "ts": 1755294149836121, "dur": 230525, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 295124, "tid": 1, "ts": 1755294150072123, "dur": 2280, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 295124, "tid": 1, "ts": 1755294150074559, "dur": 437, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 295124, "tid": 1, "ts": 1755294150085625, "dur": 2026, "ph": "X", "name": "", "args": {} },
{ "pid": 295124, "tid": 1, "ts": 1755294150084866, "dur": 3142, "ph": "X", "name": "Write chrome-trace events", "args": {} },
