{ "pid": 270960, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "UnityLinker" } },
{ "pid": 270960, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 270960, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 270960, "tid": 1, "ts": 1755293858465176, "dur": 471260, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 270960, "tid": 1, "ts": 1755293858467396, "dur": 59207, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 270960, "tid": 1, "ts": 1755293858475034, "dur": 50850, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 270960, "tid": 1, "ts": 1755293858554303, "dur": 16626, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 270960, "tid": 1, "ts": 1755293858571987, "dur": 79777, "ph": "X", "name": "CopyModeStep", "args": {} },
{ "pid": 270960, "tid": 1, "ts": 1755293858651804, "dur": 20631, "ph": "X", "name": "LoadReferencesStep", "args": {} },
{ "pid": 270960, "tid": 1, "ts": 1755293858672450, "dur": 255932, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 270960, "tid": 1, "ts": 1755293858933987, "dur": 2290, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 270960, "tid": 1, "ts": 1755293858936438, "dur": 400, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 270960, "tid": 1, "ts": 1755293858946769, "dur": 2604, "ph": "X", "name": "", "args": {} },
{ "pid": 270960, "tid": 1, "ts": 1755293858945921, "dur": 3836, "ph": "X", "name": "Write chrome-trace events", "args": {} },
