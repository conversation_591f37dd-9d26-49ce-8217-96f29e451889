using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;
using System.Collections;
#if UNITY_EDITOR
using UnityEditor;
#endif

/// <summary>
/// Minimal boot loader that shows a lightweight loading UI and loads the main scene asynchronously.
/// Usage:
/// - Create an empty scene named "<PERSON>ot" (or any name) with a GameObject that has this component
/// - Put that scene at index 0 in Build Settings
/// - Set the sceneToLoad to your main scene name (e.g., "Main")
/// </summary>
public class BootLoader : MonoBehaviour
{
	[Header("Scene")]
	[SerializeField] private string sceneToLoad = "";

#if UNITY_EDITOR
	// Assign a SceneAsset in editor; we copy its name into sceneToLoad via OnValidate
	[SerializeField] private SceneAsset sceneAsset;
#endif

	[SerializeField] private bool autoActivateOnReady = true;

	[Header("UI Text")] 
	[SerializeField] private string loadingText = "Initializing Persistence Engine";

	[Header("Animation")]
	[SerializeField] private float dotsInterval = 0.35f;
	[SerializeField] private float pulseSpeed = 1.5f;
	[SerializeField] private float pulseIntensity = 0.2f;


	[Header("UX")]
	[SerializeField] private float fadeOutDuration = 1.0f;
	[SerializeField] private float fadeInDuration = 1.0f;
	[SerializeField] private float minDisplayTime = 2.0f;

	[Header("Performance")]
	[SerializeField] private bool capFpsDuringBoot = true;
	[SerializeField] private int bootTargetFps = 60;

	// Runtime UI
	private Canvas _canvas;
	private CanvasScaler _scaler;
	private GraphicRaycaster _raycaster;
	private CanvasGroup _group;
	private Image _backdrop;
	private Text _loadingText;
	private int _prevVSync;
	private int _prevTargetFps;

	private void Awake()
	{
		// Temporarily cap FPS to avoid excessive GPU/CPU load during boot
		_prevVSync = QualitySettings.vSyncCount;
		_prevTargetFps = Application.targetFrameRate;
		if (capFpsDuringBoot)
		{
			QualitySettings.vSyncCount = 0; // ensure targetFrameRate takes effect
			Application.targetFrameRate = Mathf.Max(30, bootTargetFps);
		}

		// Additional GPU stability measures
		// Reduce quality during boot to prevent driver stress
		QualitySettings.SetQualityLevel(0, false); // Use lowest quality during boot

		DontDestroyOnLoad(gameObject);
		CreateOverlayUI();
	}

	private void Start()
	{
		StartCoroutine(LoadSceneAsyncWithOverlay());
	}

#if UNITY_EDITOR
	private void OnValidate()
	{
		if (sceneAsset != null)
		{
			string path = AssetDatabase.GetAssetPath(sceneAsset);
			if (!string.IsNullOrEmpty(path))
			{
				string name = System.IO.Path.GetFileNameWithoutExtension(path);
				if (!string.IsNullOrEmpty(name))
				{
					sceneToLoad = name;
				}
			}
		}
	}
#endif

	private void CreateOverlayUI()
	{
		// Canvas
		var canvasGO = new GameObject("LoadingCanvas");
		canvasGO.transform.SetParent(transform);
		_canvas = canvasGO.AddComponent<Canvas>();
		_canvas.renderMode = RenderMode.ScreenSpaceOverlay;
		_canvas.sortingOrder = 10000; // ensure always on top
		_scaler = canvasGO.AddComponent<CanvasScaler>();
		_scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
		_scaler.referenceResolution = new Vector2(1920, 1080);
		_raycaster = canvasGO.AddComponent<GraphicRaycaster>();
		_group = canvasGO.AddComponent<CanvasGroup>();
		_group.alpha = 1f;

		// Backdrop
		var bgGO = new GameObject("Backdrop", typeof(RectTransform));
		bgGO.transform.SetParent(canvasGO.transform, false);
		_backdrop = bgGO.AddComponent<Image>();
		_backdrop.color = new Color(0f, 0f, 0f, 1f);
		var bgRt = bgGO.GetComponent<RectTransform>();
		bgRt.anchorMin = Vector2.zero;
		bgRt.anchorMax = Vector2.one;
		bgRt.offsetMin = Vector2.zero;
		bgRt.offsetMax = Vector2.zero;

		// Loading text
		_loadingText = CreateText(canvasGO.transform, loadingText, 24, new Vector2(0.5f, 0.5f));

		// Style to match DeathManager look (font + subtle outline)
		Font serif = Resources.Load<Font>("InstrumentSerif-Regular");
		if (serif != null)
		{
			_loadingText.font = serif;
		}
		var textOutline = _loadingText.gameObject.AddComponent<UnityEngine.UI.Outline>();
		textOutline.effectColor = new Color(0.1f, 0.1f, 0.1f, 0.8f);
		textOutline.effectDistance = new Vector2(2, -2);
	}

	private Text CreateText(Transform parent, string text, int size, Vector2 anchor)
	{
		// Ensure RectTransform exists to avoid invalid casts on some Unity versions
		var go = new GameObject("Text", typeof(RectTransform));
		go.transform.SetParent(parent, false);
		var t = go.AddComponent<Text>();
		t.text = text;
		t.fontSize = size;
		t.color = Color.white;
		t.alignment = TextAnchor.MiddleCenter;
		t.horizontalOverflow = HorizontalWrapMode.Overflow;
		t.verticalOverflow = VerticalWrapMode.Overflow;
		// Use new built-in runtime font; Arial.ttf is deprecated in newer Unity versions
		t.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
		var rt = go.GetComponent<RectTransform>();
		rt.anchorMin = anchor;
		rt.anchorMax = anchor;
		rt.pivot = new Vector2(0.5f, 0.5f);
		rt.sizeDelta = new Vector2(600, 60);
		return t;
	}



	private IEnumerator LoadSceneAsyncWithOverlay()
	{
		// Start with fade-in
		_group.alpha = 0f;
		yield return StartCoroutine(FadeIn());

		// Validate target scene
		string target = sceneToLoad;
		if (string.IsNullOrEmpty(target) || !Application.CanStreamedLevelBeLoaded(target))
		{
			_loadingText.text = "Error";
			yield return new WaitForSeconds(0.5f);
			_loadingText.text = string.IsNullOrEmpty(target)
				? "No scene specified. Assign a scene in BootLoader."
				: $"Scene not found/in Build Settings: '{target}'";
			yield return StartCoroutine(WaitForAnyKeyThenQuit());
			yield break;
		}

		float shownStart = Time.unscaledTime;
		AsyncOperation op = SceneManager.LoadSceneAsync(target, LoadSceneMode.Single);
		op.allowSceneActivation = false;

		// Wait for scene to load (no progress bar needed)
		while (op != null && op.progress < 0.9f)
		{
			yield return null;
		}

		if (autoActivateOnReady)
		{
			// Ensure minimum display time
			while (Time.unscaledTime - shownStart < minDisplayTime)
			{
				yield return null;
			}
			
			// Fade out before activating scene
			yield return StartCoroutine(FadeOut());
			
			// Activate scene
			op.allowSceneActivation = true;
			while (!op.isDone)
			{
				yield return null;
			}
			
			// Clean up
			Destroy(gameObject);
		}
		else
		{
			_loadingText.text = "Press any key to continue";
			// Wait for user input
			while (!Input.anyKeyDown)
			{
				yield return null;
			}
			
			// Fade out before activating scene
			yield return StartCoroutine(FadeOut());
			
			op.allowSceneActivation = true;
			while (!op.isDone) { yield return null; }
			
			// Clean up
			Destroy(gameObject);
		}
	}

	private IEnumerator FadeIn()
	{
		float elapsed = 0f;
		while (elapsed < fadeInDuration)
		{
			elapsed += Time.unscaledDeltaTime;
			float t = elapsed / fadeInDuration;
			_group.alpha = Mathf.Lerp(0f, 1f, t);
			yield return null;
		}
		_group.alpha = 1f;
	}

	private IEnumerator FadeOut()
	{
		float elapsed = 0f;
		float startAlpha = _group.alpha;
		while (elapsed < fadeOutDuration)
		{
			elapsed += Time.unscaledDeltaTime;
			float t = elapsed / fadeOutDuration;
			_group.alpha = Mathf.Lerp(startAlpha, 0f, t);
			yield return null;
		}
		_group.alpha = 0f;
	}

	private void Update()
	{
		if (_loadingText != null)
		{
			// Animate loading dots
			float time = Time.unscaledTime;
			int dotCount = Mathf.FloorToInt(time * 2f) % 4;
			string dots = new string('.', dotCount);
			_loadingText.text = "Initializing Persistence Engine" + dots;
		}
	}

	private IEnumerator WaitForAnyKeyThenQuit()
	{
		_loadingText.text += "\nPress any key to quit";
		while (!Input.anyKeyDown)
		{
			yield return null;
		}
#if UNITY_EDITOR
		EditorApplication.isPlaying = false;
#else
		Application.Quit();
#endif
	}

	private void OnDestroy()
	{
		// Restore FPS settings
		if (capFpsDuringBoot)
		{
			QualitySettings.vSyncCount = _prevVSync;
			Application.targetFrameRate = _prevTargetFps;
		}

		// Restore quality settings - let SettingsManager handle this
		// Quality will be properly set by SettingsManager when the main scene loads
	}
}


