using UnityEngine;
using UnityEngine.SceneManagement;
using System.IO;
using System;
using KinematicCharacterController.FPS;

[System.Serializable]
public class GameSaveData
{
    public float playerX;
    public float playerY;
    public float playerZ;
    public float cameraRotationX;
    public float cameraRotationY;
    public string lastScene = "Main";
    public int saveFormatVersion = 1;
}

public class GameFlowManager : MonoBehaviour
{
    public static GameFlowManager Instance { get; private set; }
        public static bool IsInMainMenu { get; private set; } = false;
    public GameSaveData CurrentSaveData = null;
    public string SaveFilePath => Path.Combine(Application.persistentDataPath, "gamesave.json");
    
    // Event that other systems can subscribe to for save synchronization
    public event Action OnGameSaved;

    public enum GameState
    {
        MainMenu,
        Gameplay
    }

    private GameState _currentState = GameState.MainMenu;

    // Cached references
    private FPSCharacterController _playerController;
    private FPSCharacterCamera _fpsCamera;
    private Transform _playerCameraFollowPoint;
    private StartMenuManager _startMenuManager;
    private PauseMenuManager _pauseMenuManager;
    private AudioLowPassFilter _lowPassFilter;

    // Player camera reference for audio control
    private Camera _playerUnityCamera;
    private Coroutine _menuCamEnforcer;
    // Generation token to safely cancel/ignore stale enforcer coroutines
    private int _menuCamEnforcerGeneration = 0;

    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            LoadSaveData();
            Debug.Log($"GameFlowManager initialized. Save path: {SaveFilePath}");

            // Register for cleanup on quit
            Application.quitting += CleanupOnQuit;

            // Start periodic memory cleanup
            InvokeRepeating(nameof(PeriodicMemoryCleanup), 60f, 60f); // Every 60 seconds
        }
        else
        {
            Destroy(gameObject);
        }
    }

    private void Start()
    {
        // Immediately mark as in main menu to block all player inputs before first frame
        IsInMainMenu = true;
        RefreshCachedReferences();
        if (_fpsCamera != null)
        {
            _fpsCamera.SetMenuMode(true);
        }
        if (_playerUnityCamera == null && _fpsCamera != null)
        {
            _playerUnityCamera = _fpsCamera.GetComponent<Camera>();
        }
        if (_playerController == null)
        {
            _playerController = FindFirstObjectByType<FPSCharacterController>();
        }
        if (_playerController != null)
        {
            _playerController.enabled = false;
        }

        // Default to showing the main menu; perform full camera/platform freeze next frame
        StartCoroutine(EnterMainMenuNextFrame());
    }

    private System.Collections.IEnumerator EnterMainMenuNextFrame()
    {
        yield return null;
        EnterMainMenu();
    }

    public bool HasSaveData()
    {
        return CurrentSaveData != null && !string.IsNullOrEmpty(CurrentSaveData.lastScene);
    }

    public void SaveGame(Vector3 playerPosition, Vector2 cameraRotation, string sceneName)
    {
        if (CurrentSaveData == null)
        {
            CurrentSaveData = new GameSaveData();
        }

        CurrentSaveData.playerX = playerPosition.x;
        CurrentSaveData.playerY = playerPosition.y;
        CurrentSaveData.playerZ = playerPosition.z;
        CurrentSaveData.cameraRotationX = cameraRotation.x;
        CurrentSaveData.cameraRotationY = cameraRotation.y;
        CurrentSaveData.lastScene = sceneName;

        try
        {
            string saveJson = JsonUtility.ToJson(CurrentSaveData, true);
            File.WriteAllText(SaveFilePath, saveJson);
            
            // Notify listeners that a save has occurred
            OnGameSaved?.Invoke();
            
            Debug.Log($"[GameFlowManager] Game saved: {playerPosition}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to save game: {e.Message}");
        }
    }

    private void LoadSaveData()
    {
        try
        {
            if (File.Exists(SaveFilePath))
            {
                string saveJson = File.ReadAllText(SaveFilePath);
                CurrentSaveData = JsonUtility.FromJson<GameSaveData>(saveJson);
                Debug.Log($"Successfully loaded save data: {saveJson}");
            }
            else
            {
                Debug.Log("No save file exists yet");
                CurrentSaveData = null;
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to load save data: {e.Message}");
            CurrentSaveData = null;
        }
    }

    public void LoadGame()
    {
        // Single-scene flow: just enter gameplay
        LoadSaveData();
        EnterGameplay();
    }

    public void QuitGame()
    {
#if UNITY_EDITOR
        UnityEditor.EditorApplication.isPlaying = false;
#else
        Application.Quit();
#endif
    }

    private void RefreshCachedReferences()
    {
        if (_playerController == null)
        {
            _playerController = FindFirstObjectByType<FPSCharacterController>();
        }
        if (_fpsCamera == null)
        {
            _fpsCamera = FindFirstObjectByType<FPSCharacterCamera>();
        }
        if (_playerCameraFollowPoint == null && _playerController != null)
        {
            _playerCameraFollowPoint = _playerController.CameraFollowPoint;
        }
        if (_startMenuManager == null)
        {
            _startMenuManager = FindFirstObjectByType<StartMenuManager>();
            if (_startMenuManager == null)
            {
                _startMenuManager = FindAnyObjectByType<StartMenuManager>();
            }
        }
        if (_pauseMenuManager == null)
        {
            _pauseMenuManager = FindFirstObjectByType<PauseMenuManager>();
            if (_pauseMenuManager == null)
            {
                _pauseMenuManager = FindAnyObjectByType<PauseMenuManager>();
            }
        }
        if (_lowPassFilter == null)
        {
            _lowPassFilter = FindFirstObjectByType<AudioLowPassFilter>();
        }
    }

    public void EnterMainMenu()
    {
        RefreshCachedReferences();

        Debug.Log("[GameFlowManager] Entering main menu...");

        // Set state immediately to prevent any resumes
        IsInMainMenu = true;

        // Choose a menu camera anchor (prefer nearest to last known/current player position)
        Transform anchor = null;
        if (MenuCameraManager.Instance != null)
        {
            anchor = MenuCameraManager.Instance.SelectAnchorNearest();
        }

        bool usingMenuCamera = false;
        if (MenuCameraManager.Instance != null)
        {
            var anchorComp = anchor != null ? anchor.GetComponent<MenuCameraAnchor>() : null;
            if (anchorComp == null && anchor != null)
            {
                anchorComp = anchor.GetComponentInParent<MenuCameraAnchor>();
            }
            if (anchorComp != null)
            {
                Debug.Log($"[GameFlowManager] Activating menu camera for anchor: {anchorComp.AnchorId}");
                MenuCameraManager.Instance.ActivateMenuCameraForAnchor(anchorComp);
                usingMenuCamera = true;
            }
            else
            {
                Debug.LogWarning("[GameFlowManager] No menu camera anchor found! Using fallback menu camera.");
                MenuCameraManager.Instance.ActivateFallbackCamera();
                usingMenuCamera = true;
            }
        }
        else
        {
            Debug.LogWarning("[GameFlowManager] MenuCameraManager.Instance is null! Creating fallback.");
            // Try to create a fallback camera instance
            var mgr = new GameObject("MenuCameraManager").AddComponent<MenuCameraManager>();
            mgr.ActivateFallbackCamera();
            usingMenuCamera = true;
        }

        // Configure player camera after menu camera is ready
        if (_fpsCamera != null)
        {
            Debug.Log("[GameFlowManager] Setting up camera for menu mode");
            _fpsCamera.SetPauseState(true);
            _fpsCamera.SetMenuMode(true);

            if (usingMenuCamera)
            {
                // Robustly find the player's camera
                Camera playerCamera = null;
                if (_fpsCamera.Camera != null)
                {
                    playerCamera = _fpsCamera.Camera;
                }
                else
                {
                    playerCamera = _fpsCamera.GetComponent<Camera>();
                    if (playerCamera == null)
                    {
                        playerCamera = _fpsCamera.GetComponentInChildren<Camera>(true);
                    }
                }
                _playerUnityCamera = playerCamera != null ? playerCamera : _playerUnityCamera;

                var playerListener = (playerCamera != null) ? playerCamera.GetComponent<AudioListener>() : null;
                if (playerListener == null)
                {
                    playerListener = _fpsCamera.GetComponent<AudioListener>();
                }

                if (playerCamera != null && playerCamera.enabled)
                {
                    // Disable camera component to avoid rendering/input conflicts (menu UI uses its own camera)
                    playerCamera.enabled = false;
                    Debug.Log("[GameFlowManager] Player camera disabled for menu");
                }
                if (playerListener != null && playerListener.enabled)
                {
                    playerListener.enabled = false;
                    Debug.Log("[GameFlowManager] Player audio listener disabled");
                }

                // Enforce for a few frames in case another script re-enables it
                if (_menuCamEnforcer != null)
                {
                    Debug.Log("[GameFlowManager] Stopping existing menu camera enforcer before starting a new one");
                    StopCoroutine(_menuCamEnforcer);
                    _menuCamEnforcer = null;
                }
                // Bump generation so any stale enforcement exits immediately
                _menuCamEnforcerGeneration++;
                int gen = _menuCamEnforcerGeneration;
                Debug.Log($"[GameFlowManager] Starting menu camera enforcer gen={gen} for 6 frames");
                _menuCamEnforcer = StartCoroutine(EnforceMenuCameraDominance(6, gen));
            }
        }

        // Stop platforms before pausing time (they may already be paused from pause menu)
        Debug.Log("[GameFlowManager] Pausing platforms and time");
        if (PlatformPauseManager.Instance != null)
        {
            PlatformPauseManager.Instance.PauseAllPlatforms();
        }
        Time.timeScale = 0f;

        // Disable player controller input (guard against null)
        if (_playerController == null)
        {
            _playerController = FindFirstObjectByType<FPSCharacterController>();
        }
        if (_playerController != null)
        {
            _playerController.enabled = false;
            // Note: Don't zero out BaseVelocity here as PersistenceManager needs to preserve it
            // for proper velocity restoration when returning from title screen
        }

        // Disable pause ESC and ensure its UI is hidden
        if (_pauseMenuManager != null)
        {
            _pauseMenuManager.SetEscListening(false);
            _pauseMenuManager.HideMenuUIOnly();
        }

        // Apply audio low-pass for menu ambience
        if (_lowPassFilter != null)
        {
            _lowPassFilter.enabled = true;
        }

        // Show Start Menu UI if available
        if (_startMenuManager != null)
        {
            _startMenuManager.gameObject.SetActive(true);
        }

        // Ensure tool selection UI is hidden while in main menu
        var toolUI = FindFirstObjectByType<ToolSelectionManager>();
        if (toolUI != null)
        {
            toolUI.ForceHideUI();
        }

        _currentState = GameState.MainMenu;

        // Reduce GPU load while in menu
        QualitySettings.vSyncCount = 1;
        Application.targetFrameRate = 60;
    }

    public void EnterGameplay()
    {
        RefreshCachedReferences();

        // Note: Deferred persistence initialization is now handled by StartMenuManager
        // before calling this method to prevent stutters during transition
        Debug.Log("[GameFlowManager] Entering gameplay (initialization should already be complete)");

        // Stop any menu camera enforcement
        // Invalidate any running enforcer coroutines by bumping generation
        _menuCamEnforcerGeneration++;
        int cancelGen = _menuCamEnforcerGeneration;
        if (_menuCamEnforcer != null)
        {
            Debug.Log($"[GameFlowManager] Stopping menu camera enforcer (cancelGen={cancelGen})");
            StopCoroutine(_menuCamEnforcer);
            _menuCamEnforcer = null;
        }

        // Hide Start Menu UI if available
        if (_startMenuManager != null)
        {
            _startMenuManager.gameObject.SetActive(false);
        }

        // Disable menu camera and re-enable player camera
        if (MenuCameraManager.Instance != null)
        {
            MenuCameraManager.Instance.DeactivateMenuCamera();
        }
        if (_fpsCamera != null)
        {
            // Re-enable player camera components; restore render settings
            if (_playerUnityCamera == null)
            {
                _playerUnityCamera = _fpsCamera.GetComponent<Camera>();
            }
            var playerCamera = _playerUnityCamera;
            var playerListener = _fpsCamera.GetComponent<AudioListener>();
            if (playerCamera != null)
            {
                playerCamera.enabled = true;
            }
            if (playerListener != null && !playerListener.enabled)
            {
                playerListener.enabled = true;
            }
            Debug.Log($"[GameFlowManager] Re-enabled player camera ({(playerCamera != null ? playerCamera.name : "null")}) and audio listener on gameplay entry");

            // Exit menu mode and restore normal camera behavior
            _fpsCamera.SetMenuMode(false);
            _fpsCamera.SetPauseState(false);

            // Verify next frame in case anything else toggles them late in the frame
            StartCoroutine(VerifyPlayerCameraReenabledNextFrame());
        }

        // Resume time and platforms
        Time.timeScale = 1f;
        PlatformPauseManager.Instance.ResumeAllPlatforms();

        // Re-enable player controller input
        if (_playerController != null)
        {
            _playerController.enabled = true;
        }

        // Re-enable pause ESC
        if (_pauseMenuManager != null)
        {
            _pauseMenuManager.SetEscListening(true);
        }

        // Disable low-pass filter
        if (_lowPassFilter != null)
        {
            _lowPassFilter.enabled = false;
        }

        // Reapply user video settings (FPS/vSync) when returning to game
        if (SettingsVideoManager.Instance != null)
        {
            SettingsVideoManager.Instance.ApplySettings();
        }

        _currentState = GameState.Gameplay;
        IsInMainMenu = false;
    }

    private System.Collections.IEnumerator EnforceMenuCameraDominance(int frames, int generation)
    {
        Debug.Log($"[GameFlowManager] Enforcer started (gen={generation}, frames={frames})");
        for (int i = 0; i < frames; i++)
        {
            // Early exit if we've left menu or this enforcer is stale
            if (!IsInMainMenu || generation != _menuCamEnforcerGeneration)
            {
                Debug.Log($"[GameFlowManager] Enforcer exiting early (gen={generation}, IsInMainMenu={IsInMainMenu}, currentGen={_menuCamEnforcerGeneration})");
                yield break;
            }
            // Re-fetch in case references changed
            if (_fpsCamera == null)
            {
                _fpsCamera = FindFirstObjectByType<FPSCharacterCamera>();
            }
            Camera playerCamera = null;
            if (_fpsCamera != null)
            {
                playerCamera = _fpsCamera.Camera != null ? _fpsCamera.Camera : _fpsCamera.GetComponent<Camera>();
                if (playerCamera == null)
                {
                    playerCamera = _fpsCamera.GetComponentInChildren<Camera>(true);
                }
            }
            if (playerCamera != null && playerCamera.enabled)
            {
                playerCamera.enabled = false;
                Debug.Log($"[GameFlowManager] Enforcer (gen={generation}) frame {i+1}/{frames}: disabled player camera");
            }
            var listener = (playerCamera != null) ? playerCamera.GetComponent<AudioListener>() : null;
            if (listener == null && _fpsCamera != null)
            {
                listener = _fpsCamera.GetComponent<AudioListener>();
            }
            if (listener != null && listener.enabled)
            {
                listener.enabled = false;
            }
            yield return null;
        }
        if (generation == _menuCamEnforcerGeneration)
        {
            _menuCamEnforcer = null;
        }
        Debug.Log($"[GameFlowManager] Enforcer completed (gen={generation})");
    }

    private System.Collections.IEnumerator VerifyPlayerCameraReenabledNextFrame()
    {
        // Ensure we run after any late-frame toggles
        yield return new WaitForEndOfFrame();
        if (_fpsCamera == null)
        {
            _fpsCamera = FindFirstObjectByType<FPSCharacterCamera>();
        }
        if (_fpsCamera != null)
        {
            if (_playerUnityCamera == null)
            {
                _playerUnityCamera = _fpsCamera.GetComponent<Camera>();
            }
            var cam = _playerUnityCamera;
            var listener = _fpsCamera.GetComponent<AudioListener>();
            if (cam != null && !cam.enabled)
            {
                Debug.Log("[GameFlowManager] Verify: player camera was disabled late; re-enabling");
                cam.enabled = true;
            }
            if (listener != null && !listener.enabled)
            {
                Debug.Log("[GameFlowManager] Verify: audio listener was disabled late; re-enabling");
                listener.enabled = true;
            }
        }
    }

    private void PeriodicMemoryCleanup()
    {
        // Only run cleanup during gameplay, not in menus
        if (!IsInMainMenu)
        {
            // Gentle cleanup during gameplay
            System.GC.Collect();
        }
    }

    private void CleanupOnQuit()
    {
        Debug.Log("[GameFlowManager] Performing memory cleanup on quit...");

        // Force aggressive garbage collection
        System.GC.Collect();
        System.GC.WaitForPendingFinalizers();
        System.GC.Collect();

        // Unload unused assets to free graphics memory
        Resources.UnloadUnusedAssets();

        Debug.Log("[GameFlowManager] Memory cleanup complete");
    }
}