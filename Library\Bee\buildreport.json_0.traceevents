{ "pid": 284300, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 284300, "tid": 1, "ts": 1755294408883526, "dur": 5616, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 284300, "tid": 1, "ts": 1755294408889146, "dur": 131839, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 284300, "tid": 1, "ts": 1755294409020994, "dur": 3585, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 284300, "tid": 79310, "ts": 1755294410661006, "dur": 1602, "ph": "X", "name": "", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294408881862, "dur": 27290, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294408909154, "dur": 1739423, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294408910243, "dur": 2447, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294408912695, "dur": 1238, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294408913936, "dur": 215, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294408914155, "dur": 1113, "ph": "X", "name": "ProcessMessages 785", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294408915270, "dur": 1517, "ph": "X", "name": "ReadAsync 785", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294408916793, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294408916833, "dur": 509, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294408917345, "dur": 1708303, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294410625658, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294410625662, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294410625707, "dur": 2209, "ph": "X", "name": "ProcessMessages 9192", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294410627921, "dur": 5706, "ph": "X", "name": "ReadAsync 9192", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294410633633, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294410633636, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294410633671, "dur": 294, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294410633970, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294410634002, "dur": 296, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294410634301, "dur": 13470, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 284300, "tid": 79310, "ts": 1755294410662613, "dur": 29, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 284300, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 284300, "tid": 8589934592, "ts": 1755294408879808, "dur": 144808, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 284300, "tid": 8589934592, "ts": 1755294409024619, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 284300, "tid": 8589934592, "ts": 1755294409024623, "dur": 871, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 284300, "tid": 79310, "ts": 1755294410662644, "dur": 3, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 284300, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 284300, "tid": 4294967296, "ts": 1755294408860935, "dur": 1788701, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 284300, "tid": 4294967296, "ts": 1755294408865791, "dur": 9604, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 284300, "tid": 4294967296, "ts": 1755294410649692, "dur": 4912, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 284300, "tid": 4294967296, "ts": 1755294410652540, "dur": 35, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 284300, "tid": 4294967296, "ts": 1755294410654710, "dur": 20, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 284300, "tid": 79310, "ts": 1755294410662649, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1755294408907725, "dur":5276, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755294408913009, "dur":160, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755294408913201, "dur":334, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755294408913556, "dur":67, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755294408913624, "dur":1720781, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755294410634406, "dur":208, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755294410634728, "dur":4144, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1755294408913860, "dur":160, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294408914585, "dur":1079, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294408914024, "dur":2821, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":1, "ts":1755294408918897, "dur":1708715, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":2, "ts":1755294408913877, "dur":2956, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294408916833, "dur":1717586, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294408913873, "dur":2311, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294408916184, "dur":1718217, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294408913966, "dur":2652, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294408916619, "dur":1717792, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755294408914027, "dur":1913, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755294408915940, "dur":1718480, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294408914067, "dur":112200, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294409038139, "dur":323, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.0f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":6, "ts":1755294409026268, "dur":12199, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294409038467, "dur":1595954, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294408914105, "dur":124366, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294409038472, "dur":1595952, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294408914140, "dur":1720260, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294408914179, "dur":1720223, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294408914219, "dur":1720184, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294408914260, "dur":1720148, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294408914294, "dur":1720112, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755294410647507, "dur":498, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 284300, "tid": 79310, "ts": 1755294410663069, "dur": 1710, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 284300, "tid": 79310, "ts": 1755294410664905, "dur": 1238, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 284300, "tid": 79310, "ts": 1755294410660137, "dur": 6730, "ph": "X", "name": "Write chrome-trace events", "args": {} },
