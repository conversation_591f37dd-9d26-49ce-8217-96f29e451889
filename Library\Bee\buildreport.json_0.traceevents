{ "pid": 284300, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 284300, "tid": 1, "ts": 1755293846019879, "dur": 9225, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 284300, "tid": 1, "ts": 1755293846029109, "dur": 91965, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 284300, "tid": 1, "ts": 1755293846121087, "dur": 3705, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 284300, "tid": 79182, "ts": 1755293848134324, "dur": 1193, "ph": "X", "name": "", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 284300, "tid": 12884901888, "ts": 1755293846017388, "dur": 56680, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755293846074072, "dur": 2047170, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755293846075618, "dur": 3248, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755293846078872, "dur": 1673, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755293846080550, "dur": 700, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755293846081255, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755293846081322, "dur": 593, "ph": "X", "name": "ProcessMessages 209", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755293846081917, "dur": 168, "ph": "X", "name": "ReadAsync 209", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755293846082089, "dur": 414, "ph": "X", "name": "ProcessMessages 548", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755293846082507, "dur": 27, "ph": "X", "name": "ReadAsync 548", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755293846082537, "dur": 6050, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755293846088594, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755293846088598, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755293846088642, "dur": 774, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755293846089420, "dur": 2007588, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755293848097016, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755293848097019, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755293848097073, "dur": 2129, "ph": "X", "name": "ProcessMessages 9192", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755293848099205, "dur": 6892, "ph": "X", "name": "ReadAsync 9192", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755293848106104, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755293848106107, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755293848106169, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755293848106172, "dur": 357, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755293848106533, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755293848106557, "dur": 282, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755293848106842, "dur": 13749, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 284300, "tid": 79182, "ts": 1755293848135521, "dur": 28, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 284300, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 284300, "tid": 8589934592, "ts": 1755293846013201, "dur": 111635, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 284300, "tid": 8589934592, "ts": 1755293846124838, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 284300, "tid": 8589934592, "ts": 1755293846124843, "dur": 1289, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 284300, "tid": 79182, "ts": 1755293848135551, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 284300, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 284300, "tid": 4294967296, "ts": 1755293845987354, "dur": 2135010, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 284300, "tid": 4294967296, "ts": 1755293845993217, "dur": 13855, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 284300, "tid": 4294967296, "ts": 1755293848122422, "dur": 5015, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 284300, "tid": 4294967296, "ts": 1755293848125234, "dur": 39, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 284300, "tid": 4294967296, "ts": 1755293848127550, "dur": 19, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 284300, "tid": 79182, "ts": 1755293848135556, "dur": 76, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1755293846072341, "dur":8217, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755293846080569, "dur":240, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755293846080848, "dur":737, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755293846081622, "dur":161, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755293846081784, "dur":2024745, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755293848106530, "dur":229, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755293848106934, "dur":4133, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1755293846082734, "dur":62001, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755293846144735, "dur":1961778, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755293846084844, "dur":1103, "ph":"X", "name": "File",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1755293846082235, "dur":4823, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755293846087059, "dur":2019471, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755293846084797, "dur":518, "ph":"X", "name": "File",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":3, "ts":1755293846085315, "dur":657, "ph":"X", "name": "File",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.InternalAPIEngineBridge.001.dll" }}
,{ "pid":12345, "tid":3, "ts":1755293846082600, "dur":4112, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755293846086713, "dur":2019804, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755293846082131, "dur":2655, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755293846084787, "dur":2021745, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755293846082132, "dur":158, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755293846082963, "dur":1186, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":5, "ts":1755293846082298, "dur":4907, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":5, "ts":1755293846090166, "dur":2008345, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":6, "ts":1755293846082632, "dur":2013, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755293846084645, "dur":2021883, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755293846082695, "dur":43847, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755293846144217, "dur":509, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.0f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":7, "ts":1755293846126543, "dur":18188, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755293846144732, "dur":1961803, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755293846082754, "dur":2023757, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755293846082850, "dur":2023662, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755293846082924, "dur":2023583, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755293846083046, "dur":2023463, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755293846083081, "dur":2023427, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755293848119930, "dur":542, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 284300, "tid": 79182, "ts": 1755293848135992, "dur": 2375, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 284300, "tid": 79182, "ts": 1755293848138529, "dur": 1145, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 284300, "tid": 79182, "ts": 1755293848133564, "dur": 7016, "ph": "X", "name": "Write chrome-trace events", "args": {} },
