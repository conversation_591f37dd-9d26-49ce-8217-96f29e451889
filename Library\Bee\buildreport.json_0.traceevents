{ "pid": 284300, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 284300, "tid": 1, "ts": 1755294132287094, "dur": 7445, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 284300, "tid": 1, "ts": 1755294132294543, "dur": 145827, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 284300, "tid": 1, "ts": 1755294132440380, "dur": 3662, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 284300, "tid": 79260, "ts": 1755294134006868, "dur": 1299, "ph": "X", "name": "", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294132284957, "dur": 33575, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294132318535, "dur": 1675446, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294132319662, "dur": 3321, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294132322991, "dur": 1582, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294132324577, "dur": 309, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294132324888, "dur": 604, "ph": "X", "name": "ProcessMessages 753", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294132325496, "dur": 35, "ph": "X", "name": "ReadAsync 753", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294132325533, "dur": 307, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294132325843, "dur": 3507, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294132329355, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294132329357, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294132329384, "dur": 536, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294132329923, "dur": 1640642, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294133970574, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294133970579, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294133970634, "dur": 1687, "ph": "X", "name": "ProcessMessages 9192", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294133972323, "dur": 7295, "ph": "X", "name": "ReadAsync 9192", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294133979623, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294133979626, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294133979667, "dur": 311, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294133979983, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294133980012, "dur": 222, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755294133980236, "dur": 13058, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 284300, "tid": 79260, "ts": 1755294134008172, "dur": 38, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 284300, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 284300, "tid": 8589934592, "ts": 1755294132281440, "dur": 162635, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 284300, "tid": 8589934592, "ts": 1755294132444077, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 284300, "tid": 8589934592, "ts": 1755294132444080, "dur": 873, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 284300, "tid": 79260, "ts": 1755294134008212, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 284300, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 284300, "tid": 4294967296, "ts": 1755294132258874, "dur": 1736178, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 284300, "tid": 4294967296, "ts": 1755294132263402, "dur": 12364, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 284300, "tid": 4294967296, "ts": 1755294133995105, "dur": 4839, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 284300, "tid": 4294967296, "ts": 1755294133998022, "dur": 31, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 284300, "tid": 4294967296, "ts": 1755294134000015, "dur": 14, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 284300, "tid": 79260, "ts": 1755294134008221, "dur": 96, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1755294132316425, "dur":6856, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755294132323292, "dur":207, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755294132323530, "dur":338, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755294132323887, "dur":69, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755294132323956, "dur":1655342, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755294133979299, "dur":194, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755294133979634, "dur":4237, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1755294132324302, "dur":190, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294132325014, "dur":1306, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294132324496, "dur":3757, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":1, "ts":1755294132330223, "dur":1641199, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":2, "ts":1755294132324310, "dur":3482, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294132327792, "dur":1651517, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294132326110, "dur":577, "ph":"X", "name": "File",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":3, "ts":1755294132324313, "dur":3826, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294132328139, "dur":1651180, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294132326112, "dur":653, "ph":"X", "name": "File",  "args": { "detail":"Library\\ScriptAssemblies\\BakeryRuntimeAssembly.dll" }}
,{ "pid":12345, "tid":4, "ts":1755294132324346, "dur":2673, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294132327019, "dur":1652296, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755294132326093, "dur":584, "ph":"X", "name": "File",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.PerformanceTesting.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294132324334, "dur":3553, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755294132327888, "dur":1651417, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294132324439, "dur":120180, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294132456011, "dur":329, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.0f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":6, "ts":1755294132444621, "dur":11724, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294132456345, "dur":1522969, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294132324585, "dur":131764, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294132456349, "dur":1522958, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294132324614, "dur":1654682, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294132324667, "dur":1654627, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294132324706, "dur":1654587, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294132324745, "dur":1654556, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294132324771, "dur":1654532, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755294133991713, "dur":568, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 284300, "tid": 79260, "ts": 1755294134008784, "dur": 2212, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 284300, "tid": 79260, "ts": 1755294134011151, "dur": 1044, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 284300, "tid": 79260, "ts": 1755294134005876, "dur": 7070, "ph": "X", "name": "Write chrome-trace events", "args": {} },
