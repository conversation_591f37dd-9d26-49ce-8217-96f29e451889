{"PlayerBuildProgramLibrary.FeatureExtractor+Data": {"ignoreSystemDlls": false, "featuresToCheck": [{"referenceName": "UnityEngine.Rendering.MachineLearningContext"}]}, "Bee.TundraBackend.CSharpActionInvocationInformation": {"typeFullName": "PlayerBuildProgramLibrary.FeatureExtractor", "methodName": "Run", "assemblyLocation": "C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll", "targets": ["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.AppUI.Undo-FeaturesChecked.txt"], "inputs": ["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.AppUI.Undo.dll"], "targetDirectories": []}}