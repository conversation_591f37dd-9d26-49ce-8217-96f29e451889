Base path: 'C:/Unity/Editors/6000.2.0f1/Editor/Data', plugins path 'C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines'
Cmd: initializeCompiler

Cmd: compileComputeKernel
  insize=4813 file=Packages/com.unity.render-pipelines.high-definition/Runtime/RenderPipeline/RenderPass/DepthPyramid.compute kernel=KDepthDownsample8DualUav ppOnly=0 stripLineD=0 buildPlatform=19 km=<KERNEL_NAME=KDepthDownsample8DualUav>,<UNITY_VERSION=60020000> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=ENABLE_CHECKERBOARD SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=4529

Cmd: compileComputeKernel
  insize=12140 file=Packages/com.unity.render-pipelines.high-definition/Runtime/Lighting/LightLoop/Deferred.compute kernel=Deferred_Indirect_Fptl_Variant3 ppOnly=0 stripLineD=0 buildPlatform=19 km=<SHADE_OPAQUE_ENTRY=Deferred_Indirect_Fptl_Variant3>,<USE_INDIRECT=1>,<VARIANT=3>,<UNITY_VERSION=60020000> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SCREEN_SPACE_SHADOWS_OFF PROBE_VOLUMES_L1 PUNCTUAL_SHADOW_HIGH DIRECTIONAL_SHADOW_HIGH AREA_SHADOW_MEDIUM SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=63023

Cmd: compileComputeKernel
  insize=12140 file=Packages/com.unity.render-pipelines.high-definition/Runtime/Lighting/LightLoop/Deferred.compute kernel=Deferred_Indirect_Fptl_Variant7 ppOnly=0 stripLineD=0 buildPlatform=19 km=<SHADE_OPAQUE_ENTRY=Deferred_Indirect_Fptl_Variant7>,<USE_INDIRECT=1>,<VARIANT=7>,<UNITY_VERSION=60020000> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SCREEN_SPACE_SHADOWS_OFF PROBE_VOLUMES_L1 PUNCTUAL_SHADOW_HIGH DIRECTIONAL_SHADOW_HIGH AREA_SHADOW_MEDIUM SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=109840

Cmd: compileComputeKernel
  insize=12140 file=Packages/com.unity.render-pipelines.high-definition/Runtime/Lighting/LightLoop/Deferred.compute kernel=Deferred_Indirect_Fptl_Variant18 ppOnly=0 stripLineD=0 buildPlatform=19 km=<SHADE_OPAQUE_ENTRY=Deferred_Indirect_Fptl_Variant18>,<USE_INDIRECT=1>,<VARIANT=18>,<UNITY_VERSION=60020000> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SCREEN_SPACE_SHADOWS_OFF PROBE_VOLUMES_L1 PUNCTUAL_SHADOW_HIGH DIRECTIONAL_SHADOW_HIGH AREA_SHADOW_MEDIUM SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=67348

Cmd: compileComputeKernel
  insize=12140 file=Packages/com.unity.render-pipelines.high-definition/Runtime/Lighting/LightLoop/Deferred.compute kernel=Deferred_Indirect_Fptl_Variant20 ppOnly=0 stripLineD=0 buildPlatform=19 km=<SHADE_OPAQUE_ENTRY=Deferred_Indirect_Fptl_Variant20>,<USE_INDIRECT=1>,<VARIANT=20>,<UNITY_VERSION=60020000> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SCREEN_SPACE_SHADOWS_OFF PROBE_VOLUMES_L1 PUNCTUAL_SHADOW_HIGH DIRECTIONAL_SHADOW_HIGH AREA_SHADOW_MEDIUM SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=76161

Cmd: compileComputeKernel
  insize=12140 file=Packages/com.unity.render-pipelines.high-definition/Runtime/Lighting/LightLoop/Deferred.compute kernel=Deferred_Indirect_Fptl_Variant23 ppOnly=0 stripLineD=0 buildPlatform=19 km=<SHADE_OPAQUE_ENTRY=Deferred_Indirect_Fptl_Variant23>,<USE_INDIRECT=1>,<VARIANT=23>,<UNITY_VERSION=60020000> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SCREEN_SPACE_SHADOWS_OFF PROBE_VOLUMES_L1 PUNCTUAL_SHADOW_HIGH DIRECTIONAL_SHADOW_HIGH AREA_SHADOW_MEDIUM SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=65792

Cmd: compileComputeKernel
  insize=12140 file=Packages/com.unity.render-pipelines.high-definition/Runtime/Lighting/LightLoop/Deferred.compute kernel=Deferred_Indirect_Fptl_Variant25 ppOnly=0 stripLineD=0 buildPlatform=19 km=<SHADE_OPAQUE_ENTRY=Deferred_Indirect_Fptl_Variant25>,<USE_INDIRECT=1>,<VARIANT=25>,<UNITY_VERSION=60020000> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SCREEN_SPACE_SHADOWS_OFF PROBE_VOLUMES_L1 PUNCTUAL_SHADOW_HIGH DIRECTIONAL_SHADOW_HIGH AREA_SHADOW_MEDIUM SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=72269

Cmd: compileComputeKernel
  insize=12140 file=Packages/com.unity.render-pipelines.high-definition/Runtime/Lighting/LightLoop/Deferred.compute kernel=Deferred_Indirect_Fptl_Variant27 ppOnly=0 stripLineD=0 buildPlatform=19 km=<SHADE_OPAQUE_ENTRY=Deferred_Indirect_Fptl_Variant27>,<USE_INDIRECT=1>,<VARIANT=27>,<UNITY_VERSION=60020000> pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SCREEN_SPACE_SHADOWS_OFF PROBE_VOLUMES_L1 PUNCTUAL_SHADOW_HIGH DIRECTIONAL_SHADOW_HIGH AREA_SHADOW_MEDIUM SHADER_STAGE_COMPUTE platform=d3d11 flags=0 reqs=50241 forceDXC=0 forceFXC=0 ok=1 outsize=112633


Cmd: initializeCompiler

Unhandled exception: Protocol error - failed to read magic number. Error code 0x80000004 (Not connected). (transferred 0/4)

Quitting shader compiler process
