{"Bee.Core.BuildProgramContext+BuildProgramContextOutputData": {"MaxRerunAllowed": 2147483647}, "ScriptCompilationBuildProgram.Data.ScriptCompilationData_Out": {"Assemblies": [{"Path": "Library/Bee/artifacts/1900b0aP.dag/UnityEngine.UI.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/UnityEngine.UI.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/UnityEngine.UI.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Autodesk.Fbx.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Autodesk.Fbx.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Autodesk.Fbx.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/BakeryRuntimeAssembly.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/BakeryRuntimeAssembly.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/BakeryRuntimeAssembly.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Domain_Reload.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Domain_Reload.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Domain_Reload.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Tayx.Graphy.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Tayx.Graphy.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Tayx.Graphy.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.AppUI.InternalAPIBridge.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.AppUI.InternalAPIBridge.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.AppUI.InternalAPIBridge.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.AppUI.Undo.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.AppUI.Undo.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.AppUI.Undo.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.Burst.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.Burst.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.Burst.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.Formats.Fbx.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.Formats.Fbx.Runtime.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.Formats.Fbx.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.InternalAPIEngineBridge.001.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.InternalAPIEngineBridge.001.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.Mathematics.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.Mathematics.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.Mathematics.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.Multiplayer.Center.Common.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.Multiplayer.Center.Common.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.Csg.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.Csg.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.Csg.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.KdTree.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.KdTree.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.KdTree.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.Poly2Tri.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.Poly2Tri.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.Poly2Tri.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.Stl.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.Stl.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.Stl.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.ProGrids.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.ProGrids.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.ProGrids.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.Recorder.Base.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.Recorder.Base.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.Recorder.Base.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.TextMeshPro.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.TextMeshPro.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.TextMeshPro.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.Timeline.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.Timeline.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.Timeline.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.Core.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.Core.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.Core.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Autodesk.Fbx.BuildTestAssets.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Autodesk.Fbx.BuildTestAssets.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Autodesk.Fbx.BuildTestAssets.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.2D.Common.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.2D.Common.Runtime.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.2D.Common.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.AppUI.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.AppUI.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.AppUI.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.Collections.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.Collections.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.Collections.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.Recorder.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.Recorder.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.Recorder.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.VisualEffectGraph.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.VisualEffectGraph.Runtime.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.Flow.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.Flow.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.Flow.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.AppUI.MVVM.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.AppUI.MVVM.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.AppUI.MVVM.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.AppUI.Navigation.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.AppUI.Navigation.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.AppUI.Navigation.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.AppUI.Redux.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.AppUI.Redux.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.AppUI.Redux.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.InferenceEngine.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.InferenceEngine.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.InferenceEngine.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.Runtime.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.Rendering.LightTransport.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.Rendering.LightTransport.Runtime.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.Serialization.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.Serialization.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.Serialization.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.State.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.State.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.State.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.Samples.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.Samples.Runtime.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.Samples.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.HighDefinition.Config.Runtime.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.HighDefinition.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.HighDefinition.Runtime.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.HighDefinition.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aP.dag/Assembly-CSharp.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aP.dag/Assembly-CSharp.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/1900b0aP.dag/Assembly-CSharp.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEngine.TestRunner.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEngine.TestRunner.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEngine.TestRunner.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEngine.UI.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEngine.UI.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEngine.UI.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEditor.TestRunner.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEditor.TestRunner.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEditor.TestRunner.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEditor.UI.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEditor.UI.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEditor.UI.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Burst.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Burst.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Burst.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Mathematics.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Mathematics.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Mathematics.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Burst.CodeGen.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Burst.CodeGen.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Burst.CodeGen.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Collections.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Collections.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Collections.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Collections.CodeGen.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Collections.CodeGen.rsp", "MovedFromExtractorFile": "C:/Unity/BLAME/BLAME/Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Collections.CodeGen.dll.mvfrm"}], "LocalizeCompilerMessages": false}}