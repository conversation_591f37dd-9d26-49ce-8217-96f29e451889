{ "pid": 284300, "tid": 34359738368, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424898965, "dur": 29490, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424928455, "dur": 334262, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424928467, "dur": 38, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424928510, "dur": 593, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424929109, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424929147, "dur": 8, "ph": "X", "name": "ProcessMessages 47", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424929157, "dur": 5429, "ph": "X", "name": "ReadAsync 47", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424934591, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424934642, "dur": 2, "ph": "X", "name": "ProcessMessages 762", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424934645, "dur": 59, "ph": "X", "name": "ReadAsync 762", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424934707, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424934710, "dur": 34, "ph": "X", "name": "ReadAsync 536", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424934745, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424934747, "dur": 26, "ph": "X", "name": "ReadAsync 577", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424934778, "dur": 23, "ph": "X", "name": "ReadAsync 479", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424934804, "dur": 26, "ph": "X", "name": "ReadAsync 507", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424934833, "dur": 25, "ph": "X", "name": "ReadAsync 414", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424934861, "dur": 22, "ph": "X", "name": "ReadAsync 407", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424934886, "dur": 21, "ph": "X", "name": "ReadAsync 365", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424934910, "dur": 19, "ph": "X", "name": "ReadAsync 227", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424934931, "dur": 62, "ph": "X", "name": "ReadAsync 240", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424934998, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935036, "dur": 1, "ph": "X", "name": "ProcessMessages 231", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935038, "dur": 29, "ph": "X", "name": "ReadAsync 231", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935070, "dur": 23, "ph": "X", "name": "ReadAsync 265", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935096, "dur": 21, "ph": "X", "name": "ReadAsync 451", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935121, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935123, "dur": 34, "ph": "X", "name": "ReadAsync 63", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935160, "dur": 25, "ph": "X", "name": "ReadAsync 465", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935187, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935189, "dur": 94, "ph": "X", "name": "ReadAsync 237", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935286, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935324, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935325, "dur": 65, "ph": "X", "name": "ReadAsync 492", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935392, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935394, "dur": 28, "ph": "X", "name": "ReadAsync 475", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935424, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935426, "dur": 26, "ph": "X", "name": "ReadAsync 518", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935455, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935457, "dur": 27, "ph": "X", "name": "ReadAsync 462", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935488, "dur": 28, "ph": "X", "name": "ReadAsync 246", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935518, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935521, "dur": 33, "ph": "X", "name": "ReadAsync 337", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935556, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935558, "dur": 25, "ph": "X", "name": "ReadAsync 504", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935585, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935588, "dur": 38, "ph": "X", "name": "ReadAsync 255", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935629, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935666, "dur": 25, "ph": "X", "name": "ReadAsync 401", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935694, "dur": 22, "ph": "X", "name": "ReadAsync 372", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935718, "dur": 22, "ph": "X", "name": "ReadAsync 233", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935742, "dur": 25, "ph": "X", "name": "ReadAsync 330", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935769, "dur": 21, "ph": "X", "name": "ReadAsync 400", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935793, "dur": 20, "ph": "X", "name": "ReadAsync 245", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935815, "dur": 24, "ph": "X", "name": "ReadAsync 299", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935841, "dur": 25, "ph": "X", "name": "ReadAsync 425", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935869, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935871, "dur": 34, "ph": "X", "name": "ReadAsync 421", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935907, "dur": 30, "ph": "X", "name": "ReadAsync 774", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935939, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935940, "dur": 26, "ph": "X", "name": "ReadAsync 253", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935969, "dur": 17, "ph": "X", "name": "ReadAsync 482", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424935988, "dur": 18, "ph": "X", "name": "ReadAsync 271", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936009, "dur": 43, "ph": "X", "name": "ReadAsync 182", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936054, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936056, "dur": 24, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936083, "dur": 19, "ph": "X", "name": "ReadAsync 449", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936104, "dur": 20, "ph": "X", "name": "ReadAsync 258", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936126, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936127, "dur": 25, "ph": "X", "name": "ReadAsync 314", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936156, "dur": 24, "ph": "X", "name": "ReadAsync 430", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936183, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936184, "dur": 25, "ph": "X", "name": "ReadAsync 314", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936213, "dur": 20, "ph": "X", "name": "ReadAsync 350", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936235, "dur": 27, "ph": "X", "name": "ReadAsync 438", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936264, "dur": 23, "ph": "X", "name": "ReadAsync 1", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936289, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936291, "dur": 19, "ph": "X", "name": "ReadAsync 344", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936312, "dur": 24, "ph": "X", "name": "ReadAsync 273", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936339, "dur": 21, "ph": "X", "name": "ReadAsync 414", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936362, "dur": 17, "ph": "X", "name": "ReadAsync 393", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936383, "dur": 20, "ph": "X", "name": "ReadAsync 315", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936405, "dur": 15, "ph": "X", "name": "ReadAsync 438", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936422, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936424, "dur": 25, "ph": "X", "name": "ReadAsync 57", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936451, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936470, "dur": 24, "ph": "X", "name": "ReadAsync 345", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936496, "dur": 17, "ph": "X", "name": "ReadAsync 339", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936516, "dur": 18, "ph": "X", "name": "ReadAsync 367", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936536, "dur": 19, "ph": "X", "name": "ReadAsync 540", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936557, "dur": 19, "ph": "X", "name": "ReadAsync 417", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936578, "dur": 16, "ph": "X", "name": "ReadAsync 559", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936596, "dur": 18, "ph": "X", "name": "ReadAsync 437", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936617, "dur": 20, "ph": "X", "name": "ReadAsync 346", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936638, "dur": 26, "ph": "X", "name": "ReadAsync 411", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936666, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936668, "dur": 23, "ph": "X", "name": "ReadAsync 516", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936693, "dur": 19, "ph": "X", "name": "ReadAsync 310", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936714, "dur": 25, "ph": "X", "name": "ReadAsync 369", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936741, "dur": 16, "ph": "X", "name": "ReadAsync 170", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936759, "dur": 15, "ph": "X", "name": "ReadAsync 310", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936776, "dur": 18, "ph": "X", "name": "ReadAsync 120", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936796, "dur": 17, "ph": "X", "name": "ReadAsync 276", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936815, "dur": 17, "ph": "X", "name": "ReadAsync 285", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936834, "dur": 15, "ph": "X", "name": "ReadAsync 283", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936851, "dur": 16, "ph": "X", "name": "ReadAsync 284", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936869, "dur": 17, "ph": "X", "name": "ReadAsync 262", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936889, "dur": 12, "ph": "X", "name": "ReadAsync 282", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936903, "dur": 18, "ph": "X", "name": "ReadAsync 281", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936923, "dur": 17, "ph": "X", "name": "ReadAsync 275", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936942, "dur": 30, "ph": "X", "name": "ReadAsync 271", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424936974, "dur": 26, "ph": "X", "name": "ReadAsync 437", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937004, "dur": 26, "ph": "X", "name": "ReadAsync 490", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937033, "dur": 22, "ph": "X", "name": "ReadAsync 366", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937059, "dur": 23, "ph": "X", "name": "ReadAsync 289", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937083, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937084, "dur": 20, "ph": "X", "name": "ReadAsync 566", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937107, "dur": 18, "ph": "X", "name": "ReadAsync 388", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937128, "dur": 17, "ph": "X", "name": "ReadAsync 135", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937148, "dur": 17, "ph": "X", "name": "ReadAsync 501", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937168, "dur": 22, "ph": "X", "name": "ReadAsync 269", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937193, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937195, "dur": 29, "ph": "X", "name": "ReadAsync 413", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937227, "dur": 18, "ph": "X", "name": "ReadAsync 494", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937248, "dur": 17, "ph": "X", "name": "ReadAsync 352", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937266, "dur": 17, "ph": "X", "name": "ReadAsync 281", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937286, "dur": 17, "ph": "X", "name": "ReadAsync 309", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937306, "dur": 16, "ph": "X", "name": "ReadAsync 389", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937324, "dur": 15, "ph": "X", "name": "ReadAsync 168", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937342, "dur": 15, "ph": "X", "name": "ReadAsync 329", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937358, "dur": 22, "ph": "X", "name": "ReadAsync 271", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937383, "dur": 17, "ph": "X", "name": "ReadAsync 361", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937404, "dur": 34, "ph": "X", "name": "ReadAsync 399", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937439, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937441, "dur": 16, "ph": "X", "name": "ReadAsync 356", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937459, "dur": 40, "ph": "X", "name": "ReadAsync 403", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937501, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937502, "dur": 21, "ph": "X", "name": "ReadAsync 160", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937527, "dur": 30, "ph": "X", "name": "ReadAsync 223", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937558, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937559, "dur": 21, "ph": "X", "name": "ReadAsync 599", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937583, "dur": 19, "ph": "X", "name": "ReadAsync 352", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937604, "dur": 17, "ph": "X", "name": "ReadAsync 573", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937623, "dur": 19, "ph": "X", "name": "ReadAsync 387", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937644, "dur": 16, "ph": "X", "name": "ReadAsync 450", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937663, "dur": 18, "ph": "X", "name": "ReadAsync 217", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937683, "dur": 54, "ph": "X", "name": "ReadAsync 454", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937740, "dur": 16, "ph": "X", "name": "ReadAsync 340", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937757, "dur": 24, "ph": "X", "name": "ReadAsync 282", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937784, "dur": 17, "ph": "X", "name": "ReadAsync 143", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937803, "dur": 31, "ph": "X", "name": "ReadAsync 378", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937837, "dur": 17, "ph": "X", "name": "ReadAsync 340", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937857, "dur": 15, "ph": "X", "name": "ReadAsync 371", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937874, "dur": 28, "ph": "X", "name": "ReadAsync 270", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937905, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937933, "dur": 20, "ph": "X", "name": "ReadAsync 492", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937956, "dur": 21, "ph": "X", "name": "ReadAsync 436", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937979, "dur": 16, "ph": "X", "name": "ReadAsync 259", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424937997, "dur": 17, "ph": "X", "name": "ReadAsync 368", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938016, "dur": 17, "ph": "X", "name": "ReadAsync 333", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938036, "dur": 17, "ph": "X", "name": "ReadAsync 534", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938056, "dur": 16, "ph": "X", "name": "ReadAsync 364", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938074, "dur": 16, "ph": "X", "name": "ReadAsync 417", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938092, "dur": 17, "ph": "X", "name": "ReadAsync 400", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938111, "dur": 22, "ph": "X", "name": "ReadAsync 407", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938135, "dur": 15, "ph": "X", "name": "ReadAsync 547", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938152, "dur": 17, "ph": "X", "name": "ReadAsync 305", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938171, "dur": 15, "ph": "X", "name": "ReadAsync 397", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938189, "dur": 16, "ph": "X", "name": "ReadAsync 280", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938207, "dur": 16, "ph": "X", "name": "ReadAsync 207", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938225, "dur": 17, "ph": "X", "name": "ReadAsync 329", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938244, "dur": 17, "ph": "X", "name": "ReadAsync 499", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938263, "dur": 21, "ph": "X", "name": "ReadAsync 335", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938286, "dur": 15, "ph": "X", "name": "ReadAsync 517", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938303, "dur": 18, "ph": "X", "name": "ReadAsync 284", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938324, "dur": 28, "ph": "X", "name": "ReadAsync 371", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938355, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938356, "dur": 20, "ph": "X", "name": "ReadAsync 401", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938378, "dur": 18, "ph": "X", "name": "ReadAsync 608", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938399, "dur": 14, "ph": "X", "name": "ReadAsync 564", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938415, "dur": 19, "ph": "X", "name": "ReadAsync 89", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938436, "dur": 15, "ph": "X", "name": "ReadAsync 252", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938453, "dur": 15, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938470, "dur": 16, "ph": "X", "name": "ReadAsync 309", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938488, "dur": 16, "ph": "X", "name": "ReadAsync 427", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938507, "dur": 16, "ph": "X", "name": "ReadAsync 316", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938525, "dur": 17, "ph": "X", "name": "ReadAsync 381", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938544, "dur": 15, "ph": "X", "name": "ReadAsync 325", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938563, "dur": 19, "ph": "X", "name": "ReadAsync 334", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938584, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938586, "dur": 28, "ph": "X", "name": "ReadAsync 329", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938617, "dur": 1, "ph": "X", "name": "ProcessMessages 659", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938619, "dur": 28, "ph": "X", "name": "ReadAsync 659", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938649, "dur": 22, "ph": "X", "name": "ReadAsync 634", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938676, "dur": 21, "ph": "X", "name": "ReadAsync 383", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938699, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938701, "dur": 20, "ph": "X", "name": "ReadAsync 603", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938723, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938725, "dur": 28, "ph": "X", "name": "ReadAsync 336", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938754, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938756, "dur": 23, "ph": "X", "name": "ReadAsync 657", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938782, "dur": 26, "ph": "X", "name": "ReadAsync 427", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938811, "dur": 27, "ph": "X", "name": "ReadAsync 385", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938841, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938844, "dur": 28, "ph": "X", "name": "ReadAsync 679", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938875, "dur": 20, "ph": "X", "name": "ReadAsync 673", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938897, "dur": 21, "ph": "X", "name": "ReadAsync 340", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938920, "dur": 18, "ph": "X", "name": "ReadAsync 663", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938941, "dur": 16, "ph": "X", "name": "ReadAsync 345", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938959, "dur": 17, "ph": "X", "name": "ReadAsync 373", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938979, "dur": 16, "ph": "X", "name": "ReadAsync 538", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424938997, "dur": 16, "ph": "X", "name": "ReadAsync 356", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939015, "dur": 18, "ph": "X", "name": "ReadAsync 308", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939035, "dur": 18, "ph": "X", "name": "ReadAsync 457", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939055, "dur": 16, "ph": "X", "name": "ReadAsync 393", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939073, "dur": 16, "ph": "X", "name": "ReadAsync 434", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939091, "dur": 17, "ph": "X", "name": "ReadAsync 392", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939110, "dur": 18, "ph": "X", "name": "ReadAsync 530", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939131, "dur": 16, "ph": "X", "name": "ReadAsync 422", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939149, "dur": 16, "ph": "X", "name": "ReadAsync 354", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939166, "dur": 16, "ph": "X", "name": "ReadAsync 534", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939185, "dur": 17, "ph": "X", "name": "ReadAsync 377", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939204, "dur": 17, "ph": "X", "name": "ReadAsync 405", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939223, "dur": 17, "ph": "X", "name": "ReadAsync 451", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939243, "dur": 18, "ph": "X", "name": "ReadAsync 470", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939262, "dur": 17, "ph": "X", "name": "ReadAsync 320", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939282, "dur": 20, "ph": "X", "name": "ReadAsync 439", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939303, "dur": 18, "ph": "X", "name": "ReadAsync 436", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939324, "dur": 22, "ph": "X", "name": "ReadAsync 351", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939348, "dur": 13, "ph": "X", "name": "ReadAsync 571", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939363, "dur": 15, "ph": "X", "name": "ReadAsync 92", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939380, "dur": 15, "ph": "X", "name": "ReadAsync 253", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939398, "dur": 16, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939417, "dur": 16, "ph": "X", "name": "ReadAsync 344", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939435, "dur": 16, "ph": "X", "name": "ReadAsync 500", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939454, "dur": 15, "ph": "X", "name": "ReadAsync 405", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939471, "dur": 15, "ph": "X", "name": "ReadAsync 124", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939488, "dur": 34, "ph": "X", "name": "ReadAsync 420", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939526, "dur": 23, "ph": "X", "name": "ReadAsync 449", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939551, "dur": 2, "ph": "X", "name": "ProcessMessages 378", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939554, "dur": 29, "ph": "X", "name": "ReadAsync 378", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939584, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939586, "dur": 26, "ph": "X", "name": "ReadAsync 549", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939614, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939616, "dur": 18, "ph": "X", "name": "ReadAsync 597", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939636, "dur": 19, "ph": "X", "name": "ReadAsync 85", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939658, "dur": 19, "ph": "X", "name": "ReadAsync 556", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939679, "dur": 16, "ph": "X", "name": "ReadAsync 394", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939697, "dur": 17, "ph": "X", "name": "ReadAsync 480", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939716, "dur": 17, "ph": "X", "name": "ReadAsync 469", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939735, "dur": 16, "ph": "X", "name": "ReadAsync 428", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939753, "dur": 29, "ph": "X", "name": "ReadAsync 345", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939785, "dur": 17, "ph": "X", "name": "ReadAsync 653", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939803, "dur": 17, "ph": "X", "name": "ReadAsync 607", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939824, "dur": 14, "ph": "X", "name": "ReadAsync 370", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939840, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939866, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939890, "dur": 18, "ph": "X", "name": "ReadAsync 629", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939911, "dur": 21, "ph": "X", "name": "ReadAsync 423", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939935, "dur": 16, "ph": "X", "name": "ReadAsync 608", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939953, "dur": 17, "ph": "X", "name": "ReadAsync 350", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939973, "dur": 17, "ph": "X", "name": "ReadAsync 544", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424939993, "dur": 23, "ph": "X", "name": "ReadAsync 397", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940019, "dur": 16, "ph": "X", "name": "ReadAsync 579", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940037, "dur": 15, "ph": "X", "name": "ReadAsync 360", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940054, "dur": 16, "ph": "X", "name": "ReadAsync 288", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940072, "dur": 14, "ph": "X", "name": "ReadAsync 379", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940089, "dur": 23, "ph": "X", "name": "ReadAsync 21", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940113, "dur": 16, "ph": "X", "name": "ReadAsync 571", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940131, "dur": 16, "ph": "X", "name": "ReadAsync 297", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940149, "dur": 16, "ph": "X", "name": "ReadAsync 373", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940168, "dur": 17, "ph": "X", "name": "ReadAsync 332", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940187, "dur": 19, "ph": "X", "name": "ReadAsync 512", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940208, "dur": 28, "ph": "X", "name": "ReadAsync 378", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940239, "dur": 23, "ph": "X", "name": "ReadAsync 468", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940265, "dur": 17, "ph": "X", "name": "ReadAsync 619", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940285, "dur": 16, "ph": "X", "name": "ReadAsync 546", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940304, "dur": 14, "ph": "X", "name": "ReadAsync 444", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940319, "dur": 19, "ph": "X", "name": "ReadAsync 310", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940341, "dur": 17, "ph": "X", "name": "ReadAsync 438", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940360, "dur": 16, "ph": "X", "name": "ReadAsync 636", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940379, "dur": 17, "ph": "X", "name": "ReadAsync 434", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940398, "dur": 23, "ph": "X", "name": "ReadAsync 476", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940423, "dur": 20, "ph": "X", "name": "ReadAsync 768", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940445, "dur": 15, "ph": "X", "name": "ReadAsync 566", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940462, "dur": 17, "ph": "X", "name": "ReadAsync 277", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940482, "dur": 15, "ph": "X", "name": "ReadAsync 549", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940499, "dur": 14, "ph": "X", "name": "ReadAsync 527", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940514, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940531, "dur": 15, "ph": "X", "name": "ReadAsync 327", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940548, "dur": 17, "ph": "X", "name": "ReadAsync 444", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940568, "dur": 28, "ph": "X", "name": "ReadAsync 442", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940598, "dur": 21, "ph": "X", "name": "ReadAsync 365", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940623, "dur": 18, "ph": "X", "name": "ReadAsync 703", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940643, "dur": 16, "ph": "X", "name": "ReadAsync 623", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940662, "dur": 16, "ph": "X", "name": "ReadAsync 435", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940680, "dur": 15, "ph": "X", "name": "ReadAsync 491", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940697, "dur": 14, "ph": "X", "name": "ReadAsync 440", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940712, "dur": 15, "ph": "X", "name": "ReadAsync 95", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940730, "dur": 16, "ph": "X", "name": "ReadAsync 469", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940748, "dur": 15, "ph": "X", "name": "ReadAsync 501", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940765, "dur": 17, "ph": "X", "name": "ReadAsync 395", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940784, "dur": 16, "ph": "X", "name": "ReadAsync 282", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940802, "dur": 15, "ph": "X", "name": "ReadAsync 379", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940819, "dur": 16, "ph": "X", "name": "ReadAsync 328", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940837, "dur": 18, "ph": "X", "name": "ReadAsync 483", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940857, "dur": 15, "ph": "X", "name": "ReadAsync 447", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940874, "dur": 15, "ph": "X", "name": "ReadAsync 423", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940892, "dur": 15, "ph": "X", "name": "ReadAsync 489", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940909, "dur": 13, "ph": "X", "name": "ReadAsync 416", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940924, "dur": 18, "ph": "X", "name": "ReadAsync 94", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940945, "dur": 16, "ph": "X", "name": "ReadAsync 591", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940963, "dur": 23, "ph": "X", "name": "ReadAsync 405", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940988, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424940989, "dur": 88, "ph": "X", "name": "ReadAsync 360", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941080, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941082, "dur": 33, "ph": "X", "name": "ReadAsync 567", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941116, "dur": 1, "ph": "X", "name": "ProcessMessages 2107", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941118, "dur": 22, "ph": "X", "name": "ReadAsync 2107", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941144, "dur": 17, "ph": "X", "name": "ReadAsync 367", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941163, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941181, "dur": 21, "ph": "X", "name": "ReadAsync 336", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941205, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941207, "dur": 29, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941239, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941241, "dur": 38, "ph": "X", "name": "ReadAsync 519", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941281, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941283, "dur": 35, "ph": "X", "name": "ReadAsync 435", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941321, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941323, "dur": 29, "ph": "X", "name": "ReadAsync 618", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941357, "dur": 65, "ph": "X", "name": "ReadAsync 5", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941425, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941427, "dur": 26, "ph": "X", "name": "ReadAsync 400", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941456, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941458, "dur": 28, "ph": "X", "name": "ReadAsync 348", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941488, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941491, "dur": 70, "ph": "X", "name": "ReadAsync 596", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941564, "dur": 1, "ph": "X", "name": "ProcessMessages 740", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941566, "dur": 26, "ph": "X", "name": "ReadAsync 740", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941595, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941597, "dur": 26, "ph": "X", "name": "ReadAsync 380", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941627, "dur": 29, "ph": "X", "name": "ReadAsync 423", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941659, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941661, "dur": 32, "ph": "X", "name": "ReadAsync 459", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941695, "dur": 1, "ph": "X", "name": "ProcessMessages 243", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941697, "dur": 53, "ph": "X", "name": "ReadAsync 243", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941752, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941754, "dur": 33, "ph": "X", "name": "ReadAsync 296", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941790, "dur": 1, "ph": "X", "name": "ProcessMessages 652", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941793, "dur": 36, "ph": "X", "name": "ReadAsync 652", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941831, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941834, "dur": 37, "ph": "X", "name": "ReadAsync 394", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941873, "dur": 1, "ph": "X", "name": "ProcessMessages 673", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941875, "dur": 43, "ph": "X", "name": "ReadAsync 673", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941920, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941943, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941945, "dur": 21, "ph": "X", "name": "ReadAsync 344", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941968, "dur": 18, "ph": "X", "name": "ReadAsync 369", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424941988, "dur": 18, "ph": "X", "name": "ReadAsync 324", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942009, "dur": 23, "ph": "X", "name": "ReadAsync 339", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942036, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942067, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942068, "dur": 27, "ph": "X", "name": "ReadAsync 461", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942099, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942101, "dur": 28, "ph": "X", "name": "ReadAsync 367", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942131, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942133, "dur": 23, "ph": "X", "name": "ReadAsync 541", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942158, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942160, "dur": 23, "ph": "X", "name": "ReadAsync 336", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942186, "dur": 26, "ph": "X", "name": "ReadAsync 327", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942214, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942216, "dur": 25, "ph": "X", "name": "ReadAsync 467", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942243, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942245, "dur": 28, "ph": "X", "name": "ReadAsync 551", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942276, "dur": 26, "ph": "X", "name": "ReadAsync 363", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942305, "dur": 19, "ph": "X", "name": "ReadAsync 595", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942326, "dur": 20, "ph": "X", "name": "ReadAsync 103", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942348, "dur": 14, "ph": "X", "name": "ReadAsync 293", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942364, "dur": 15, "ph": "X", "name": "ReadAsync 43", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942381, "dur": 24, "ph": "X", "name": "ReadAsync 254", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942407, "dur": 24, "ph": "X", "name": "ReadAsync 386", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942434, "dur": 1, "ph": "X", "name": "ProcessMessages 198", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942436, "dur": 29, "ph": "X", "name": "ReadAsync 198", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942467, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942470, "dur": 18, "ph": "X", "name": "ReadAsync 530", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942491, "dur": 41, "ph": "X", "name": "ReadAsync 263", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942534, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942536, "dur": 30, "ph": "X", "name": "ReadAsync 408", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942568, "dur": 1, "ph": "X", "name": "ProcessMessages 1023", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942569, "dur": 19, "ph": "X", "name": "ReadAsync 1023", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942591, "dur": 18, "ph": "X", "name": "ReadAsync 474", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942611, "dur": 16, "ph": "X", "name": "ReadAsync 586", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942630, "dur": 15, "ph": "X", "name": "ReadAsync 398", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942646, "dur": 15, "ph": "X", "name": "ReadAsync 334", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942664, "dur": 17, "ph": "X", "name": "ReadAsync 253", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942683, "dur": 18, "ph": "X", "name": "ReadAsync 489", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942703, "dur": 16, "ph": "X", "name": "ReadAsync 455", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942722, "dur": 14, "ph": "X", "name": "ReadAsync 482", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942738, "dur": 16, "ph": "X", "name": "ReadAsync 261", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942756, "dur": 15, "ph": "X", "name": "ReadAsync 318", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942774, "dur": 15, "ph": "X", "name": "ReadAsync 390", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942791, "dur": 17, "ph": "X", "name": "ReadAsync 344", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942810, "dur": 14, "ph": "X", "name": "ReadAsync 369", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942825, "dur": 15, "ph": "X", "name": "ReadAsync 314", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942843, "dur": 14, "ph": "X", "name": "ReadAsync 168", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942859, "dur": 15, "ph": "X", "name": "ReadAsync 352", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942875, "dur": 1, "ph": "X", "name": "ProcessMessages 190", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942877, "dur": 16, "ph": "X", "name": "ReadAsync 190", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942895, "dur": 39, "ph": "X", "name": "ReadAsync 357", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942937, "dur": 21, "ph": "X", "name": "ReadAsync 285", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942959, "dur": 1, "ph": "X", "name": "ProcessMessages 673", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942961, "dur": 18, "ph": "X", "name": "ReadAsync 673", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424942981, "dur": 15, "ph": "X", "name": "ReadAsync 408", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943015, "dur": 28, "ph": "X", "name": "ReadAsync 242", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943044, "dur": 1, "ph": "X", "name": "ProcessMessages 1403", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943045, "dur": 17, "ph": "X", "name": "ReadAsync 1403", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943064, "dur": 17, "ph": "X", "name": "ReadAsync 323", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943083, "dur": 17, "ph": "X", "name": "ReadAsync 451", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943103, "dur": 16, "ph": "X", "name": "ReadAsync 541", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943122, "dur": 24, "ph": "X", "name": "ReadAsync 187", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943149, "dur": 28, "ph": "X", "name": "ReadAsync 666", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943181, "dur": 25, "ph": "X", "name": "ReadAsync 395", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943208, "dur": 20, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943230, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943232, "dur": 26, "ph": "X", "name": "ReadAsync 392", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943261, "dur": 17, "ph": "X", "name": "ReadAsync 569", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943280, "dur": 15, "ph": "X", "name": "ReadAsync 215", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943298, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943299, "dur": 15, "ph": "X", "name": "ReadAsync 300", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943316, "dur": 14, "ph": "X", "name": "ReadAsync 521", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943332, "dur": 16, "ph": "X", "name": "ReadAsync 334", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943350, "dur": 20, "ph": "X", "name": "ReadAsync 338", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943372, "dur": 15, "ph": "X", "name": "ReadAsync 601", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943389, "dur": 15, "ph": "X", "name": "ReadAsync 53", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943406, "dur": 15, "ph": "X", "name": "ReadAsync 319", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943423, "dur": 15, "ph": "X", "name": "ReadAsync 374", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943441, "dur": 15, "ph": "X", "name": "ReadAsync 376", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943458, "dur": 15, "ph": "X", "name": "ReadAsync 350", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943475, "dur": 17, "ph": "X", "name": "ReadAsync 514", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943494, "dur": 16, "ph": "X", "name": "ReadAsync 457", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943512, "dur": 16, "ph": "X", "name": "ReadAsync 423", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943530, "dur": 13, "ph": "X", "name": "ReadAsync 368", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943545, "dur": 15, "ph": "X", "name": "ReadAsync 332", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943560, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943562, "dur": 15, "ph": "X", "name": "ReadAsync 351", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943579, "dur": 46, "ph": "X", "name": "ReadAsync 494", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943629, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943652, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943654, "dur": 26, "ph": "X", "name": "ReadAsync 591", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943683, "dur": 18, "ph": "X", "name": "ReadAsync 699", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943703, "dur": 16, "ph": "X", "name": "ReadAsync 441", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943721, "dur": 17, "ph": "X", "name": "ReadAsync 492", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943740, "dur": 16, "ph": "X", "name": "ReadAsync 384", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943758, "dur": 16, "ph": "X", "name": "ReadAsync 482", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943777, "dur": 16, "ph": "X", "name": "ReadAsync 453", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943795, "dur": 17, "ph": "X", "name": "ReadAsync 531", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943814, "dur": 15, "ph": "X", "name": "ReadAsync 505", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943832, "dur": 16, "ph": "X", "name": "ReadAsync 340", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943850, "dur": 17, "ph": "X", "name": "ReadAsync 312", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943869, "dur": 16, "ph": "X", "name": "ReadAsync 345", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943887, "dur": 19, "ph": "X", "name": "ReadAsync 495", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943909, "dur": 16, "ph": "X", "name": "ReadAsync 335", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943928, "dur": 16, "ph": "X", "name": "ReadAsync 493", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943946, "dur": 16, "ph": "X", "name": "ReadAsync 382", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943964, "dur": 17, "ph": "X", "name": "ReadAsync 459", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424943983, "dur": 16, "ph": "X", "name": "ReadAsync 470", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424944001, "dur": 16, "ph": "X", "name": "ReadAsync 377", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424944019, "dur": 15, "ph": "X", "name": "ReadAsync 388", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424944037, "dur": 14, "ph": "X", "name": "ReadAsync 254", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424944053, "dur": 28, "ph": "X", "name": "ReadAsync 108", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424944083, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424944102, "dur": 16, "ph": "X", "name": "ReadAsync 338", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424944121, "dur": 17, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424944140, "dur": 17, "ph": "X", "name": "ReadAsync 407", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424944159, "dur": 17, "ph": "X", "name": "ReadAsync 400", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424944178, "dur": 16, "ph": "X", "name": "ReadAsync 430", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424944197, "dur": 16, "ph": "X", "name": "ReadAsync 351", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424944214, "dur": 15, "ph": "X", "name": "ReadAsync 283", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424944231, "dur": 15, "ph": "X", "name": "ReadAsync 272", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424944249, "dur": 59, "ph": "X", "name": "ReadAsync 299", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424944310, "dur": 24, "ph": "X", "name": "ReadAsync 211", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424944337, "dur": 23, "ph": "X", "name": "ReadAsync 480", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424944362, "dur": 18, "ph": "X", "name": "ReadAsync 317", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424944382, "dur": 17, "ph": "X", "name": "ReadAsync 307", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424944402, "dur": 14, "ph": "X", "name": "ReadAsync 456", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424944418, "dur": 19, "ph": "X", "name": "ReadAsync 86", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424944439, "dur": 17, "ph": "X", "name": "ReadAsync 396", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424944458, "dur": 20, "ph": "X", "name": "ReadAsync 312", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424944480, "dur": 18, "ph": "X", "name": "ReadAsync 371", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424944502, "dur": 16, "ph": "X", "name": "ReadAsync 339", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424944520, "dur": 26123, "ph": "X", "name": "ReadAsync 305", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424970651, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424970656, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424970684, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424970686, "dur": 282, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424970973, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424971019, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424971023, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424971063, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424971065, "dur": 40, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424971107, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424971109, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424971137, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424971166, "dur": 632, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424971804, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424971837, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424971839, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424971893, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424971926, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424971928, "dur": 38, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424971971, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424972001, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424972003, "dur": 27, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424972034, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424972073, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424972109, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424972111, "dur": 227, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424972342, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424972344, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424972380, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424972382, "dur": 78, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424972466, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424972498, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424972500, "dur": 97, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424972602, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424972635, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424972637, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424972682, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424972718, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424972720, "dur": 158, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424972883, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424972917, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424972919, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424972981, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973009, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973108, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973110, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973148, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973153, "dur": 109, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973266, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973297, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973299, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973331, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973354, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973356, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973404, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973434, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973467, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973469, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973534, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973565, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973567, "dur": 162, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973734, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973763, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973765, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973796, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973798, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973827, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": ****************, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973855, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973884, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973913, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973914, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973945, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973947, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424973980, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424974010, "dur": 119, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424974134, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424974163, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424974165, "dur": 124, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424974292, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424974322, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424974370, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424974405, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424974434, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424974458, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424974537, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424974567, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424974593, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424974621, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424974623, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424974700, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424974703, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424974736, "dur": 156, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424974897, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424974923, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424974925, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424974949, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424974952, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424974993, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424975028, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424975055, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424975126, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424975159, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424975161, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424975194, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424975196, "dur": 90, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424975291, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424975316, "dur": 160, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424975483, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424975513, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424975549, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424975551, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424975590, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424975621, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424975623, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424975656, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424975658, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424975695, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424975726, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424975754, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424975756, "dur": 90, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424975851, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424975879, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424975881, "dur": 157, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424976043, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424976071, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424976099, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424976128, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424976130, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424976175, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424976208, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424976210, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424976245, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424976282, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424976314, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424976346, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424976388, "dur": 207, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424976599, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424976626, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424976627, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424976693, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424976695, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424976724, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424976767, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424976802, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424976836, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424976838, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424976869, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424976897, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424976927, "dur": 253, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424977186, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424977219, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424977269, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424977301, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424977303, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424977333, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424977363, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424977366, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424977396, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424977444, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424977477, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424977479, "dur": 50, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424977534, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424977568, "dur": 167, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424977739, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424977766, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424977854, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424977856, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424977887, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424977889, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424977919, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424977921, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424977948, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424977978, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424977980, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424978007, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424978045, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424978048, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424978077, "dur": 160, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424978242, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424978272, "dur": 162, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424978438, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424978440, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424978467, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424978469, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424978495, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424978497, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424978563, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424978591, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424978592, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424978624, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424978626, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424978652, "dur": 108, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424978765, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424978796, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424978798, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424978894, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424978927, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424978998, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979029, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979059, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979120, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979158, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979191, "dur": 94, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979289, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979321, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979347, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979379, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979407, "dur": 101, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979514, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979542, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979568, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979606, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979609, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979636, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979670, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979694, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979762, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979788, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979816, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979847, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979874, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979897, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979957, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979984, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424979986, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980073, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980100, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980101, "dur": 72, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980178, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980210, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980292, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980321, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980323, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980353, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980393, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980416, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980417, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980442, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980444, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980470, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980472, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980498, "dur": 205, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980707, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980735, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980737, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980765, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980815, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980817, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980847, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980849, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980897, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980932, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980934, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980960, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980986, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424980988, "dur": 531, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424981526, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424981556, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424981560, "dur": 1006, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424982569, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424982572, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424982604, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424982608, "dur": 5289, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424987904, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424987907, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424987930, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424987933, "dur": 1683, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424989620, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424989622, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424989677, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424989680, "dur": 6479, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424996168, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424996172, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424996200, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294424996204, "dur": 4800, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425001010, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425001014, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425001036, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425001038, "dur": 3234, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425004278, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425004281, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425004318, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425004320, "dur": 2650, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425006974, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425006976, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425006998, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425007002, "dur": 6212, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425013222, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425013225, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425013251, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425013254, "dur": 5507, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425018769, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425018773, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425018818, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425018821, "dur": 3922, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425022748, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425022751, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425022790, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425022792, "dur": 1036, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425023833, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425023871, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425023874, "dur": 36, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425023914, "dur": 4781, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425028700, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425028703, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425028728, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425028731, "dur": 10268, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425039008, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425039013, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425039044, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425039047, "dur": 166, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425039218, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425039253, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425039255, "dur": 290, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425039549, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425039552, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425039593, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425039595, "dur": 331, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425039931, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425039965, "dur": 256, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425040227, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425040263, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425040265, "dur": 372, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425040643, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425040678, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425040680, "dur": 420, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425041104, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425041129, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425041131, "dur": 342, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425041480, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425041508, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425041510, "dur": 373, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425041888, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425041927, "dur": 327, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425042257, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425042259, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425042294, "dur": 361, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425042658, "dur": 30, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425042692, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425042695, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425042750, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425042774, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425042840, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425042873, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425042875, "dur": 253, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425043132, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425043165, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425043167, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425043257, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425043283, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425043350, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425043380, "dur": 288, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425043672, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425043674, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425043708, "dur": 3693, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425047414, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425047420, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425047511, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425047514, "dur": 136, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425047654, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425047656, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425047703, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425047705, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425047745, "dur": 174, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425047923, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425047925, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425047958, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425047964, "dur": 92, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425048061, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425048092, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425048094, "dur": 162, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425048262, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425048291, "dur": 437, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425048733, "dur": 111, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425048848, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425048850, "dur": 276, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425049131, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425049161, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425049162, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425049215, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425049244, "dur": 34, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425049282, "dur": 482, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425049770, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425049800, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425049801, "dur": 555, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425050361, "dur": 348, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425050712, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425050714, "dur": 977, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425051697, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425051733, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425051735, "dur": 514, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425052253, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425052256, "dur": 228, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425052489, "dur": 21, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425052511, "dur": 295, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425052812, "dur": 116, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425052931, "dur": 17, "ph": "X", "name": "ProcessMessages 126", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425052950, "dur": 37, "ph": "X", "name": "ReadAsync 126", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425052990, "dur": 4, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425052995, "dur": 481, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425053480, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425053483, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425053504, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425053519, "dur": 189, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425053712, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425053715, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425053749, "dur": 5, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425053755, "dur": 322, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425054082, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425054111, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425054113, "dur": 231, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425054350, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425054377, "dur": 1317, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425055698, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425055700, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425055793, "dur": 14, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425055808, "dur": 46, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425055859, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425055891, "dur": 349, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425056246, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425056329, "dur": 200, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425056534, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425056569, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425056570, "dur": 117, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425056694, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425056711, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425056736, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425056737, "dur": 253, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425056995, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425057022, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425057024, "dur": 362, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425057391, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425057438, "dur": 127, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425057568, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425057570, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425057638, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425057640, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425057673, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425057713, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425057738, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425057740, "dur": 112, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425057857, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425057888, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425057890, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425057941, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425057969, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425057971, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425057998, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425058136, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425058166, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425058196, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425058198, "dur": 147, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425058349, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425058352, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425058381, "dur": 136, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425058521, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425058553, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425058555, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425058599, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425058631, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425058634, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425058656, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425058689, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425058719, "dur": 86, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425058809, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425058841, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425058843, "dur": 203, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425059050, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425059052, "dur": 169, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425059224, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425059226, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425059257, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425059259, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425059283, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425059362, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425059387, "dur": 152, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425059544, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425059576, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425059578, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425059613, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425059644, "dur": 375, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425060022, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425060024, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425060063, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425060067, "dur": 449, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425060521, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425060557, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425060559, "dur": 424, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425060987, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425061017, "dur": 144, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425061175, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425061208, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425061210, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425061239, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425061241, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425061288, "dur": 134, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425061427, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425061456, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425061458, "dur": 224, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425061687, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425061719, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425061792, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425061831, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425061833, "dur": 137, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425061975, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425062004, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425062104, "dur": 119, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425062228, "dur": 306, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425062540, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425062570, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425062571, "dur": 108, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425062685, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425062713, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425062715, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425062734, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425062817, "dur": 223, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425063044, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425063085, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425063087, "dur": 218, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425063309, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425063347, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425063377, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425063378, "dur": 127, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425063512, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425063548, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425063549, "dur": 107, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425063661, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425063693, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425063695, "dur": 93, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425063791, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425063820, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425063822, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425063901, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425063945, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425063946, "dur": 69, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425064019, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425064051, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425064053, "dur": 123, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425064180, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425064210, "dur": 108, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425064321, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425064323, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425064349, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425064351, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425064378, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425064448, "dur": 802, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425065254, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425065257, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425065316, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425065377, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425065379, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425065435, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425065477, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425065480, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425065546, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425065589, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425065591, "dur": 518, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425066114, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425066149, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425066151, "dur": 157, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425066314, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425066341, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425066373, "dur": 132, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425066509, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425066541, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425066543, "dur": 254, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425066801, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425066832, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425066834, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425066900, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425066927, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425066929, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425066956, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425066958, "dur": 139, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067101, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067129, "dur": 12, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067142, "dur": 155, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067302, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067333, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067335, "dur": 33, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067372, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067405, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067407, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067486, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067514, "dur": 82, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067599, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067602, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067642, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067644, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067671, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067724, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067756, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067758, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067790, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067824, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067855, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067856, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067899, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067900, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067950, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425067983, "dur": 204, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425068193, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425068225, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425068246, "dur": 109, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425068360, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425068397, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425068399, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425068433, "dur": 805, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425069243, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425069272, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425069274, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425069357, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425069393, "dur": 118, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425069515, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425069547, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425069548, "dur": 110, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425069661, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425069696, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425069698, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425069763, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425069805, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425069888, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425069922, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425069959, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425069961, "dur": 124, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425070090, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425070124, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425070126, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425070159, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425070191, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425070220, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425070222, "dur": 319, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425070546, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425070577, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425070578, "dur": 728, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425071311, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425071345, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425071347, "dur": 102, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425071453, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425071484, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425071486, "dur": 206, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425071696, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425071726, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425071728, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425071816, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425071844, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425071846, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425071876, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425071878, "dur": 74, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425071957, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425071990, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425071992, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425072031, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425072035, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425072079, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425072081, "dur": 234, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425072319, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425072349, "dur": 140, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425072492, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425072496, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425072529, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425072531, "dur": 101, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425072636, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425072669, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425072671, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425072751, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425072779, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425072781, "dur": 390, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425073176, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425073208, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425073210, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425073272, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425073308, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425073310, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425073347, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425073373, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425073375, "dur": 28, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425073406, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425073408, "dur": 26, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425073437, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425073439, "dur": 292, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425073735, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425073767, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425073847, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425073876, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425073878, "dur": 60, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425073943, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425073971, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425073973, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425073996, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425074023, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425074025, "dur": 193, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425074220, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425074223, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425074256, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425074258, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425074292, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425074323, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425074325, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425074352, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425074354, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425074426, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425074461, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425074462, "dur": 90, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425074557, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425074586, "dur": 257, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425074847, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425074850, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425074883, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425074885, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425074960, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425074992, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425074993, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425075055, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425075087, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425075089, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425075121, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425075150, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425075192, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425075221, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425075223, "dur": 120, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425075349, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425075385, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425075387, "dur": 343, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425075734, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425075770, "dur": 457, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425076232, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425076266, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425076268, "dur": 138, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425076411, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425076446, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425076482, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425076520, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425076546, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425076548, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425076579, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425076649, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425076677, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425076679, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425076716, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425076745, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425076747, "dur": 173, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425076925, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425076957, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425076995, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077022, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077024, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077081, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077113, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077116, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077147, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077149, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077205, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077207, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077240, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077275, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077325, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077354, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077356, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077381, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077413, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077439, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077440, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077539, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077568, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077570, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077610, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077612, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077641, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077643, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077692, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077728, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077730, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077760, "dur": 25, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077789, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077817, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077819, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077853, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077920, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077951, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077952, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425077975, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425078028, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425078057, "dur": 75, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425078136, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425078167, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425078170, "dur": 580, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425078756, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425078786, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425078788, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425078858, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425078890, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425078892, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425078924, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425078990, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425079030, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425079032, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425079073, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425079106, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425079108, "dur": 218, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425079332, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425079368, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425079370, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425079405, "dur": 189, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425079599, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425079639, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425079641, "dur": 101, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425079748, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425079779, "dur": 211, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425079995, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425080026, "dur": 424, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425080454, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425080486, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425080519, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425080552, "dur": 703, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425081259, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425081298, "dur": 338, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425081641, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425081671, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425081673, "dur": 196, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425081874, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425081903, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425081904, "dur": 113, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425082022, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425082057, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425082058, "dur": 419, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425082482, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425082513, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425082515, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425082548, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425082588, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425082590, "dur": 146, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425082741, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425082769, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425082771, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425082797, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425082799, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425082829, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425082858, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425082860, "dur": 218, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425083082, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425083111, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425083113, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425083164, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425083192, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425083194, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425083228, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425083289, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425083321, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425083399, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425083432, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425083465, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425083467, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425083494, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425083496, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425083523, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425083553, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425083572, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425083604, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425083632, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425083634, "dur": 233, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425083874, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425083903, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425083905, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425083969, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425084031, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425084033, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425084067, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425084083, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425084085, "dur": 107, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425084197, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425084222, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425084224, "dur": 135, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425084364, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425084395, "dur": 64, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425084463, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425084494, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425084496, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425084578, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425084608, "dur": 240, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425084852, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425084880, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425084882, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425084959, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425084985, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425085021, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425085054, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425085056, "dur": 581, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425085642, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425085675, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425085677, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425085755, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425085785, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425085788, "dur": 345, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425086135, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425086137, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425086172, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425086174, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425086238, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425086268, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425086271, "dur": 248, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425086523, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425086558, "dur": 168, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425086730, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425086762, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425086764, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425086788, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425086792, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425086821, "dur": 247, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425087071, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425087073, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425087105, "dur": 3, "ph": "X", "name": "ProcessMessages 24", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425087110, "dur": 312, "ph": "X", "name": "ReadAsync 24", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425087427, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425087454, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425087457, "dur": 33, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425087493, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425087495, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425087525, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425087527, "dur": 107, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425087639, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425087663, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425087665, "dur": 9899, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425097577, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425097582, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425097611, "dur": 23, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425097635, "dur": 145, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425097786, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425097841, "dur": 5, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425097848, "dur": 251, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425098104, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425098156, "dur": 8, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425098165, "dur": 4372, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425102552, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425102557, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425102602, "dur": 16, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425102619, "dur": 11569, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425114198, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425114203, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425114237, "dur": 15, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425114253, "dur": 56245, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425170508, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425170513, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425170566, "dur": 13, "ph": "X", "name": "ProcessMessages 444", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425170580, "dur": 49296, "ph": "X", "name": "ReadAsync 444", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425219886, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425219890, "dur": 226, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425220119, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425220123, "dur": 1327, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425221456, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425221495, "dur": 12, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425221509, "dur": 26287, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425247806, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425247810, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425247844, "dur": 11, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425247857, "dur": 37, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425247897, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425247899, "dur": 340, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425248245, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 34359738368, "ts": 1755294425248266, "dur": 14442, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 284300, "tid": 79301, "ts": 1755294425264092, "dur": 2605, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 284300, "tid": 30064771072, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 284300, "tid": 30064771072, "ts": 1755294424894278, "dur": 368515, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 284300, "tid": 30064771072, "ts": 1755294424894367, "dur": 4542, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 284300, "tid": 30064771072, "ts": 1755294425262799, "dur": 134, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 284300, "tid": 30064771072, "ts": 1755294425262935, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 284300, "tid": 79301, "ts": 1755294425266700, "dur": 9, "ph": "X", "name": "BuildAsync", "args": {} },
{ "pid": 284300, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 284300, "tid": 1, "ts": 1755294424633023, "dur": 2501, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 284300, "tid": 1, "ts": 1755294424635530, "dur": 257184, "ph": "X", "name": "<SetupBuildRequest>b__0", "args": {} },
{ "pid": 284300, "tid": 1, "ts": 1755294424892716, "dur": 1542, "ph": "X", "name": "WriteJson", "args": {} },
{ "pid": 284300, "tid": 79301, "ts": 1755294425266711, "dur": 5, "ph": "X", "name": "", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1755294424928964, "dur":5106, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755294424934078, "dur":569, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755294424934686, "dur":56, "ph":"X", "name": "Tundra",  "args": { "detail":"PrepareNodes" }}
,{ "pid":12345, "tid":0, "ts":1755294424934742, "dur":308, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755294424935848, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Domain_Reload.dll" }}
,{ "pid":12345, "tid":0, "ts":1755294424938202, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.StreamingModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1755294424941804, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine-FeaturesChecked.txt_1bd3.info" }}
,{ "pid":12345, "tid":0, "ts":1755294424941888, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.DSPGraphModule-FeaturesChecked.txt_8ndh.info" }}
,{ "pid":12345, "tid":0, "ts":1755294424942185, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.HierarchyCoreModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1755294424942351, "dur":72, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.IdentifiersModule-FeaturesChecked.txt_l52h.info" }}
,{ "pid":12345, "tid":0, "ts":1755294424944086, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityTestProtocolModule-FeaturesChecked.txt_6hwq.info" }}
,{ "pid":12345, "tid":0, "ts":1755294424944758, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/machine.config" }}
,{ "pid":12345, "tid":0, "ts":1755294424935070, "dur":9958, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755294424945033, "dur":303374, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755294425248408, "dur":136, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755294425248544, "dur":71, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755294425248743, "dur":3756, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1755294424935493, "dur":9547, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294424949641, "dur":367, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"../../Editors/6000.2.0f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1755294424950008, "dur":302, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Assets/StreamingAssets" }}
,{ "pid":12345, "tid":1, "ts":1755294424950310, "dur":1293, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.0f1/Editor/Data/il2cpp/build/deploy" }}
,{ "pid":12345, "tid":1, "ts":1755294424951604, "dur":15100, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.0f1/Editor/Data/il2cpp/libil2cpp" }}
,{ "pid":12345, "tid":1, "ts":1755294424966704, "dur":320, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport" }}
,{ "pid":12345, "tid":1, "ts":1755294424967024, "dur":3047, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono" }}
,{ "pid":12345, "tid":1, "ts":1755294424970071, "dur":172, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/Data/Resources" }}
,{ "pid":12345, "tid":1, "ts":1755294424970243, "dur":279, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.0f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1755294424970522, "dur":279, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Library/PlayerDataCache/Win642/Data" }}
,{ "pid":12345, "tid":1, "ts":1755294424970802, "dur":61, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Temp/StagingArea/Data/Plugins" }}
,{ "pid":12345, "tid":1, "ts":1755294424970864, "dur":53, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Temp/StagingArea/Data/UnitySubsystems" }}
,{ "pid":12345, "tid":1, "ts":1755294424945048, "dur":25885, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294424970943, "dur":80, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/7893594715672875865.rsp" }}
,{ "pid":12345, "tid":1, "ts":1755294424971024, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294424971342, "dur":813, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424972159, "dur":170, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.BuildTestAssets.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424972331, "dur":276, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424972609, "dur":163, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\BakeryRuntimeAssembly.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424972777, "dur":151, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Domain_Reload.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424972930, "dur":199, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424973134, "dur":180, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.2D.Common.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424973315, "dur":528, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.AppUI.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424973849, "dur":168, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.AppUI.InternalAPIBridge.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424974022, "dur":181, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.AppUI.MVVM.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424974209, "dur":255, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.AppUI.Navigation.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424974466, "dur":373, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.AppUI.Redux.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424974841, "dur":220, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.AppUI.Undo.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424975063, "dur":294, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424975359, "dur":437, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424975798, "dur":161, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Formats.Fbx.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424975960, "dur":697, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.InferenceEngine.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424976659, "dur":202, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.InternalAPIEngineBridge.001.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424976862, "dur":623, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424977490, "dur":195, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424977690, "dur":210, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424977906, "dur":561, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424978473, "dur":204, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424978685, "dur":198, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424978885, "dur":203, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424979094, "dur":151, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424979249, "dur":154, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424979409, "dur":171, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424979589, "dur":430, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424980022, "dur":1011, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424981035, "dur":234, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424981271, "dur":187, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424981459, "dur":178, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424981642, "dur":317, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424981961, "dur":189, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424982156, "dur":1751, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424983909, "dur":200, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424984112, "dur":219, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424984332, "dur":241, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Serialization.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424984574, "dur":381, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424984961, "dur":310, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424985276, "dur":221, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424985498, "dur":550, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424986050, "dur":427, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424986479, "dur":154, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424986638, "dur":311, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294424987466, "dur":138, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\MethodsToPreserve.xml" }}
,{ "pid":12345, "tid":1, "ts":1755294424987609, "dur":122, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\TypesInScenes.xml" }}
,{ "pid":12345, "tid":1, "ts":1755294424987733, "dur":123, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\SerializedTypes.xml" }}
,{ "pid":12345, "tid":1, "ts":1755294424987861, "dur":188, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\EditorToUnityLinkerData.json" }}
,{ "pid":12345, "tid":1, "ts":1755294424971125, "dur":17210, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"UnityLinker C:/Unity/BLAME/BLAME/Library/Bee/artifacts/unitylinker_dwek.traceevents" }}
,{ "pid":12345, "tid":1, "ts":1755294424988336, "dur":16422, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425008105, "dur":14851, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"GenerateNativePluginsForAssemblies Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker" }}
,{ "pid":12345, "tid":1, "ts":1755294425022958, "dur":272, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425024106, "dur":101, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Plugins/x86_64/lib_burst_generated.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294425024208, "dur":189, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425024411, "dur":15044, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425039525, "dur":180, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425039721, "dur":318, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425040061, "dur":362, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425040434, "dur":286, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425040720, "dur":78, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.DirectorModule.dll" }}
,{ "pid":12345, "tid":1, "ts":1755294425040799, "dur":149, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/BakeryRuntimeAssembly-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":1, "ts":1755294425040949, "dur":182, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425041446, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425041842, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425042237, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425042649, "dur":179, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425043077, "dur":169, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425043511, "dur":116, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425043667, "dur":179, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425044188, "dur":3729, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425047927, "dur":165, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425048106, "dur":300, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425048418, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425048600, "dur":151, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425049561, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425049981, "dur":305, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425050664, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425051697, "dur":483, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425054752, "dur":1602, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425057316, "dur":186, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425057506, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.ServiceModel.Internals-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":1, "ts":1755294425057558, "dur":341, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425058812, "dur":284, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425059106, "dur":193, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425059312, "dur":136, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.VisualScripting.Antlr3.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":1, "ts":1755294425059452, "dur":648, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425061627, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425061750, "dur":1216, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425062975, "dur":890, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425063908, "dur":492, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425066328, "dur":517, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425067794, "dur":527, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425069716, "dur":663, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425071683, "dur":629, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425073209, "dur":558, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425073777, "dur":570, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425074356, "dur":440, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425074840, "dur":618, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425076631, "dur":409, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425077673, "dur":456, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425079262, "dur":562, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425081474, "dur":1053, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425083876, "dur":250, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425084133, "dur":455, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425084594, "dur":170, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":1, "ts":1755294425084765, "dur":712, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425086480, "dur":533, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755294425088152, "dur":160254, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294424935545, "dur":9525, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294424945076, "dur":722, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Channels.dll" }}
,{ "pid":12345, "tid":2, "ts":1755294424945798, "dur":601, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.RegularExpressions.dll" }}
,{ "pid":12345, "tid":2, "ts":1755294424946400, "dur":533, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Json.dll" }}
,{ "pid":12345, "tid":2, "ts":1755294424946933, "dur":553, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encodings.Web.dll" }}
,{ "pid":12345, "tid":2, "ts":1755294424947486, "dur":608, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.Extensions.dll" }}
,{ "pid":12345, "tid":2, "ts":1755294424948095, "dur":581, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.dll" }}
,{ "pid":12345, "tid":2, "ts":1755294424948676, "dur":675, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.CodePages.dll" }}
,{ "pid":12345, "tid":2, "ts":1755294424949352, "dur":566, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ServiceProcess.dll" }}
,{ "pid":12345, "tid":2, "ts":1755294424949918, "dur":567, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ServiceModel.Web.dll" }}
,{ "pid":12345, "tid":2, "ts":1755294424950485, "dur":845, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.SecureString.dll" }}
,{ "pid":12345, "tid":2, "ts":1755294424951331, "dur":535, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Principal.Windows.dll" }}
,{ "pid":12345, "tid":2, "ts":1755294424951866, "dur":551, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Principal.dll" }}
,{ "pid":12345, "tid":2, "ts":1755294424952417, "dur":548, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.dll" }}
,{ "pid":12345, "tid":2, "ts":1755294424952966, "dur":528, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.X509Certificates.dll" }}
,{ "pid":12345, "tid":2, "ts":1755294424953494, "dur":585, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Primitives.dll" }}
,{ "pid":12345, "tid":2, "ts":1755294424954079, "dur":567, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.OpenSsl.dll" }}
,{ "pid":12345, "tid":2, "ts":1755294424954646, "dur":645, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Encoding.dll" }}
,{ "pid":12345, "tid":2, "ts":1755294424955291, "dur":563, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.dll" }}
,{ "pid":12345, "tid":2, "ts":1755294424955854, "dur":597, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Csp.dll" }}
,{ "pid":12345, "tid":2, "ts":1755294424956451, "dur":579, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Cng.dll" }}
,{ "pid":12345, "tid":2, "ts":1755294424945076, "dur":11954, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":****************, "dur":630, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Drawing.dll" }}
,{ "pid":12345, "tid":2, "ts":1755294424959599, "dur":567, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Design.dll" }}
,{ "pid":12345, "tid":2, "ts":1755294424957030, "dur":8008, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294424965543, "dur":543, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.TextRenderingModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1755294424965039, "dur":4563, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294424969603, "dur":1344, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294424970962, "dur":532, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294424971507, "dur":803, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294424972317, "dur":639, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294424972969, "dur":653, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294424973628, "dur":600, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294424974236, "dur":551, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294424974795, "dur":596, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294424975399, "dur":575, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294424975983, "dur":554, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294424976542, "dur":549, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294424977097, "dur":582, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294424977686, "dur":547, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294424978241, "dur":494, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294424978743, "dur":514, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294424979266, "dur":611, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294424979885, "dur":231, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/UnityCrashHandler64.exe" }}
,{ "pid":12345, "tid":2, "ts":1755294424980117, "dur":199, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294424980358, "dur":124, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":2, "ts":1755294424980483, "dur":306, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294424980796, "dur":268, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/settings.map" }}
,{ "pid":12345, "tid":2, "ts":1755294424981065, "dur":325, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425040165, "dur":2625, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/Resources/unity_builtin_extra" }}
,{ "pid":12345, "tid":2, "ts":1755294425042919, "dur":200, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AMDModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1755294425043120, "dur":214, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425043573, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425044032, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425044162, "dur":3669, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425047858, "dur":349, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425048258, "dur":165, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425049070, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425049492, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425049931, "dur":312, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425053917, "dur":924, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425060024, "dur":491, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425061356, "dur":324, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425061702, "dur":895, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425062752, "dur":2192, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425064944, "dur":831, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1755294425065778, "dur":171, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Numerics-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1755294425065949, "dur":1056, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425068081, "dur":653, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425069850, "dur":802, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425071806, "dur":645, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425073330, "dur":594, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425073934, "dur":515, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425074449, "dur":74, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":2, "ts":1755294425074525, "dur":59, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.XRModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1755294425074585, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425075693, "dur":531, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425077178, "dur":328, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425077514, "dur":367, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425077888, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425078039, "dur":219, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425078268, "dur":172, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425078452, "dur":179, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425078670, "dur":714, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425081408, "dur":360, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TextRenderingModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1755294425081769, "dur":597, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425083652, "dur":355, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425084058, "dur":437, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425085624, "dur":626, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755294425087588, "dur":11019, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level0" }}
,{ "pid":12345, "tid":2, "ts":1755294425098978, "dur":149434, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294424935501, "dur":9547, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294424945857, "dur":587, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.runtimeconfig.json" }}
,{ "pid":12345, "tid":3, "ts":1755294424946445, "dur":546, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.exe" }}
,{ "pid":12345, "tid":3, "ts":1755294424946991, "dur":518, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.dll.config" }}
,{ "pid":12345, "tid":3, "ts":1755294424947509, "dur":593, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.dll" }}
,{ "pid":12345, "tid":3, "ts":1755294424948103, "dur":573, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.deps.json" }}
,{ "pid":12345, "tid":3, "ts":1755294424948676, "dur":686, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.TinyProfiler.dll" }}
,{ "pid":12345, "tid":3, "ts":1755294424949362, "dur":568, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Options.dll" }}
,{ "pid":12345, "tid":3, "ts":1755294424949931, "dur":552, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.Output.xml" }}
,{ "pid":12345, "tid":3, "ts":1755294424950483, "dur":840, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.Output.dll" }}
,{ "pid":12345, "tid":3, "ts":1755294424951323, "dur":540, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.dll" }}
,{ "pid":12345, "tid":3, "ts":1755294424951864, "dur":543, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Shell.dll" }}
,{ "pid":12345, "tid":3, "ts":1755294424952407, "dur":532, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Compile.dll" }}
,{ "pid":12345, "tid":3, "ts":1755294424952939, "dur":534, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Common35.dll" }}
,{ "pid":12345, "tid":3, "ts":1755294424953473, "dur":601, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Common.dll" }}
,{ "pid":12345, "tid":3, "ts":1755294424954074, "dur":579, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.dll" }}
,{ "pid":12345, "tid":3, "ts":1755294424954653, "dur":654, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.Data.dll" }}
,{ "pid":12345, "tid":3, "ts":1755294424945055, "dur":10252, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294424955307, "dur":573, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp.dll.config" }}
,{ "pid":12345, "tid":3, "ts":1755294424955880, "dur":582, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp-compile.runtimeconfig.json" }}
,{ "pid":12345, "tid":3, "ts":1755294424956463, "dur":577, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp-compile.exe" }}
,{ "pid":12345, "tid":3, "ts":1755294424955307, "dur":4363, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294424959671, "dur":646, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\I18N.CJK.dll" }}
,{ "pid":12345, "tid":3, "ts":1755294424959671, "dur":4905, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294424964576, "dur":3308, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294424967884, "dur":3062, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294424970976, "dur":595, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294424971581, "dur":884, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294424972473, "dur":582, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294424973062, "dur":699, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294424973769, "dur":548, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294424974322, "dur":544, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294424974873, "dur":577, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294424975454, "dur":568, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294424976028, "dur":564, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294424976597, "dur":619, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294424977222, "dur":541, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294424977771, "dur":578, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294424978356, "dur":575, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294424978938, "dur":448, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294424979394, "dur":645, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294424980044, "dur":205, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/config" }}
,{ "pid":12345, "tid":3, "ts":1755294424980250, "dur":319, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294424980575, "dur":150, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":3, "ts":1755294424980725, "dur":219, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294424980948, "dur":150, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/DirectML.dll" }}
,{ "pid":12345, "tid":3, "ts":1755294424981098, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755294424982415, "dur":265860, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets1.assets.resS" }}
,{ "pid":12345, "tid":4, "ts":1755294424935525, "dur":9531, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294424945062, "dur":681, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.WindowsDesktop.dll" }}
,{ "pid":12345, "tid":4, "ts":1755294424945743, "dur":534, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.WebGL.dll" }}
,{ "pid":12345, "tid":4, "ts":1755294424946277, "dur":563, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.VisionOS.dll" }}
,{ "pid":12345, "tid":4, "ts":1755294424946840, "dur":551, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.UniversalWindows.dll" }}
,{ "pid":12345, "tid":4, "ts":1755294424947392, "dur":597, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.MacOSX.dll" }}
,{ "pid":12345, "tid":4, "ts":1755294424947989, "dur":588, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.Linux.dll" }}
,{ "pid":12345, "tid":4, "ts":1755294424948577, "dur":566, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.iOS.dll" }}
,{ "pid":12345, "tid":4, "ts":1755294424949143, "dur":687, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.EmbeddedLinux.dll" }}
,{ "pid":12345, "tid":4, "ts":1755294424949831, "dur":586, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.dll" }}
,{ "pid":12345, "tid":4, "ts":1755294424950417, "dur":785, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.AppleTV.dll" }}
,{ "pid":12345, "tid":4, "ts":1755294424951202, "dur":597, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.Android.dll" }}
,{ "pid":12345, "tid":4, "ts":1755294424951799, "dur":536, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.Output.xml" }}
,{ "pid":12345, "tid":4, "ts":1755294424952335, "dur":535, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.Output.dll" }}
,{ "pid":12345, "tid":4, "ts":1755294424952870, "dur":546, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.dll" }}
,{ "pid":12345, "tid":4, "ts":1755294424953416, "dur":586, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Cecil.Awesome.dll" }}
,{ "pid":12345, "tid":4, "ts":1755294424954002, "dur":535, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Api.Attributes.dll" }}
,{ "pid":12345, "tid":4, "ts":1755294424954538, "dur":651, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XPath.XDocument.dll" }}
,{ "pid":12345, "tid":4, "ts":1755294424955189, "dur":555, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XPath.dll" }}
,{ "pid":12345, "tid":4, "ts":1755294424955744, "dur":597, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XmlSerializer.dll" }}
,{ "pid":12345, "tid":4, "ts":1755294424956341, "dur":604, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XmlDocument.dll" }}
,{ "pid":12345, "tid":4, "ts":1755294424945061, "dur":11885, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294424956946, "dur":2832, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294424959778, "dur":4005, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294424963784, "dur":3934, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294424967718, "dur":3219, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294424970967, "dur":577, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294424971556, "dur":834, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294424972399, "dur":441, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294424972847, "dur":633, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294424973486, "dur":412, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294424973907, "dur":516, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294424974429, "dur":604, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294424975040, "dur":577, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294424975625, "dur":568, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294424976201, "dur":554, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294424976760, "dur":551, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294424977316, "dur":556, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294424977878, "dur":553, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294424978435, "dur":557, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294424978997, "dur":510, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294424979513, "dur":594, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294424980127, "dur":296, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/web.config" }}
,{ "pid":12345, "tid":4, "ts":1755294424980424, "dur":389, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294424980817, "dur":306, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/machine.config" }}
,{ "pid":12345, "tid":4, "ts":1755294424981124, "dur":270, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425013700, "dur":39546, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.resource" }}
,{ "pid":12345, "tid":4, "ts":1755294425053466, "dur":508, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425056466, "dur":371, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425057941, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425058089, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425058225, "dur":241, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425058474, "dur":154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425058663, "dur":353, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425060327, "dur":1149, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425062612, "dur":65, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.HighDefinition.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1755294425062678, "dur":277, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425063613, "dur":903, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425064527, "dur":548, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425065076, "dur":717, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.VirtualTexturingModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1755294425065794, "dur":56, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Assembly-CSharp-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1755294425065850, "dur":207, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425067266, "dur":182, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425067455, "dur":306, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425067799, "dur":482, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425069446, "dur":565, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425071246, "dur":582, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425073226, "dur":621, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425073857, "dur":503, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425074377, "dur":412, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425074828, "dur":728, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425076705, "dur":265, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425076978, "dur":237, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425077224, "dur":354, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425077588, "dur":320, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425077919, "dur":158, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425078084, "dur":212, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425078303, "dur":169, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425078508, "dur":741, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425080554, "dur":395, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425082807, "dur":514, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425083329, "dur":328, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425083667, "dur":373, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425084056, "dur":310, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425084376, "dur":334, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425084718, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425084925, "dur":595, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425086591, "dur":683, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755294425088140, "dur":160263, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755294424935638, "dur":9476, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755294424945119, "dur":665, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424945784, "dur":596, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.Calendars.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424946381, "dur":540, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Formats.Tar.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424946921, "dur":561, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Formats.Asn1.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424947482, "dur":577, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Dynamic.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424948059, "dur":585, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Drawing.Primitives.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424948644, "dur":552, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Drawing.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424949196, "dur":674, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424949870, "dur":562, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Tracing.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424950432, "dur":780, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.TraceSource.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424951212, "dur":613, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Tools.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424951826, "dur":541, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.TextWriterTraceListener.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424952367, "dur":522, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.StackTrace.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424952890, "dur":542, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Process.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424953432, "dur":578, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.FileVersionInfo.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424954010, "dur":581, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.DiagnosticSource.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424954591, "dur":613, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Debug.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424955204, "dur":552, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Contracts.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424955756, "dur":609, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424956365, "dur":601, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.DataSetExtensions.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424945119, "dur":11847, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755294424957865, "dur":554, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\monodoc.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424959614, "dur":599, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\SystemWebTestShim.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424956967, "dur":7248, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755294424964216, "dur":3675, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755294424967892, "dur":3042, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755294424970942, "dur":62, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/boot.config_tyr4.info" }}
,{ "pid":12345, "tid":5, "ts":1755294424971005, "dur":582, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755294424971607, "dur":197, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Win642\\Data\\boot.config" }}
,{ "pid":12345, "tid":5, "ts":1755294424971822, "dur":183, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Win642\\Data\\ScriptingAssemblies.json" }}
,{ "pid":12345, "tid":5, "ts":1755294424972116, "dur":769, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424972890, "dur":170, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Domain_Reload.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424973061, "dur":205, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424973268, "dur":164, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.2D.Common.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424973434, "dur":766, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.AppUI.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424974208, "dur":267, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.AppUI.Navigation.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424974477, "dur":236, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.AppUI.Redux.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424974719, "dur":140, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.AppUI.Undo.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424974861, "dur":385, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424975247, "dur":412, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424975665, "dur":159, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Formats.Fbx.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424975826, "dur":817, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.InferenceEngine.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424976649, "dur":222, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.InternalAPIEngineBridge.001.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424976874, "dur":636, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424977512, "dur":188, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424977706, "dur":211, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424977918, "dur":601, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424978522, "dur":205, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424978728, "dur":260, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424978990, "dur":217, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424979209, "dur":154, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424979364, "dur":211, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424979577, "dur":197, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424979776, "dur":325, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424980102, "dur":924, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424981032, "dur":228, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424981262, "dur":181, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424981450, "dur":196, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424981648, "dur":327, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424981977, "dur":182, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424982161, "dur":1738, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424983906, "dur":217, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424984125, "dur":179, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424984310, "dur":256, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Serialization.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424984572, "dur":390, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424984964, "dur":336, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424985302, "dur":189, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424985495, "dur":531, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424986032, "dur":425, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424986458, "dur":175, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424986638, "dur":338, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":5, "ts":1755294424971593, "dur":15902, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"AddBootConfigGUID Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":5, "ts":1755294424988253, "dur":119, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755294424989371, "dur":181659, "ph":"X", "name": "AddBootConfigGUID",  "args": { "detail":"Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":5, "ts":1755294425220146, "dur":174, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\boot.config" }}
,{ "pid":12345, "tid":5, "ts":1755294425220027, "dur":295, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/boot.config" }}
,{ "pid":12345, "tid":5, "ts":1755294425220347, "dur":1625, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/boot.config" }}
,{ "pid":12345, "tid":5, "ts":1755294425221974, "dur":26434, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294424935538, "dur":9524, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294424945068, "dur":695, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XDocument.dll" }}
,{ "pid":12345, "tid":6, "ts":1755294424945763, "dur":549, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.Serialization.dll" }}
,{ "pid":12345, "tid":6, "ts":1755294424946312, "dur":551, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.ReaderWriter.dll" }}
,{ "pid":12345, "tid":6, "ts":1755294424946863, "dur":547, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":6, "ts":1755294424947411, "dur":594, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.dll" }}
,{ "pid":12345, "tid":6, "ts":1755294424948005, "dur":565, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Windows.dll" }}
,{ "pid":12345, "tid":6, "ts":1755294424948570, "dur":539, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Web.HttpUtility.dll" }}
,{ "pid":12345, "tid":6, "ts":1755294424949109, "dur":704, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Web.dll" }}
,{ "pid":12345, "tid":6, "ts":1755294424949814, "dur":526, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ValueTuple.dll" }}
,{ "pid":12345, "tid":6, "ts":1755294424950341, "dur":648, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Transactions.Local.dll" }}
,{ "pid":12345, "tid":6, "ts":1755294424950989, "dur":736, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Transactions.dll" }}
,{ "pid":12345, "tid":6, "ts":1755294424951725, "dur":539, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Timer.dll" }}
,{ "pid":12345, "tid":6, "ts":1755294424952265, "dur":583, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.ThreadPool.dll" }}
,{ "pid":12345, "tid":6, "ts":1755294424952848, "dur":548, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Thread.dll" }}
,{ "pid":12345, "tid":6, "ts":1755294424953397, "dur":590, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Parallel.dll" }}
,{ "pid":12345, "tid":6, "ts":1755294424953988, "dur":526, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Extensions.dll" }}
,{ "pid":12345, "tid":6, "ts":1755294424954514, "dur":636, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.dll" }}
,{ "pid":12345, "tid":6, "ts":1755294424955151, "dur":578, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Dataflow.dll" }}
,{ "pid":12345, "tid":6, "ts":1755294424955730, "dur":603, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Overlapped.dll" }}
,{ "pid":12345, "tid":6, "ts":1755294424956333, "dur":620, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.dll" }}
,{ "pid":12345, "tid":6, "ts":1755294424945068, "dur":11885, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294424956954, "dur":2772, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294424959727, "dur":4038, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294424963765, "dur":4174, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294424967939, "dur":3012, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294424970960, "dur":590, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294424971560, "dur":943, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294424972519, "dur":657, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294424973190, "dur":660, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294424973856, "dur":496, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294424974357, "dur":573, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294424974935, "dur":603, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294424975543, "dur":556, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294424976105, "dur":561, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294424976674, "dur":588, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294424977270, "dur":534, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294424977811, "dur":592, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294424978409, "dur":547, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294424978962, "dur":531, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294424979500, "dur":505, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294424980013, "dur":210, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/mconfig/config.xml" }}
,{ "pid":12345, "tid":6, "ts":1755294424980224, "dur":231, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294424980461, "dur":173, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/machine.config" }}
,{ "pid":12345, "tid":6, "ts":1755294424980635, "dur":191, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294424980830, "dur":218, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":6, "ts":1755294424981049, "dur":223, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425001493, "dur":51768, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.assets" }}
,{ "pid":12345, "tid":6, "ts":1755294425053470, "dur":68, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.AppUI.Navigation-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1755294425053540, "dur":1032, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425057352, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425057528, "dur":356, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425058911, "dur":276, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425059223, "dur":538, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425061165, "dur":1017, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425062861, "dur":619, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425063490, "dur":1325, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425064826, "dur":269, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425065096, "dur":700, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":6, "ts":1755294425065829, "dur":211, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425067248, "dur":447, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425067705, "dur":277, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425068031, "dur":654, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425069959, "dur":623, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425071753, "dur":720, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425073279, "dur":579, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425073865, "dur":575, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425074473, "dur":474, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425074990, "dur":652, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425076766, "dur":265, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425077068, "dur":353, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425078168, "dur":160, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425078334, "dur":191, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425078569, "dur":782, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425080679, "dur":894, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425082863, "dur":414, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425083285, "dur":295, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425083590, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425083719, "dur":349, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425084076, "dur":484, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425084579, "dur":61, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Domain_Reload-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1755294425084641, "dur":433, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425086178, "dur":475, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755294425087999, "dur":26669, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers.assets" }}
,{ "pid":12345, "tid":6, "ts":1755294425114778, "dur":133645, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294424935555, "dur":9523, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294424945085, "dur":698, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Algorithms.dll" }}
,{ "pid":12345, "tid":7, "ts":1755294424945783, "dur":586, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Claims.dll" }}
,{ "pid":12345, "tid":7, "ts":1755294424946369, "dur":546, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.AccessControl.dll" }}
,{ "pid":12345, "tid":7, "ts":1755294424946915, "dur":529, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Xml.dll" }}
,{ "pid":12345, "tid":7, "ts":1755294424947444, "dur":585, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Primitives.dll" }}
,{ "pid":12345, "tid":7, "ts":1755294424948029, "dur":588, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Json.dll" }}
,{ "pid":12345, "tid":7, "ts":1755294424948617, "dur":562, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Formatters.dll" }}
,{ "pid":12345, "tid":7, "ts":1755294424949179, "dur":663, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":7, "ts":1755294424949843, "dur":564, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Numerics.dll" }}
,{ "pid":12345, "tid":7, "ts":1755294424950407, "dur":708, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Loader.dll" }}
,{ "pid":12345, "tid":7, "ts":1755294424951115, "dur":677, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Intrinsics.dll" }}
,{ "pid":12345, "tid":7, "ts":1755294424951792, "dur":531, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.RuntimeInformation.dll" }}
,{ "pid":12345, "tid":7, "ts":1755294424952323, "dur":517, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.JavaScript.dll" }}
,{ "pid":12345, "tid":7, "ts":1755294424952840, "dur":537, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.dll" }}
,{ "pid":12345, "tid":7, "ts":1755294424953377, "dur":559, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Handles.dll" }}
,{ "pid":12345, "tid":7, "ts":1755294424953936, "dur":527, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Extensions.dll" }}
,{ "pid":12345, "tid":7, "ts":1755294424954463, "dur":605, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.dll" }}
,{ "pid":12345, "tid":7, "ts":1755294424955069, "dur":557, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.CompilerServices.VisualC.dll" }}
,{ "pid":12345, "tid":7, "ts":1755294424955626, "dur":576, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.CompilerServices.Unsafe.dll" }}
,{ "pid":12345, "tid":7, "ts":1755294424956202, "dur":544, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.Writer.dll" }}
,{ "pid":12345, "tid":7, "ts":1755294424945085, "dur":11661, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294424956746, "dur":2934, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294424959681, "dur":4543, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294424964224, "dur":3350, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294424967574, "dur":3426, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294424971006, "dur":609, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294424971621, "dur":787, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294424972417, "dur":149, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294424972575, "dur":602, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294424973194, "dur":568, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294424973773, "dur":493, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294424974271, "dur":615, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294424974892, "dur":621, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294424975519, "dur":603, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294424976128, "dur":552, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294424976688, "dur":575, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294424977274, "dur":580, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294424977859, "dur":599, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294424978463, "dur":618, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294424979086, "dur":567, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294424979659, "dur":696, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294424980379, "dur":129, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/web.config" }}
,{ "pid":12345, "tid":7, "ts":1755294424980509, "dur":166, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294424980681, "dur":129, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/web.config" }}
,{ "pid":12345, "tid":7, "ts":1755294424980811, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294424980982, "dur":129, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Plugins/ARM64/AppUINativePlugin.dll" }}
,{ "pid":12345, "tid":7, "ts":1755294424981112, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294424990029, "dur":63361, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.resource" }}
,{ "pid":12345, "tid":7, "ts":1755294425053557, "dur":1023, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294425056772, "dur":282, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294425057063, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294425057195, "dur":1586, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294425058791, "dur":252, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294425059076, "dur":641, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294425060894, "dur":765, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294425062757, "dur":473, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294425063239, "dur":909, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294425064185, "dur":675, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294425067642, "dur":451, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294425068135, "dur":717, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294425070041, "dur":648, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294425071871, "dur":674, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294425073312, "dur":557, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294425073875, "dur":589, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294425074471, "dur":473, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294425074979, "dur":712, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294425076860, "dur":317, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294425077182, "dur":309, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294425077499, "dur":374, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294425077919, "dur":496, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294425079685, "dur":767, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294425082323, "dur":654, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294425083969, "dur":497, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294425085518, "dur":619, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755294425087268, "dur":10768, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level1" }}
,{ "pid":12345, "tid":7, "ts":1755294425098188, "dur":150225, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294424935588, "dur":9507, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294424945102, "dur":738, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebSockets.Client.dll" }}
,{ "pid":12345, "tid":8, "ts":1755294424945840, "dur":586, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebProxy.dll" }}
,{ "pid":12345, "tid":8, "ts":1755294424946426, "dur":548, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebHeaderCollection.dll" }}
,{ "pid":12345, "tid":8, "ts":1755294424946974, "dur":516, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebClient.dll" }}
,{ "pid":12345, "tid":8, "ts":1755294424947490, "dur":584, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Sockets.dll" }}
,{ "pid":12345, "tid":8, "ts":1755294424948074, "dur":578, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.ServicePoint.dll" }}
,{ "pid":12345, "tid":8, "ts":1755294424948652, "dur":550, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Security.dll" }}
,{ "pid":12345, "tid":8, "ts":1755294424949202, "dur":669, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Requests.dll" }}
,{ "pid":12345, "tid":8, "ts":1755294424949871, "dur":591, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Quic.dll" }}
,{ "pid":12345, "tid":8, "ts":1755294424950463, "dur":805, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Primitives.dll" }}
,{ "pid":12345, "tid":8, "ts":1755294424951268, "dur":581, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Ping.dll" }}
,{ "pid":12345, "tid":8, "ts":1755294424951849, "dur":539, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.NetworkInformation.dll" }}
,{ "pid":12345, "tid":8, "ts":1755294424952388, "dur":534, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.NameResolution.dll" }}
,{ "pid":12345, "tid":8, "ts":1755294424952922, "dur":519, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Mail.dll" }}
,{ "pid":12345, "tid":8, "ts":1755294424953441, "dur":624, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.HttpListener.dll" }}
,{ "pid":12345, "tid":8, "ts":1755294424954065, "dur":549, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Http.Json.dll" }}
,{ "pid":12345, "tid":8, "ts":1755294424954614, "dur":619, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Http.dll" }}
,{ "pid":12345, "tid":8, "ts":1755294424955233, "dur":538, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.dll" }}
,{ "pid":12345, "tid":8, "ts":1755294424955772, "dur":617, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Memory.dll" }}
,{ "pid":12345, "tid":8, "ts":1755294424956389, "dur":589, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Queryable.dll" }}
,{ "pid":12345, "tid":8, "ts":1755294424945102, "dur":11876, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":517, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.WebPages.dll" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":519, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Routing.dll" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":7904, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":3294, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":2759, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":633, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":855, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":608, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":710, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":671, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294424974505, "dur":599, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294424975107, "dur":555, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294424975692, "dur":549, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294424976247, "dur":567, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294424976819, "dur":552, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294424977376, "dur":568, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294424977949, "dur":531, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294424978486, "dur":586, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294424979078, "dur":532, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294424979616, "dur":643, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294424980266, "dur":347, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/machine.config" }}
,{ "pid":12345, "tid":8, "ts":1755294424980614, "dur":302, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294424980921, "dur":140, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/EmbedRuntime/MonoPosixHelper.dll" }}
,{ "pid":12345, "tid":8, "ts":1755294424981061, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294424981208, "dur":120, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME.exe" }}
,{ "pid":12345, "tid":8, "ts":1755294424981329, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294425024325, "dur":43362, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.assets" }}
,{ "pid":12345, "tid":8, "ts":1755294425067824, "dur":340, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294425068211, "dur":680, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294425070350, "dur":689, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294425072229, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294425072364, "dur":163, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294425072558, "dur":259, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294425072844, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294425073000, "dur":264, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294425073271, "dur":630, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294425073912, "dur":529, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294425074441, "dur":60, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Burst.dll" }}
,{ "pid":12345, "tid":8, "ts":1755294425074505, "dur":546, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294425075095, "dur":747, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294425076849, "dur":296, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294425077154, "dur":290, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294425077452, "dur":367, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294425077855, "dur":447, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294425079707, "dur":1310, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294425082749, "dur":294, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294425083049, "dur":91, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Runtime.Serialization-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1755294425083140, "dur":822, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294425084841, "dur":692, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294425086678, "dur":890, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755294425088277, "dur":160125, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294424935576, "dur":9511, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294424945094, "dur":682, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.ResourceManager.dll" }}
,{ "pid":12345, "tid":9, "ts":1755294424945776, "dur":579, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.Reader.dll" }}
,{ "pid":12345, "tid":9, "ts":1755294424946355, "dur":524, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.TypeExtensions.dll" }}
,{ "pid":12345, "tid":9, "ts":1755294424946879, "dur":547, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Primitives.dll" }}
,{ "pid":12345, "tid":9, "ts":1755294424947426, "dur":655, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Metadata.dll" }}
,{ "pid":12345, "tid":9, "ts":1755294424948082, "dur":573, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Extensions.dll" }}
,{ "pid":12345, "tid":9, "ts":1755294424948655, "dur":542, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.Lightweight.dll" }}
,{ "pid":12345, "tid":9, "ts":1755294424949198, "dur":659, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.ILGeneration.dll" }}
,{ "pid":12345, "tid":9, "ts":1755294424949857, "dur":573, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.dll" }}
,{ "pid":12345, "tid":9, "ts":1755294424950430, "dur":791, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.dll" }}
,{ "pid":12345, "tid":9, "ts":1755294424951221, "dur":593, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.DispatchProxy.dll" }}
,{ "pid":12345, "tid":9, "ts":1755294424951814, "dur":567, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Xml.Linq.dll" }}
,{ "pid":12345, "tid":9, "ts":1755294424952382, "dur":546, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Xml.dll" }}
,{ "pid":12345, "tid":9, "ts":1755294424952929, "dur":524, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Uri.dll" }}
,{ "pid":12345, "tid":9, "ts":1755294424953454, "dur":613, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.DataContractSerialization.dll" }}
,{ "pid":12345, "tid":9, "ts":1755294424954067, "dur":567, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.CoreLib.dll" }}
,{ "pid":12345, "tid":9, "ts":1755294424954634, "dur":652, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ObjectModel.dll" }}
,{ "pid":12345, "tid":9, "ts":1755294424955287, "dur":535, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Numerics.Vectors.dll" }}
,{ "pid":12345, "tid":9, "ts":1755294424955823, "dur":591, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Numerics.dll" }}
,{ "pid":12345, "tid":9, "ts":1755294424956415, "dur":603, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebSockets.dll" }}
,{ "pid":12345, "tid":9, "ts":1755294424945094, "dur":11924, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294424957658, "dur":566, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Interfaces.dll" }}
,{ "pid":12345, "tid":9, "ts":1755294424960648, "dur":566, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Net.Http.dll" }}
,{ "pid":12345, "tid":9, "ts":1755294424957018, "dur":8035, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294424965054, "dur":4628, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294424969682, "dur":1258, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294424970971, "dur":602, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294424971581, "dur":888, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294424972481, "dur":704, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294424973194, "dur":737, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294424973947, "dur":527, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294424974483, "dur":581, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294424975072, "dur":584, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294424975661, "dur":575, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294424976242, "dur":532, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294424976782, "dur":572, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294424977359, "dur":570, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294424977937, "dur":594, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294424978536, "dur":523, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294424979076, "dur":606, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294424979689, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294424979829, "dur":190, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/UnityPlayer.dll" }}
,{ "pid":12345, "tid":9, "ts":1755294424980020, "dur":285, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294424980310, "dur":319, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":9, "ts":1755294424980630, "dur":294, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294424980928, "dur":220, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/EmbedRuntime/mono-2.0-bdwgc.dll" }}
,{ "pid":12345, "tid":9, "ts":1755294424981148, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294424996637, "dur":57718, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.assets.resS" }}
,{ "pid":12345, "tid":9, "ts":1755294425054558, "dur":163, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.InputForUIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1755294425054721, "dur":3709, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294425060228, "dur":786, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294425061845, "dur":2538, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294425064420, "dur":1509, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294425067225, "dur":168, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294425067403, "dur":303, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294425067739, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294425069304, "dur":546, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294425071226, "dur":575, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294425072945, "dur":183, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294425073137, "dur":554, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294425073697, "dur":646, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294425074353, "dur":481, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294425074868, "dur":824, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294425076773, "dur":278, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294425077058, "dur":158, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294425077226, "dur":396, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294425077627, "dur":50, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ImageConversionModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1755294425077677, "dur":511, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294425079619, "dur":621, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294425082883, "dur":352, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294425083245, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294425083364, "dur":419, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294425083792, "dur":590, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294425084419, "dur":409, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294425084829, "dur":54, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Mono.Security-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1755294425086220, "dur":511, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755294425088229, "dur":160176, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294424935626, "dur":9479, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294424945113, "dur":686, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Parallel.dll" }}
,{ "pid":12345, "tid":10, "ts":1755294424945800, "dur":612, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Expressions.dll" }}
,{ "pid":12345, "tid":10, "ts":1755294424946412, "dur":550, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.dll" }}
,{ "pid":12345, "tid":10, "ts":1755294424946962, "dur":529, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.UnmanagedMemoryStream.dll" }}
,{ "pid":12345, "tid":10, "ts":1755294424947492, "dur":638, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Pipes.dll" }}
,{ "pid":12345, "tid":10, "ts":1755294424948130, "dur":587, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Pipes.AccessControl.dll" }}
,{ "pid":12345, "tid":10, "ts":1755294424948718, "dur":700, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.MemoryMappedFiles.dll" }}
,{ "pid":12345, "tid":10, "ts":1755294424949418, "dur":520, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.IsolatedStorage.dll" }}
,{ "pid":12345, "tid":10, "ts":1755294424949939, "dur":572, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.Watcher.dll" }}
,{ "pid":12345, "tid":10, "ts":1755294424950511, "dur":839, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.Primitives.dll" }}
,{ "pid":12345, "tid":10, "ts":1755294424951350, "dur":533, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.DriveInfo.dll" }}
,{ "pid":12345, "tid":10, "ts":1755294424951884, "dur":536, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.dll" }}
,{ "pid":12345, "tid":10, "ts":1755294424952421, "dur":530, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.AccessControl.dll" }}
,{ "pid":12345, "tid":10, "ts":1755294424952951, "dur":531, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.dll" }}
,{ "pid":12345, "tid":10, "ts":1755294424953482, "dur":609, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.ZipFile.dll" }}
,{ "pid":12345, "tid":10, "ts":1755294424954091, "dur":572, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.Native.dll" }}
,{ "pid":12345, "tid":10, "ts":1755294424954663, "dur":631, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":10, "ts":1755294424955294, "dur":541, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.dll" }}
,{ "pid":12345, "tid":10, "ts":1755294424955835, "dur":599, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.Brotli.dll" }}
,{ "pid":12345, "tid":10, "ts":1755294424956434, "dur":579, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.Extensions.dll" }}
,{ "pid":12345, "tid":10, "ts":1755294424945112, "dur":11901, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294424957731, "dur":531, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.dll" }}
,{ "pid":12345, "tid":10, "ts":1755294424959689, "dur":547, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.Activation.dll" }}
,{ "pid":12345, "tid":10, "ts":1755294424957014, "dur":8061, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294424965075, "dur":1793, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294424966869, "dur":4070, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294424970967, "dur":620, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294424971624, "dur":768, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294424972402, "dur":433, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294424972843, "dur":534, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294424973385, "dur":465, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294424973859, "dur":528, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294424974395, "dur":554, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294424974954, "dur":597, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294424975557, "dur":572, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294424976134, "dur":567, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294424976709, "dur":598, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294424977312, "dur":716, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294424978036, "dur":526, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294424978567, "dur":553, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294424979126, "dur":655, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294424979788, "dur":228, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/D3D12/D3D12Core.dll" }}
,{ "pid":12345, "tid":10, "ts":1755294424980017, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294424980177, "dur":294, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/settings.map" }}
,{ "pid":12345, "tid":10, "ts":1755294424980471, "dur":422, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294424980900, "dur":209, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":10, "ts":1755294424981110, "dur":216, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294424981331, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425029186, "dur":23498, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level1.resS" }}
,{ "pid":12345, "tid":10, "ts":1755294425052882, "dur":1136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425056469, "dur":490, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425057853, "dur":275, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425059056, "dur":249, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425059322, "dur":80, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Mathematics-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1755294425059402, "dur":456, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425061592, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425061724, "dur":746, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425062481, "dur":697, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425063189, "dur":611, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425063843, "dur":160, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425066294, "dur":514, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425067463, "dur":319, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425067785, "dur":52, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Autodesk.Fbx.BuildTestAssets-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1755294425067838, "dur":523, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425069612, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425069775, "dur":669, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425071710, "dur":662, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425073253, "dur":626, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425073887, "dur":560, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425074447, "dur":66, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.AppUI.dll" }}
,{ "pid":12345, "tid":10, "ts":1755294425074548, "dur":197, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425076158, "dur":564, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425077406, "dur":294, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425077745, "dur":497, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425079347, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425079524, "dur":566, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425081846, "dur":669, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425083425, "dur":248, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425083679, "dur":392, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425084079, "dur":439, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425084553, "dur":400, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425086125, "dur":504, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755294425087927, "dur":15075, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers.assets.resS" }}
,{ "pid":12345, "tid":10, "ts":1755294425103139, "dur":145275, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294424935662, "dur":9458, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294424945127, "dur":678, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.Common.dll" }}
,{ "pid":12345, "tid":11, "ts":1755294424945805, "dur":611, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Core.dll" }}
,{ "pid":12345, "tid":11, "ts":1755294424946416, "dur":544, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Console.dll" }}
,{ "pid":12345, "tid":11, "ts":1755294424946960, "dur":528, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Configuration.dll" }}
,{ "pid":12345, "tid":11, "ts":1755294424947488, "dur":619, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.TypeConverter.dll" }}
,{ "pid":12345, "tid":11, "ts":1755294424948107, "dur":610, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.Primitives.dll" }}
,{ "pid":12345, "tid":11, "ts":1755294424948717, "dur":699, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.EventBasedAsync.dll" }}
,{ "pid":12345, "tid":11, "ts":1755294424949416, "dur":520, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.dll" }}
,{ "pid":12345, "tid":11, "ts":1755294424949937, "dur":573, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.DataAnnotations.dll" }}
,{ "pid":12345, "tid":11, "ts":1755294424950511, "dur":845, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.Annotations.dll" }}
,{ "pid":12345, "tid":11, "ts":1755294424951356, "dur":533, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Specialized.dll" }}
,{ "pid":12345, "tid":11, "ts":1755294424951889, "dur":532, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.NonGeneric.dll" }}
,{ "pid":12345, "tid":11, "ts":1755294424952421, "dur":529, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Immutable.dll" }}
,{ "pid":12345, "tid":11, "ts":1755294424952951, "dur":521, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.dll" }}
,{ "pid":12345, "tid":11, "ts":1755294424953472, "dur":600, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Concurrent.dll" }}
,{ "pid":12345, "tid":11, "ts":1755294424954072, "dur":563, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Buffers.dll" }}
,{ "pid":12345, "tid":11, "ts":1755294424954635, "dur":633, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.AppContext.dll" }}
,{ "pid":12345, "tid":11, "ts":1755294424955268, "dur":564, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\SharpYaml.dll" }}
,{ "pid":12345, "tid":11, "ts":1755294424955833, "dur":634, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\NiceIO.dll" }}
,{ "pid":12345, "tid":11, "ts":1755294424956467, "dur":601, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Newtonsoft.Json.dll" }}
,{ "pid":12345, "tid":11, "ts":1755294424945127, "dur":11942, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294424957842, "dur":534, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\PEAPI.dll" }}
,{ "pid":12345, "tid":11, "ts":1755294424959617, "dur":610, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Tasklets.dll" }}
,{ "pid":12345, "tid":11, "ts":1755294424957069, "dur":8000, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294424965623, "dur":527, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.CrashReportingModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1755294424965069, "dur":4255, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294424969324, "dur":1617, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294424970959, "dur":502, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294424971470, "dur":825, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294424972315, "dur":162, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294424972489, "dur":593, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294424973088, "dur":683, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294424973777, "dur":525, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294424974308, "dur":559, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294424974876, "dur":612, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294424975495, "dur":581, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294424976082, "dur":526, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294424976613, "dur":574, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294424977196, "dur":594, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294424977796, "dur":584, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294424978386, "dur":585, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294424978977, "dur":568, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294424979551, "dur":510, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294424980065, "dur":216, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/browscap.ini" }}
,{ "pid":12345, "tid":11, "ts":1755294424980282, "dur":303, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294424980590, "dur":124, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":11, "ts":1755294424980715, "dur":231, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294424980951, "dur":158, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Resources/unity default resources" }}
,{ "pid":12345, "tid":11, "ts":1755294424981109, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294424983076, "dur":70233, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets1.assets" }}
,{ "pid":12345, "tid":11, "ts":1755294425053515, "dur":493, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294425055952, "dur":783, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294425057732, "dur":579, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294425059498, "dur":552, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294425061704, "dur":587, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294425062302, "dur":1865, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294425064212, "dur":460, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294425066860, "dur":439, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294425067935, "dur":508, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294425069660, "dur":597, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294425071576, "dur":615, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294425073119, "dur":549, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294425073690, "dur":509, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294425074236, "dur":479, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294425074753, "dur":590, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294425076432, "dur":472, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294425077605, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294425078778, "dur":791, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294425080857, "dur":1278, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294425083281, "dur":613, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294425084880, "dur":577, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294425086617, "dur":676, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755294425088209, "dur":160214, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294424935690, "dur":9438, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294424945128, "dur":690, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\netstandard.dll" }}
,{ "pid":12345, "tid":12, "ts":1755294424945818, "dur":629, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\msquic.dll" }}
,{ "pid":12345, "tid":12, "ts":1755294424946447, "dur":567, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscorrc.dll" }}
,{ "pid":12345, "tid":12, "ts":1755294424947014, "dur":542, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscorlib.dll" }}
,{ "pid":12345, "tid":12, "ts":1755294424947556, "dur":623, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordbi.dll" }}
,{ "pid":12345, "tid":12, "ts":1755294424948179, "dur":555, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordaccore_amd64_amd64_8.0.424.16909.dll" }}
,{ "pid":12345, "tid":12, "ts":1755294424948735, "dur":692, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordaccore.dll" }}
,{ "pid":12345, "tid":12, "ts":1755294424949427, "dur":533, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\monolinker.dll" }}
,{ "pid":12345, "tid":12, "ts":1755294424949961, "dur":560, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Rocks.dll" }}
,{ "pid":12345, "tid":12, "ts":1755294424950521, "dur":838, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Pdb.dll" }}
,{ "pid":12345, "tid":12, "ts":1755294424951359, "dur":550, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Mdb.dll" }}
,{ "pid":12345, "tid":12, "ts":1755294424951910, "dur":528, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.dll" }}
,{ "pid":12345, "tid":12, "ts":1755294424952438, "dur":539, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Win32.Registry.dll" }}
,{ "pid":12345, "tid":12, "ts":1755294424952977, "dur":523, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Win32.Primitives.dll" }}
,{ "pid":12345, "tid":12, "ts":1755294424953501, "dur":601, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.VisualBasic.dll" }}
,{ "pid":12345, "tid":12, "ts":1755294424954102, "dur":565, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.VisualBasic.Core.dll" }}
,{ "pid":12345, "tid":12, "ts":1755294424954667, "dur":646, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.DiaSymReader.Native.amd64.dll" }}
,{ "pid":12345, "tid":12, "ts":1755294424955313, "dur":564, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.CSharp.dll" }}
,{ "pid":12345, "tid":12, "ts":1755294424955877, "dur":589, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Bcl.HashCode.dll" }}
,{ "pid":12345, "tid":12, "ts":1755294424956466, "dur":613, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp.exe" }}
,{ "pid":12345, "tid":12, "ts":1755294424945128, "dur":11951, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294424957802, "dur":593, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.CSharp.dll" }}
,{ "pid":12345, "tid":12, "ts":1755294424959548, "dur":553, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.VisualC.dll" }}
,{ "pid":12345, "tid":12, "ts":1755294424957079, "dur":7954, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294424965611, "dur":526, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.VirtualTexturingModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1755294424965034, "dur":4627, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294424969661, "dur":1343, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294424971010, "dur":597, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294424971646, "dur":762, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294424972417, "dur":559, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294424972982, "dur":623, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294424973612, "dur":419, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294424974040, "dur":588, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294424974635, "dur":561, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294424975203, "dur":584, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294424975793, "dur":553, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294424976352, "dur":516, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294424976875, "dur":544, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294424977425, "dur":530, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294424977966, "dur":569, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294424978541, "dur":563, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294424979109, "dur":566, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294424979681, "dur":659, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294424980382, "dur":299, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/settings.map" }}
,{ "pid":12345, "tid":12, "ts":1755294424980681, "dur":270, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294424980955, "dur":184, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Plugins/x86_64/AppUINativePlugin.dll" }}
,{ "pid":12345, "tid":12, "ts":1755294424981140, "dur":154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294424981301, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425019243, "dur":36952, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.assets.resS" }}
,{ "pid":12345, "tid":12, "ts":1755294425056356, "dur":868, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425057929, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425058074, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425058221, "dur":254, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425058482, "dur":207, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425058697, "dur":432, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425059137, "dur":229, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425059402, "dur":566, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425061299, "dur":622, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425062752, "dur":499, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425063257, "dur":1025, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425064334, "dur":2276, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425067408, "dur":354, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425067802, "dur":420, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425069516, "dur":642, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425071360, "dur":586, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425073025, "dur":220, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425073255, "dur":607, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425073869, "dur":630, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425074505, "dur":416, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425074960, "dur":623, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425076673, "dur":343, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425077643, "dur":428, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425079251, "dur":609, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425081172, "dur":1359, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425083813, "dur":289, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425084112, "dur":581, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425084732, "dur":614, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425086541, "dur":686, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755294425088144, "dur":10192, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers" }}
,{ "pid":12345, "tid":12, "ts":1755294425098505, "dur":149915, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755294425260471, "dur":2126, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 284300, "tid": 79301, "ts": 1755294425266740, "dur": 18, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 284300, "tid": 79301, "ts": 1755294425266925, "dur": 5731, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 284300, "tid": 79301, "ts": 1755294425264087, "dur": 8610, "ph": "X", "name": "Write chrome-trace events", "args": {} },
