{"Bee.Core.BuildProgramContext+BuildProgramContextOutputData": {"MaxRerunAllowed": 2147483647}, "PlayerBuildProgramLibrary.Data.BuiltFilesOutput": {"Files": ["C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers", "C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers.assets", "C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers.assets.resS", "C:/Unity/Builds/NLAME/BLAME_Data/level0", "C:/Unity/Builds/NLAME/BLAME_Data/level1", "C:/Unity/Builds/NLAME/BLAME_Data/level1.resS", "C:/Unity/Builds/NLAME/BLAME_Data/resources.assets", "C:/Unity/Builds/NLAME/BLAME_Data/resources.assets.resS", "C:/Unity/Builds/NLAME/BLAME_Data/resources.resource", "C:/Unity/Builds/NLAME/BLAME_Data/Resources/unity_builtin_extra", "C:/Unity/Builds/NLAME/BLAME_Data/RuntimeInitializeOnLoads.json", "C:/Unity/Builds/NLAME/BLAME_Data/ScriptingAssemblies.json", "C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.assets", "C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.assets.resS", "C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.resource", "C:/Unity/Builds/NLAME/BLAME_Data/sharedassets1.assets", "C:/Unity/Builds/NLAME/BLAME_Data/sharedassets1.assets.resS", "C:/Unity/Builds/NLAME/BLAME_Data/boot.config", "C:/Unity/Builds/NLAME/BLAME_Data/Plugins/ARM64/AppUINativePlugin.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Plugins/x86_64/AppUINativePlugin.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Assembly-CSharp.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Autodesk.Fbx.BuildTestAssets.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Autodesk.Fbx.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/BakeryRuntimeAssembly.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Domain_Reload.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Mono.Security.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/mscorlib.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/netstandard.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Newtonsoft.Json.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.ComponentModel.Composition.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Configuration.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Core.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Data.DataSetExtensions.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Data.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Drawing.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.EnterpriseServices.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.IO.Compression.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.IO.Compression.FileSystem.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.IO.Hashing.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Memory.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Net.Http.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Numerics.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Runtime.CompilerServices.Unsafe.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Runtime.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Runtime.Serialization.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Security.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.ServiceModel.Internals.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Transactions.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Xml.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Xml.Linq.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Tayx.Graphy.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.2D.Common.Runtime.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.AppUI.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.AppUI.InternalAPIBridge.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.AppUI.MVVM.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.AppUI.Navigation.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.AppUI.Redux.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.AppUI.Undo.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Burst.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Burst.Unsafe.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Collections.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Collections.LowLevel.ILSupport.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Formats.Fbx.Runtime.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.InferenceEngine.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.InternalAPIEngineBridge.001.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Mathematics.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Multiplayer.Center.Common.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.ProBuilder.Csg.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.ProBuilder.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.ProBuilder.KdTree.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.ProBuilder.Poly2Tri.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.ProBuilder.Stl.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.ProGrids.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Recorder.Base.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Recorder.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Rendering.LightTransport.Runtime.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.Core.Runtime.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.Core.Runtime.Shared.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.Core.Samples.Runtime.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.Core.ShaderLibrary.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.GPUDriven.Runtime.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.HighDefinition.Runtime.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Serialization.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.TextMeshPro.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Timeline.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.VisualEffectGraph.Runtime.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.VisualScripting.Antlr3.Runtime.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.VisualScripting.Core.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.VisualScripting.Flow.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.VisualScripting.State.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.AccessibilityModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.AIModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.AMDModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.AndroidJNIModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.AnimationModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.ARModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.AssetBundleModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.AudioModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.ClothModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.ClusterInputModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.ClusterRendererModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.ContentLoadModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.CoreModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.CrashReportingModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.DirectorModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.DSPGraphModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.GameCenterModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.GIModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.GraphicsStateCollectionSerializerModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.GridModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.HierarchyCoreModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.HotReloadModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.IdentifiersModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.ImageConversionModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.IMGUIModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.InputForUIModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.InputLegacyModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.InputModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.InsightsModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.JSONSerializeModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.LocalizationModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.MarshallingModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.MultiplayerModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.NVIDIAModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.ParticleSystemModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.PerformanceReportingModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.Physics2DModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.PhysicsModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.PropertiesModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.ScreenCaptureModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.ShaderVariantAnalyticsModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.SharedInternalsModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.SpriteMaskModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.SpriteShapeModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.StreamingModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.SubstanceModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.SubsystemsModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.TerrainModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.TerrainPhysicsModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.TextCoreFontEngineModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.TextCoreTextEngineModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.TextRenderingModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.TilemapModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.TLSModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UI.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UIElementsModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UIModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UmbraModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UnityAnalyticsCommonModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UnityAnalyticsModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UnityConnectModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UnityConsentModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UnityCurlModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UnityTestProtocolModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UnityWebRequestAudioModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UnityWebRequestModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UnityWebRequestTextureModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UnityWebRequestWWWModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.VehiclesModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.VFXModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.VideoModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.VirtualTexturingModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.VRModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.WindModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.XRModule.dll", "C:/Unity/Builds/NLAME/BLAME_Data/Plugins/x86_64/lib_burst_generated.dll", "C:/Unity/Builds/NLAME/BLAME_Data/app.info", "C:/Unity/Builds/NLAME/BLAME_Data/Resources/unity default resources", "C:/Unity/Builds/NLAME/BLAME.exe", "C:/Unity/Builds/NLAME/MonoBleedingEdge/EmbedRuntime/mono-2.0-bdwgc.dll", "C:/Unity/Builds/NLAME/MonoBleedingEdge/EmbedRuntime/MonoPosixHelper.dll", "C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/Browsers/Compat.browser", "C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx", "C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/machine.config", "C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/settings.map", "C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/web.config", "C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/Browsers/Compat.browser", "C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/DefaultWsdlHelpGenerator.aspx", "C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/machine.config", "C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/settings.map", "C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/web.config", "C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/Browsers/Compat.browser", "C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/DefaultWsdlHelpGenerator.aspx", "C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/machine.config", "C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/settings.map", "C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/web.config", "C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/browscap.ini", "C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/config", "C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/mconfig/config.xml", "C:/Unity/Builds/NLAME/UnityCrashHandler64.exe", "C:/Unity/Builds/NLAME/UnityPlayer.dll", "C:/Unity/Builds/NLAME/D3D12/D3D12Core.dll", "C:/Unity/Builds/NLAME/DirectML.dll"], "BootConfigArtifact": "Library/Bee/artifacts/WinPlayerBuildProgram/boot.config"}}