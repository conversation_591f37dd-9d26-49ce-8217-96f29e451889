using UnityEngine;
using System;
using UnityEngine.SceneManagement;
using KinematicCharacterController.FPS;

public class SettingsVideoManager : MonoBehaviour
{
    private static SettingsVideoManager _instance;
    public static SettingsVideoManager Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = FindAnyObjectByType<SettingsVideoManager>();
                if (_instance == null)
                {
                    var go = new GameObject("SettingsVideoManager");
                    _instance = go.AddComponent<SettingsVideoManager>();
                    DontDestroyOnLoad(go);
                }
            }
            return _instance;
        }
    }

    public event Action<float> OnFOVChanged;
    public event Action<int> OnFPSLimitChanged;
    public event Action<int> OnBackgroundFPSLimitChanged;

    private bool _isSubscribedToSceneLoaded = false;

    [Header("Video Settings")]
    [SerializeField] private float _defaultFOV = 90f;
    [SerializeField] private float _minFOV = 60f;
    [SerializeField] private float _maxFOV = 110f;

    private int _fpsLimit = -1;
    private int _backgroundFpsLimit = 30;

    private float _fieldOfView;
    public float FieldOfView
    {
        get => _fieldOfView;
        set
        {
            float newValue = Mathf.Clamp(value, _minFOV, _maxFOV);
            if (_fieldOfView != newValue)
            {
                _fieldOfView = newValue;
                SaveFOVSetting();
                OnFOVChanged?.Invoke(_fieldOfView);
                Debug.Log($"Field of view changed to: {_fieldOfView}");
            }
        }
    }

    public float MinFOV => _minFOV;
    public float MaxFOV => _maxFOV;

    public int FPSLimit
    {
        get => _fpsLimit;
        set
        {
            if (_fpsLimit != value)
            {
                _fpsLimit = value;
                SaveFPSLimitSetting();
                ApplyFPSLimitSettings();
                OnFPSLimitChanged?.Invoke(_fpsLimit);
                Debug.Log($"FPS limit changed to: {_fpsLimit}");
            }
        }
    }

    public int BackgroundFPSLimit
    {
        get => _backgroundFpsLimit;
        set
        {
            if (_backgroundFpsLimit != value)
            {
                _backgroundFpsLimit = value;
                SaveBackgroundFPSLimitSetting();
                ApplyFPSLimitSettings();
                OnBackgroundFPSLimitChanged?.Invoke(_backgroundFpsLimit);
                Debug.Log($"Background FPS limit changed to: {_backgroundFpsLimit}");
            }
        }
    }

    public int[] FPSLimitOptions => new int[] { 30, 60, 120, 144, 165, 240, -1 };

    private void Awake()
    {
        if (_instance != null && _instance != this)
        {
            Debug.LogWarning($"Multiple SettingsVideoManager instances found. Destroying duplicate on {gameObject.name}");
            Destroy(gameObject);
            return;
        }

        _instance = this;
        DontDestroyOnLoad(gameObject);

        Debug.Log("SettingsVideoManager initialized - Loading settings");
        LoadSettings();
    }

    private void Start()
    {
        ApplySettings();
    }

    private void OnEnable()
    {
        if (!_isSubscribedToSceneLoaded)
        {
            SceneManager.sceneLoaded += HandleSceneLoaded;
            _isSubscribedToSceneLoaded = true;
        }
    }

    private void OnDisable()
    {
        if (_isSubscribedToSceneLoaded)
        {
            SceneManager.sceneLoaded -= HandleSceneLoaded;
            _isSubscribedToSceneLoaded = false;
        }
    }

    private void HandleSceneLoaded(Scene scene, LoadSceneMode mode)
    {
        // Ensure FOV and FPS limits are applied after any scene change
        ApplySettings();
    }

    private void OnApplicationFocus(bool hasFocus)
    {
        if (hasFocus)
        {
            if (_fpsLimit > 0)
            {
                Application.targetFrameRate = _fpsLimit;
                Debug.Log($"Focus gained: Applied foreground FPS limit: {_fpsLimit}");
            }
            else
            {
                Application.targetFrameRate = -1;
                Debug.Log("Focus gained: Applied unlimited FPS");
            }

            Time.fixedDeltaTime = 0.02f;
        }
        else
        {
            if (_backgroundFpsLimit <= 30)
            {
                Application.targetFrameRate = _backgroundFpsLimit;
                Time.fixedDeltaTime = 0.033f;
                Debug.Log($"Focus lost: Applied background FPS limit: {_backgroundFpsLimit}");
            }
            else
            {
                Application.targetFrameRate = _backgroundFpsLimit;
                Time.fixedDeltaTime = 0.016f;
                Debug.Log($"Focus lost: Applied background FPS limit: {_backgroundFpsLimit}");
            }
        }
    }

    private void OnApplicationPause(bool isPaused)
    {
        OnApplicationFocus(!isPaused);
    }

    private void LoadSettings()
    {
        _fpsLimit = PlayerPrefs.GetInt("SettingsFPSLimit", -1);
        _backgroundFpsLimit = PlayerPrefs.GetInt("SettingsBackgroundFPSLimit", 30);
        _fieldOfView = PlayerPrefs.GetFloat("SettingsFOV", _defaultFOV);

        Debug.Log("Loaded all video settings from PlayerPrefs");
    }

    private void SaveFOVSetting()
    {
        PlayerPrefs.SetFloat("SettingsFOV", _fieldOfView);
        PlayerPrefs.Save();
    }

    private void SaveFPSLimitSetting()
    {
        PlayerPrefs.SetInt("SettingsFPSLimit", _fpsLimit);
        PlayerPrefs.Save();
    }

    private void SaveBackgroundFPSLimitSetting()
    {
        PlayerPrefs.SetInt("SettingsBackgroundFPSLimit", _backgroundFpsLimit);
        PlayerPrefs.Save();
    }

    private void ApplyFPSLimitSettings()
    {
        if (Application.isFocused)
        {
            if (_fpsLimit > 0)
            {
                Application.targetFrameRate = _fpsLimit;
                Debug.Log($"Applied FPS limit: {_fpsLimit}");
            }
            else
            {
                Application.targetFrameRate = -1;
                Debug.Log("Applied unlimited FPS");
            }
        }
        else
        {
            Application.targetFrameRate = _backgroundFpsLimit;
            Debug.Log($"Applied background FPS limit: {_backgroundFpsLimit} (application not focused)");
        }

        Application.runInBackground = true;

        if (_backgroundFpsLimit <= 30)
        {
            QualitySettings.vSyncCount = 0;
            Application.backgroundLoadingPriority = ThreadPriority.Low;
            QualitySettings.maxQueuedFrames = 2;

            if (!Application.isFocused)
            {
                Time.fixedDeltaTime = 0.033f;
            }
        }
        else
        {
            Application.backgroundLoadingPriority = ThreadPriority.Normal;
            QualitySettings.maxQueuedFrames = 3;

            if (!Application.isFocused)
            {
                Time.fixedDeltaTime = 0.016f;
            }
        }

        Debug.Log($"Configured background FPS settings: {_backgroundFpsLimit}");
    }

    public void ApplySettings()
    {
        ApplyFPSLimitSettings();

        Debug.Log("Applying FOV settings to all cameras");
        var fpsCameras = FindObjectsByType<FPSCharacterCamera>(FindObjectsSortMode.None);
        if (fpsCameras.Length == 0)
        {
            Debug.LogWarning("No FPSCharacterCamera found to apply settings");
        }

        foreach (var fpsCamera in fpsCameras)
        {
            if (fpsCamera != null && fpsCamera.Camera != null)
            {
                fpsCamera.Camera.fieldOfView = _fieldOfView;
                Debug.Log($"Applied FOV {_fieldOfView} to camera on {fpsCamera.gameObject.name}");
            }
        }
    }

    public void ResetToDefaults()
    {
        _fieldOfView = _defaultFOV;
        SaveFOVSetting();
        OnFOVChanged?.Invoke(_fieldOfView);

        _fpsLimit = -1;
        SaveFPSLimitSetting();
        OnFPSLimitChanged?.Invoke(_fpsLimit);

        _backgroundFpsLimit = 30;
        SaveBackgroundFPSLimitSetting();
        OnBackgroundFPSLimitChanged?.Invoke(_backgroundFpsLimit);

        ApplySettings();

        Debug.Log("Reset all video settings to defaults");
    }
}
