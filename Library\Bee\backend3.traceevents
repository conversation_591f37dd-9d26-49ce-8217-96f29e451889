{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1755290514861154, "dur":87361, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755290514948521, "dur":364, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755290514948913, "dur":63, "ph":"X", "name": "Tundra",  "args": { "detail":"PrepareNodes" }}
,{ "pid":12345, "tid":0, "ts":1755290514948976, "dur":526, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755290514950688, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Drawing.dll" }}
,{ "pid":12345, "tid":0, "ts":1755290514953993, "dur":100, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Autodesk.Fbx.BuildTestAssets-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1755290514954192, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/BakeryRuntimeAssembly-FeaturesChecked.txt_gew5.info" }}
,{ "pid":12345, "tid":0, "ts":1755290514954661, "dur":74, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System.Configuration-FeaturesChecked.txt_nqsf.info" }}
,{ "pid":12345, "tid":0, "ts":1755290514955592, "dur":86, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System.Runtime.CompilerServices.Unsafe-FeaturesChecked.txt_mtva.info" }}
,{ "pid":12345, "tid":0, "ts":1755290514956052, "dur":75, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Xml.Linq-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1755290514956213, "dur":73, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.2D.Common.Runtime-FeaturesChecked.txt_2jk0.info" }}
,{ "pid":12345, "tid":0, "ts":1755290514956447, "dur":199, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.AppUI.InternalAPIBridge-FeaturesChecked.txt_26vz.info" }}
,{ "pid":12345, "tid":0, "ts":1755290514957217, "dur":84, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Burst-FeaturesChecked.txt_9564.info" }}
,{ "pid":12345, "tid":0, "ts":1755290514958279, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Recorder-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1755290514958632, "dur":56, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.Core.ShaderLibrary-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1755290514961225, "dur":136, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.MultiplayerModule-FeaturesChecked.txt_d35b.info" }}
,{ "pid":12345, "tid":0, "ts":1755290514962554, "dur":179, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TilemapModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1755290514962770, "dur":181, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TLSModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1755290514963129, "dur":104, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UIModule-FeaturesChecked.txt_rlwj.info" }}
,{ "pid":12345, "tid":0, "ts":1755290514963318, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1755290514963412, "dur":119, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityAnalyticsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1755290514964611, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.VirtualTexturingModule-FeaturesChecked.txt_s30u.info" }}
,{ "pid":12345, "tid":0, "ts":1755290514965308, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/web.config" }}
,{ "pid":12345, "tid":0, "ts":1755290514949539, "dur":16209, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755290514965755, "dur":75636, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755290515041392, "dur":178, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755290515041791, "dur":5503, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1755290514949655, "dur":16108, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755290514972225, "dur":535, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"../../Editors/6000.2.0f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1755290514972761, "dur":445, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Assets/StreamingAssets" }}
,{ "pid":12345, "tid":1, "ts":1755290514973206, "dur":1711, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.0f1/Editor/Data/il2cpp/build/deploy" }}
,{ "pid":12345, "tid":1, "ts":1755290514974918, "dur":15317, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.0f1/Editor/Data/il2cpp/libil2cpp" }}
,{ "pid":12345, "tid":1, "ts":1755290514990236, "dur":310, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport" }}
,{ "pid":12345, "tid":1, "ts":1755290514990546, "dur":2988, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono" }}
,{ "pid":12345, "tid":1, "ts":1755290514993534, "dur":187, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/Data/Resources" }}
,{ "pid":12345, "tid":1, "ts":1755290514993721, "dur":288, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.0f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1755290514994009, "dur":273, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Library/PlayerDataCache/Win642/Data" }}
,{ "pid":12345, "tid":1, "ts":1755290514994282, "dur":63, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Temp/StagingArea/Data/Plugins" }}
,{ "pid":12345, "tid":1, "ts":1755290514994345, "dur":55, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Temp/StagingArea/Data/UnitySubsystems" }}
,{ "pid":12345, "tid":1, "ts":1755290514965770, "dur":28646, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755290514994428, "dur":74, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/7893594715672875865.rsp" }}
,{ "pid":12345, "tid":1, "ts":1755290514994503, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755290514994653, "dur":1500, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"UnityLinker C:/Unity/BLAME/BLAME/Library/Bee/artifacts/unitylinker_dwek.traceevents" }}
,{ "pid":12345, "tid":1, "ts":1755290514996154, "dur":16140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755290515014145, "dur":12621, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"GenerateNativePluginsForAssemblies Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker" }}
,{ "pid":12345, "tid":1, "ts":1755290515026767, "dur":318, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755290515028189, "dur":112, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Plugins/x86_64/lib_burst_generated.dll" }}
,{ "pid":12345, "tid":1, "ts":1755290515028302, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755290515028442, "dur":638, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755290515030104, "dur":288, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755290515030401, "dur":577, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755290515030984, "dur":406, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755290515031429, "dur":582, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755290515033298, "dur":411, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755290515033745, "dur":335, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755290515035260, "dur":532, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755290515036964, "dur":431, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755290515038212, "dur":845, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755290515040312, "dur":455, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755290515041341, "dur":54, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290514949686, "dur":16097, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290514965791, "dur":592, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.WindowsDesktop.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290514966383, "dur":676, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.WebGL.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290514967059, "dur":589, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.VisionOS.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290514967648, "dur":600, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.UniversalWindows.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290514968248, "dur":500, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.MacOSX.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290514968749, "dur":563, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.Linux.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290514969313, "dur":629, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.iOS.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290514969942, "dur":571, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.EmbeddedLinux.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290514970513, "dur":567, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290514971080, "dur":655, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.AppleTV.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290514971735, "dur":672, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.Android.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290514972407, "dur":685, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.Output.xml" }}
,{ "pid":12345, "tid":2, "ts":1755290514973093, "dur":654, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.Output.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290514973747, "dur":1002, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290514974749, "dur":505, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Cecil.Awesome.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290514975254, "dur":576, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Api.Attributes.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290514975830, "dur":666, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XPath.XDocument.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290514976496, "dur":713, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XPath.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290514977210, "dur":593, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XmlSerializer.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290514977804, "dur":747, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XmlDocument.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290514965791, "dur":12761, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290514980130, "dur":516, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\cscompmgd.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290514983130, "dur":518, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290514984136, "dur":985, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Workflow.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290514985122, "dur":603, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Workflow.ComponentModel.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290514986042, "dur":530, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Windows.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290514978552, "dur":8291, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290514986844, "dur":4389, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290514991234, "dur":3248, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290514994487, "dur":669, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290514995161, "dur":561, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290514995733, "dur":905, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290514996648, "dur":550, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290514997207, "dur":530, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290514997743, "dur":630, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290514998377, "dur":559, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290514998941, "dur":791, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290514999748, "dur":750, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515000503, "dur":718, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515001229, "dur":628, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515001864, "dur":555, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515002428, "dur":820, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515003270, "dur":302, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515003576, "dur":357, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":2, "ts":1755290515003934, "dur":241, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515004182, "dur":260, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/EmbedRuntime/MonoPosixHelper.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290515004443, "dur":234, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515004684, "dur":466, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515005158, "dur":458, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515005619, "dur":8533, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515014157, "dur":615, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.InferenceEngine-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1755290515014773, "dur":531, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515015305, "dur":62, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.InferenceEngine-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1755290515016269, "dur":369, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.InputLegacyModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1755290515016638, "dur":613, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515018311, "dur":359, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.InputForUIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1755290515018670, "dur":220, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515019777, "dur":106, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515019888, "dur":111, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.AudioModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290515020004, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515020111, "dur":130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290515020242, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515020376, "dur":127, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.InputModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290515020504, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515020655, "dur":427, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.GraphicsStateCollectionSerializerModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290515021083, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515021215, "dur":229, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.TextMeshPro-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1755290515021444, "dur":546, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515023089, "dur":518, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515023643, "dur":352, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515025169, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515025306, "dur":161, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UIModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1755290515025468, "dur":264, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515025768, "dur":809, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515027735, "dur":607, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515029776, "dur":207, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515030020, "dur":350, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515030403, "dur":570, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515030981, "dur":422, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515031441, "dur":641, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515033368, "dur":403, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515033809, "dur":398, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515035312, "dur":590, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515036992, "dur":224, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515037224, "dur":205, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515037438, "dur":269, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515037714, "dur":354, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515038105, "dur":301, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515039795, "dur":586, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755290515041152, "dur":249, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290514949678, "dur":16094, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290514966241, "dur":607, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\WindowsBase.dll" }}
,{ "pid":12345, "tid":3, "ts":1755290514966849, "dur":651, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.runtimeconfig.json" }}
,{ "pid":12345, "tid":3, "ts":1755290514967500, "dur":515, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.exe" }}
,{ "pid":12345, "tid":3, "ts":1755290514968413, "dur":682, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.dll" }}
,{ "pid":12345, "tid":3, "ts":1755290514969095, "dur":848, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.deps.json" }}
,{ "pid":12345, "tid":3, "ts":1755290514969943, "dur":621, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.TinyProfiler.dll" }}
,{ "pid":12345, "tid":3, "ts":1755290514970564, "dur":625, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Options.dll" }}
,{ "pid":12345, "tid":3, "ts":1755290514971189, "dur":604, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.Output.xml" }}
,{ "pid":12345, "tid":3, "ts":1755290514971793, "dur":648, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.Output.dll" }}
,{ "pid":12345, "tid":3, "ts":1755290514972441, "dur":668, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.dll" }}
,{ "pid":12345, "tid":3, "ts":1755290514973109, "dur":665, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Shell.dll" }}
,{ "pid":12345, "tid":3, "ts":1755290514973774, "dur":992, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Compile.dll" }}
,{ "pid":12345, "tid":3, "ts":1755290514975254, "dur":559, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Common.dll" }}
,{ "pid":12345, "tid":3, "ts":1755290514975813, "dur":547, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.dll" }}
,{ "pid":12345, "tid":3, "ts":1755290514976361, "dur":731, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.Data.dll" }}
,{ "pid":12345, "tid":3, "ts":1755290514965782, "dur":11310, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290514977093, "dur":545, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp.dll.config" }}
,{ "pid":12345, "tid":3, "ts":1755290514977638, "dur":742, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp-compile.runtimeconfig.json" }}
,{ "pid":12345, "tid":3, "ts":1755290514977093, "dur":4618, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290514983952, "dur":550, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Security.Principal.dll" }}
,{ "pid":12345, "tid":3, "ts":1755290514984524, "dur":641, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Security.Principal.Windows.dll" }}
,{ "pid":12345, "tid":3, "ts":1755290514981711, "dur":5842, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290514987553, "dur":2216, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290514989770, "dur":4652, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290514994448, "dur":615, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290514995077, "dur":561, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290514995642, "dur":702, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290514996356, "dur":576, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290514996951, "dur":602, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290514997561, "dur":640, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290514998208, "dur":616, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290514998831, "dur":590, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290514999429, "dur":711, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515000150, "dur":882, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515001037, "dur":556, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515001597, "dur":552, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515002155, "dur":775, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515002940, "dur":560, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515003504, "dur":379, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/settings.map" }}
,{ "pid":12345, "tid":3, "ts":1755290515003883, "dur":190, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515004078, "dur":541, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":3, "ts":1755290515004620, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515004742, "dur":499, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515005250, "dur":374, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515005626, "dur":8531, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515014158, "dur":821, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.IO.Compression-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1755290515014980, "dur":378, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515016290, "dur":213, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515017245, "dur":242, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.AppUI.Navigation-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1755290515017488, "dur":635, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515018880, "dur":207, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.PerformanceReportingModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1755290515019087, "dur":182, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515019276, "dur":220, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1755290515019497, "dur":484, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515021048, "dur":344, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Mathematics-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1755290515021393, "dur":544, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515022962, "dur":490, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515023488, "dur":205, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515024841, "dur":541, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515026194, "dur":712, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515028109, "dur":641, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515030072, "dur":296, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515030375, "dur":524, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515030908, "dur":456, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515031400, "dur":663, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515033406, "dur":422, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515033878, "dur":328, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515035271, "dur":537, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515036947, "dur":201, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515037158, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515037298, "dur":249, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515037556, "dur":168, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515037760, "dur":216, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515039184, "dur":583, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755290515040847, "dur":551, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290514949764, "dur":16028, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290514965801, "dur":611, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XDocument.dll" }}
,{ "pid":12345, "tid":4, "ts":1755290514966412, "dur":632, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.Serialization.dll" }}
,{ "pid":12345, "tid":4, "ts":1755290514967044, "dur":594, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.ReaderWriter.dll" }}
,{ "pid":12345, "tid":4, "ts":1755290514967638, "dur":566, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":4, "ts":1755290514968609, "dur":511, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Windows.dll" }}
,{ "pid":12345, "tid":4, "ts":1755290514969121, "dur":700, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Web.HttpUtility.dll" }}
,{ "pid":12345, "tid":4, "ts":1755290514969822, "dur":520, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Web.dll" }}
,{ "pid":12345, "tid":4, "ts":1755290514970342, "dur":549, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ValueTuple.dll" }}
,{ "pid":12345, "tid":4, "ts":1755290514970892, "dur":678, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Transactions.Local.dll" }}
,{ "pid":12345, "tid":4, "ts":1755290514971570, "dur":613, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Transactions.dll" }}
,{ "pid":12345, "tid":4, "ts":1755290514972183, "dur":541, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Timer.dll" }}
,{ "pid":12345, "tid":4, "ts":1755290514972724, "dur":666, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.ThreadPool.dll" }}
,{ "pid":12345, "tid":4, "ts":1755290514973390, "dur":829, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Thread.dll" }}
,{ "pid":12345, "tid":4, "ts":1755290514974220, "dur":669, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Parallel.dll" }}
,{ "pid":12345, "tid":4, "ts":1755290514974889, "dur":604, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Extensions.dll" }}
,{ "pid":12345, "tid":4, "ts":1755290514975493, "dur":638, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.dll" }}
,{ "pid":12345, "tid":4, "ts":1755290514976132, "dur":709, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Dataflow.dll" }}
,{ "pid":12345, "tid":4, "ts":1755290514976841, "dur":589, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Overlapped.dll" }}
,{ "pid":12345, "tid":4, "ts":1755290514977430, "dur":692, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.dll" }}
,{ "pid":12345, "tid":4, "ts":1755290514965800, "dur":12322, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290514978123, "dur":547, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Bee.Toolchain.TvOS.dll" }}
,{ "pid":12345, "tid":4, "ts":1755290514978123, "dur":3444, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290514981568, "dur":5095, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290514986664, "dur":4049, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290514990714, "dur":3770, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290514994490, "dur":638, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290514995134, "dur":542, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290514995685, "dur":179, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290514995873, "dur":804, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290514996687, "dur":567, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290514997261, "dur":549, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290514997815, "dur":587, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290514998409, "dur":561, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290514998975, "dur":610, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290514999591, "dur":630, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515000226, "dur":683, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515000915, "dur":586, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515001507, "dur":578, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515002094, "dur":534, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515002662, "dur":649, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515003316, "dur":157, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/D3D12/D3D12Core.dll" }}
,{ "pid":12345, "tid":4, "ts":1755290515003473, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515003595, "dur":149, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":4, "ts":1755290515003745, "dur":236, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515004005, "dur":329, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/web.config" }}
,{ "pid":12345, "tid":4, "ts":1755290515004335, "dur":156, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515004498, "dur":123, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Resources/unity default resources" }}
,{ "pid":12345, "tid":4, "ts":1755290515004622, "dur":149, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515004777, "dur":530, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515005312, "dur":326, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515005642, "dur":8499, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515014143, "dur":549, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.DirectorModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1755290515014693, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515014820, "dur":208, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Data.DataSetExtensions-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1755290515015029, "dur":341, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515016250, "dur":131, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1755290515016381, "dur":173, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515016558, "dur":335, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.WindModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1755290515016894, "dur":545, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515018561, "dur":429, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UnityWebRequestAudioModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1755290515018990, "dur":165, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515019159, "dur":184, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.InternalAPIEngineBridge.001-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1755290515019344, "dur":509, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515020943, "dur":405, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.Core.ShaderLibrary-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1755290515021348, "dur":540, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515022948, "dur":494, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515023452, "dur":347, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515023836, "dur":659, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515025446, "dur":275, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515025762, "dur":806, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515027722, "dur":559, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515029490, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515029608, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515029750, "dur":213, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515030004, "dur":263, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515030276, "dur":412, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515030695, "dur":552, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515031283, "dur":544, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515033051, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515033226, "dur":264, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515034199, "dur":286, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515034522, "dur":593, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515036292, "dur":541, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515037779, "dur":223, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515039134, "dur":597, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755290515040851, "dur":554, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290514949797, "dur":16003, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290514965807, "dur":675, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Channels.dll" }}
,{ "pid":12345, "tid":5, "ts":1755290514966483, "dur":609, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.RegularExpressions.dll" }}
,{ "pid":12345, "tid":5, "ts":1755290514967093, "dur":551, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Json.dll" }}
,{ "pid":12345, "tid":5, "ts":1755290514967645, "dur":610, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encodings.Web.dll" }}
,{ "pid":12345, "tid":5, "ts":1755290514969200, "dur":689, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.CodePages.dll" }}
,{ "pid":12345, "tid":5, "ts":1755290514969890, "dur":540, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ServiceProcess.dll" }}
,{ "pid":12345, "tid":5, "ts":1755290514970430, "dur":514, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ServiceModel.Web.dll" }}
,{ "pid":12345, "tid":5, "ts":1755290514970944, "dur":692, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.SecureString.dll" }}
,{ "pid":12345, "tid":5, "ts":1755290514971636, "dur":656, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Principal.Windows.dll" }}
,{ "pid":12345, "tid":5, "ts":1755290514972292, "dur":523, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Principal.dll" }}
,{ "pid":12345, "tid":5, "ts":1755290514972816, "dur":713, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.dll" }}
,{ "pid":12345, "tid":5, "ts":1755290514973529, "dur":1006, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.X509Certificates.dll" }}
,{ "pid":12345, "tid":5, "ts":1755290514974980, "dur":582, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.OpenSsl.dll" }}
,{ "pid":12345, "tid":5, "ts":1755290514975562, "dur":574, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Encoding.dll" }}
,{ "pid":12345, "tid":5, "ts":1755290514976136, "dur":717, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.dll" }}
,{ "pid":12345, "tid":5, "ts":1755290514976853, "dur":591, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Csp.dll" }}
,{ "pid":12345, "tid":5, "ts":1755290514977444, "dur":749, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Cng.dll" }}
,{ "pid":12345, "tid":5, "ts":1755290514965807, "dur":12386, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290514978194, "dur":510, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Analytics.deps.json" }}
,{ "pid":12345, "tid":5, "ts":1755290514978193, "dur":3535, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290514981728, "dur":4911, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290514986640, "dur":3799, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290514990439, "dur":3992, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290514994450, "dur":615, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290514995076, "dur":588, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290514995664, "dur":68, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System.Data.DataSetExtensions-FeaturesChecked.txt_pams.info" }}
,{ "pid":12345, "tid":5, "ts":1755290514995734, "dur":726, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290514996465, "dur":478, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290514996955, "dur":698, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290514997661, "dur":634, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290514998300, "dur":555, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290514998865, "dur":621, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290514999491, "dur":650, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515000156, "dur":905, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515001066, "dur":572, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515001647, "dur":512, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515002164, "dur":582, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515002752, "dur":614, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515003371, "dur":191, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/UnityPlayer.dll" }}
,{ "pid":12345, "tid":5, "ts":1755290515003562, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515003703, "dur":1076, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/settings.map" }}
,{ "pid":12345, "tid":5, "ts":1755290515004779, "dur":156, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515004943, "dur":567, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515005516, "dur":8622, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515014139, "dur":850, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.VehiclesModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1755290515014990, "dur":287, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515015282, "dur":288, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.CrashReportingModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1755290515015571, "dur":116, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515015695, "dur":226, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UnityAnalyticsModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1755290515015922, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515016097, "dur":214, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1755290515016312, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515016447, "dur":346, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.MarshallingModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1755290515016793, "dur":577, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515018515, "dur":484, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AndroidJNIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1755290515019000, "dur":392, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515020433, "dur":401, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1755290515020835, "dur":215, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515021894, "dur":62, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ClusterInputModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1755290515021957, "dur":537, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515023152, "dur":522, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515023678, "dur":126, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.VirtualTexturingModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1755290515023805, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515023953, "dur":613, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515025477, "dur":498, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515027366, "dur":532, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515029176, "dur":493, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515030419, "dur":560, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515030986, "dur":400, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515031426, "dur":577, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515033314, "dur":395, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515033749, "dur":387, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515035232, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515035394, "dur":587, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515037066, "dur":370, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515038167, "dur":789, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515040123, "dur":505, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755290515041256, "dur":144, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290514949825, "dur":15982, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290514965815, "dur":687, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Algorithms.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514966503, "dur":630, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Claims.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514967134, "dur":574, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.AccessControl.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514967708, "dur":617, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Xml.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514968325, "dur":649, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Primitives.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514968975, "dur":563, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Json.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514969538, "dur":619, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Formatters.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514970157, "dur":505, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514970662, "dur":621, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Numerics.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514971284, "dur":595, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Loader.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514971879, "dur":686, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Intrinsics.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514972565, "dur":636, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.RuntimeInformation.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514973201, "dur":671, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.JavaScript.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514973873, "dur":1037, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514974910, "dur":575, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Handles.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514975486, "dur":616, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Extensions.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514976102, "dur":739, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514976841, "dur":588, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.CompilerServices.VisualC.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514977430, "dur":715, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.CompilerServices.Unsafe.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514978145, "dur":552, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.Writer.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514965815, "dur":12882, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290514980253, "dur":663, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.DirectoryServices.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514981384, "dur":548, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Design.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514983918, "dur":627, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.Linq.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514984545, "dur":545, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.Entity.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514985090, "dur":515, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.DataSetExtensions.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514978698, "dur":8319, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290514987865, "dur":539, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.TextCoreFontEngineModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514989198, "dur":503, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.SubsystemsModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290514987017, "dur":5578, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290514992596, "dur":1831, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290514994445, "dur":639, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290514995089, "dur":538, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290514995639, "dur":771, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290514996430, "dur":642, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290514997085, "dur":647, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290514997736, "dur":612, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290514998354, "dur":533, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290514998895, "dur":681, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290514999584, "dur":614, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515000204, "dur":682, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515000894, "dur":574, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515001476, "dur":604, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515002090, "dur":571, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515002667, "dur":592, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515003266, "dur":407, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515003681, "dur":703, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/web.config" }}
,{ "pid":12345, "tid":6, "ts":1755290515004384, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515004542, "dur":236, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Plugins/ARM64/AppUINativePlugin.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290515004779, "dur":188, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515004972, "dur":590, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515005568, "dur":8615, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515014185, "dur":587, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.VFXModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1755290515014773, "dur":571, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515016324, "dur":354, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ContentLoadModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1755290515016679, "dur":659, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515018479, "dur":447, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.GameCenterModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1755290515018930, "dur":406, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515020306, "dur":149, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.ClothModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290515020456, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515020594, "dur":371, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.LocalizationModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1755290515020966, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515021139, "dur":314, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515022510, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515022645, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515022783, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515022916, "dur":279, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515023201, "dur":457, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515023699, "dur":487, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515025288, "dur":148, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515025472, "dur":460, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515027328, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515027488, "dur":588, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515029373, "dur":416, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515030472, "dur":584, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515031096, "dur":235, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515032712, "dur":554, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515033720, "dur":357, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515035269, "dur":565, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515037006, "dur":244, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515037259, "dur":220, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515037486, "dur":214, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515037707, "dur":232, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515037956, "dur":285, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515038277, "dur":833, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755290515040364, "dur":1025, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290514949842, "dur":15973, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290514965821, "dur":663, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.ResourceManager.dll" }}
,{ "pid":12345, "tid":7, "ts":1755290514966485, "dur":646, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.Reader.dll" }}
,{ "pid":12345, "tid":7, "ts":1755290514967131, "dur":546, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.TypeExtensions.dll" }}
,{ "pid":12345, "tid":7, "ts":1755290514967677, "dur":708, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Primitives.dll" }}
,{ "pid":12345, "tid":7, "ts":1755290514968385, "dur":610, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Metadata.dll" }}
,{ "pid":12345, "tid":7, "ts":1755290514968996, "dur":682, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Extensions.dll" }}
,{ "pid":12345, "tid":7, "ts":1755290514969678, "dur":543, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.Lightweight.dll" }}
,{ "pid":12345, "tid":7, "ts":1755290514970221, "dur":510, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.ILGeneration.dll" }}
,{ "pid":12345, "tid":7, "ts":1755290514970731, "dur":713, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.dll" }}
,{ "pid":12345, "tid":7, "ts":1755290514971444, "dur":633, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.dll" }}
,{ "pid":12345, "tid":7, "ts":1755290514972077, "dur":594, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.DispatchProxy.dll" }}
,{ "pid":12345, "tid":7, "ts":1755290514972671, "dur":608, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Xml.Linq.dll" }}
,{ "pid":12345, "tid":7, "ts":1755290514973280, "dur":763, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Xml.dll" }}
,{ "pid":12345, "tid":7, "ts":1755290514974043, "dur":781, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Uri.dll" }}
,{ "pid":12345, "tid":7, "ts":1755290514974824, "dur":597, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.DataContractSerialization.dll" }}
,{ "pid":12345, "tid":7, "ts":1755290514975421, "dur":641, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.CoreLib.dll" }}
,{ "pid":12345, "tid":7, "ts":1755290514976062, "dur":710, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ObjectModel.dll" }}
,{ "pid":12345, "tid":7, "ts":1755290514976772, "dur":610, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Numerics.Vectors.dll" }}
,{ "pid":12345, "tid":7, "ts":1755290514977382, "dur":594, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Numerics.dll" }}
,{ "pid":12345, "tid":7, "ts":1755290514977976, "dur":639, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebSockets.dll" }}
,{ "pid":12345, "tid":7, "ts":1755290514965821, "dur":12795, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290514980607, "dur":503, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.Internals.dll" }}
,{ "pid":12345, "tid":7, "ts":1755290514984050, "dur":601, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Runtime.Caching.dll" }}
,{ "pid":12345, "tid":7, "ts":1755290514984652, "dur":567, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reflection.Context.dll" }}
,{ "pid":12345, "tid":7, "ts":1755290514978616, "dur":8191, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290514986807, "dur":3479, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290514990287, "dur":4133, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290514994454, "dur":619, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290514995080, "dur":583, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290514995668, "dur":784, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290514996464, "dur":625, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290514997095, "dur":604, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290514997704, "dur":635, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290514998348, "dur":599, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290514998953, "dur":643, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290514999602, "dur":682, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515000289, "dur":696, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515000990, "dur":521, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515001517, "dur":669, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515002191, "dur":562, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515002758, "dur":770, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515003533, "dur":283, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/machine.config" }}
,{ "pid":12345, "tid":7, "ts":1755290515003817, "dur":207, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515004028, "dur":493, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/machine.config" }}
,{ "pid":12345, "tid":7, "ts":1755290515004521, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515004641, "dur":314, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515004961, "dur":586, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515005552, "dur":8583, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515014136, "dur":785, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":7, "ts":1755290515014922, "dur":339, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515015267, "dur":314, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1755290515015581, "dur":394, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515016521, "dur":355, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.IdentifiersModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1755290515016876, "dur":603, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515018549, "dur":471, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.SpriteMaskModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1755290515019021, "dur":454, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515020629, "dur":389, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515021471, "dur":524, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515023021, "dur":509, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515023568, "dur":346, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515025118, "dur":199, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.XRModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1755290515025318, "dur":190, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515025546, "dur":532, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515027424, "dur":636, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515029364, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.VisualScripting.State-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1755290515029419, "dur":452, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515030492, "dur":616, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515031143, "dur":282, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515032709, "dur":53, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Runtime.CompilerServices.Unsafe-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1755290515032763, "dur":576, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515033847, "dur":464, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515035374, "dur":608, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515037052, "dur":403, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515038201, "dur":813, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515040266, "dur":471, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755290515041329, "dur":83, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290514949886, "dur":15997, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290514965889, "dur":628, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebSockets.Client.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290514966518, "dur":687, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebProxy.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290514967205, "dur":522, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebHeaderCollection.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290514967727, "dur":650, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebClient.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290514968378, "dur":660, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Sockets.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290514969038, "dur":836, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.ServicePoint.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290514969875, "dur":608, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Security.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290514970483, "dur":572, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Requests.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290514971055, "dur":662, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Quic.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290514971717, "dur":662, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Primitives.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290514972379, "dur":622, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Ping.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290514973001, "dur":651, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.NetworkInformation.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290514973652, "dur":1008, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.NameResolution.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290514975049, "dur":597, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.HttpListener.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290514975646, "dur":575, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Http.Json.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290514976222, "dur":699, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Http.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290514976922, "dur":578, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290514977501, "dur":747, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Memory.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290514965889, "dur":12817, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290514984082, "dur":886, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Options.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290514978707, "dur":8261, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290514987849, "dur":541, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.VehiclesModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290514989200, "dur":517, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.UnityWebRequestTextureModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290514986969, "dur":5617, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290514992586, "dur":1850, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290514994449, "dur":699, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290514995152, "dur":570, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290514995736, "dur":728, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290514996471, "dur":513, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290514996989, "dur":624, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290514997619, "dur":820, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290514998444, "dur":530, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290514998980, "dur":597, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290514999578, "dur":107, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.GraphicsStateCollectionSerializerModule-FeaturesChecked.txt_teqg.info" }}
,{ "pid":12345, "tid":8, "ts":1755290514999688, "dur":733, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515000434, "dur":529, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515000970, "dur":521, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515001497, "dur":602, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515002105, "dur":663, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515002777, "dur":644, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515003426, "dur":417, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/config" }}
,{ "pid":12345, "tid":8, "ts":1755290515003843, "dur":173, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515004021, "dur":684, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/settings.map" }}
,{ "pid":12345, "tid":8, "ts":1755290515004705, "dur":158, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515004879, "dur":538, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515005424, "dur":8713, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515014138, "dur":648, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.DSPGraphModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290515014787, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515014937, "dur":146, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290515015084, "dur":219, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515015323, "dur":261, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290515015585, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515015728, "dur":196, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.TerrainPhysicsModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290515015925, "dur":211, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515016174, "dur":296, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515017276, "dur":238, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.GridModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1755290515017514, "dur":666, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515018695, "dur":358, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290515019054, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515019253, "dur":439, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515020760, "dur":410, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":8, "ts":1755290515021170, "dur":154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515021363, "dur":534, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515022934, "dur":432, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515023376, "dur":391, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515023774, "dur":53, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Assembly-CSharp-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1755290515023835, "dur":614, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515025384, "dur":315, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515025740, "dur":621, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515027632, "dur":550, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515029708, "dur":216, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515029931, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515030061, "dur":317, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515030406, "dur":607, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515031020, "dur":376, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515031435, "dur":636, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515033335, "dur":546, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515033890, "dur":207, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515034104, "dur":319, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515034462, "dur":598, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515036258, "dur":545, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515037695, "dur":244, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515037948, "dur":328, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515038311, "dur":809, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755290515040381, "dur":1009, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290514949909, "dur":16065, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290514966009, "dur":508, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Parallel.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290514966517, "dur":624, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Expressions.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290514967141, "dur":556, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290514967697, "dur":604, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.UnmanagedMemoryStream.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290514968301, "dur":723, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Pipes.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290514969024, "dur":582, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Pipes.AccessControl.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290514969607, "dur":724, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.MemoryMappedFiles.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290514970829, "dur":666, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.Watcher.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290514971496, "dur":655, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.Primitives.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290514972151, "dur":553, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.DriveInfo.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290514972705, "dur":651, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290514973356, "dur":744, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.AccessControl.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290514974101, "dur":874, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290514974975, "dur":581, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.ZipFile.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290514975556, "dur":621, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.Native.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290514976178, "dur":741, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290514976919, "dur":579, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290514977499, "dur":741, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.Brotli.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290514966009, "dur":12699, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290514980280, "dur":529, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Btls.Interface.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290514982833, "dur":542, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.Build.Tasks.v4.0.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290514983867, "dur":666, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.Build.Engine.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290514984533, "dur":876, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\ICSharpCode.SharpZipLib.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290514978709, "dur":8450, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290514987478, "dur":692, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.DSPGraphModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290514987159, "dur":5612, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290514992771, "dur":1717, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290514994493, "dur":627, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290514995126, "dur":539, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290514995683, "dur":817, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290514996506, "dur":574, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290514997085, "dur":587, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290514997678, "dur":671, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290514998358, "dur":546, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290514998910, "dur":792, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290514999708, "dur":526, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515000240, "dur":737, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515000983, "dur":534, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515001554, "dur":648, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515002208, "dur":701, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515002937, "dur":548, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515003492, "dur":203, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/web.config" }}
,{ "pid":12345, "tid":9, "ts":1755290515003696, "dur":152, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515003855, "dur":920, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":9, "ts":1755290515004776, "dur":160, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515004950, "dur":610, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515005564, "dur":8579, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515014149, "dur":804, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/BakeryRuntimeAssembly-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1755290515014954, "dur":369, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515015957, "dur":182, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.ParticleSystemModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290515016139, "dur":158, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515016303, "dur":303, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.MultiplayerModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1755290515016607, "dur":596, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515018310, "dur":318, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.PropertiesModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1755290515018629, "dur":253, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515019711, "dur":137, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UmbraModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290515019848, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515019965, "dur":112, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.IMGUIModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290515020078, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515020196, "dur":122, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.VideoModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290515020319, "dur":243, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515020576, "dur":344, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.ClusterInputModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290515020921, "dur":159, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515021085, "dur":326, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Collections-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1755290515021411, "dur":569, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515023013, "dur":191, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.Serialization.dll" }}
,{ "pid":12345, "tid":9, "ts":1755290515023204, "dur":471, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515023684, "dur":161, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515023880, "dur":614, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515025473, "dur":474, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515027381, "dur":580, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515029292, "dur":648, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515030511, "dur":620, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515031166, "dur":319, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515032823, "dur":107, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515032937, "dur":161, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515033106, "dur":184, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515033297, "dur":376, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515033682, "dur":261, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515033949, "dur":253, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515034210, "dur":307, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515034555, "dur":613, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515036371, "dur":537, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515037816, "dur":392, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515038244, "dur":829, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755290515040348, "dur":483, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290514949937, "dur":16071, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290514966016, "dur":515, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.dll" }}
,{ "pid":12345, "tid":10, "ts":1755290514966531, "dur":675, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.Calendars.dll" }}
,{ "pid":12345, "tid":10, "ts":1755290514967206, "dur":530, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Formats.Tar.dll" }}
,{ "pid":12345, "tid":10, "ts":1755290514967736, "dur":597, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Formats.Asn1.dll" }}
,{ "pid":12345, "tid":10, "ts":1755290514968334, "dur":548, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Dynamic.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1755290514968882, "dur":558, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Drawing.Primitives.dll" }}
,{ "pid":12345, "tid":10, "ts":1755290514969441, "dur":591, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Drawing.dll" }}
,{ "pid":12345, "tid":10, "ts":1755290514970032, "dur":560, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.dll" }}
,{ "pid":12345, "tid":10, "ts":1755290514970592, "dur":676, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Tracing.dll" }}
,{ "pid":12345, "tid":10, "ts":1755290514971268, "dur":582, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.TraceSource.dll" }}
,{ "pid":12345, "tid":10, "ts":1755290514971850, "dur":660, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Tools.dll" }}
,{ "pid":12345, "tid":10, "ts":1755290514972510, "dur":667, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.TextWriterTraceListener.dll" }}
,{ "pid":12345, "tid":10, "ts":1755290514973177, "dur":655, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.StackTrace.dll" }}
,{ "pid":12345, "tid":10, "ts":1755290514973832, "dur":967, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Process.dll" }}
,{ "pid":12345, "tid":10, "ts":1755290514974799, "dur":623, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.FileVersionInfo.dll" }}
,{ "pid":12345, "tid":10, "ts":1755290514975422, "dur":629, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.DiagnosticSource.dll" }}
,{ "pid":12345, "tid":10, "ts":1755290514976051, "dur":660, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Debug.dll" }}
,{ "pid":12345, "tid":10, "ts":1755290514976712, "dur":638, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Contracts.dll" }}
,{ "pid":12345, "tid":10, "ts":1755290514977350, "dur":613, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.dll" }}
,{ "pid":12345, "tid":10, "ts":1755290514977963, "dur":644, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.DataSetExtensions.dll" }}
,{ "pid":12345, "tid":10, "ts":1755290514966016, "dur":12592, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290514984059, "dur":583, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Extensions.dll" }}
,{ "pid":12345, "tid":10, "ts":1755290514978608, "dur":7598, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290514986207, "dur":4279, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290514990487, "dur":3931, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290514994428, "dur":54, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/boot.config_tyr4.info" }}
,{ "pid":12345, "tid":10, "ts":1755290514994483, "dur":651, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290514995140, "dur":668, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"AddBootConfigGUID Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":10, "ts":1755290514995809, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290514995923, "dur":822, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290514996752, "dur":538, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290514997296, "dur":517, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290514997819, "dur":581, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290514998405, "dur":574, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290514998984, "dur":615, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290514999605, "dur":622, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515000232, "dur":692, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515000943, "dur":559, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515001510, "dur":651, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515002168, "dur":601, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515002769, "dur":88, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Burst-FeaturesChecked.txt_9564.info" }}
,{ "pid":12345, "tid":10, "ts":1755290515002859, "dur":530, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515003393, "dur":177, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/UnityCrashHandler64.exe" }}
,{ "pid":12345, "tid":10, "ts":1755290515003571, "dur":146, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515003721, "dur":613, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/machine.config" }}
,{ "pid":12345, "tid":10, "ts":1755290515004335, "dur":238, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515004577, "dur":117, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME.exe" }}
,{ "pid":12345, "tid":10, "ts":1755290515004695, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515004805, "dur":519, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515005327, "dur":8813, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515014140, "dur":769, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.InsightsModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1755290515014910, "dur":311, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515015227, "dur":261, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.NVIDIAModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1755290515015488, "dur":408, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515016538, "dur":330, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1755290515016869, "dur":523, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515018499, "dur":525, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.SubsystemsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1755290515019024, "dur":370, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515020562, "dur":320, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UnityConsentModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1755290515020883, "dur":214, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515021134, "dur":318, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515022595, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515022722, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515022863, "dur":107, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515022979, "dur":492, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515023506, "dur":263, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515024952, "dur":107, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515025069, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515025194, "dur":184, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515025421, "dur":409, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515027266, "dur":524, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515029023, "dur":510, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515030292, "dur":357, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515030658, "dur":561, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515031259, "dur":541, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515032950, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515033101, "dur":165, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515033273, "dur":317, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515033600, "dur":304, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515033911, "dur":278, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515034196, "dur":316, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515034551, "dur":610, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515036339, "dur":569, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515037835, "dur":387, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515038261, "dur":845, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755290515040421, "dur":981, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290514949968, "dur":16053, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290514966037, "dur":569, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.Common.dll" }}
,{ "pid":12345, "tid":11, "ts":1755290514966607, "dur":549, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Core.dll" }}
,{ "pid":12345, "tid":11, "ts":1755290514967157, "dur":582, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Console.dll" }}
,{ "pid":12345, "tid":11, "ts":1755290514967740, "dur":600, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Configuration.dll" }}
,{ "pid":12345, "tid":11, "ts":1755290514968340, "dur":559, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.TypeConverter.dll" }}
,{ "pid":12345, "tid":11, "ts":1755290514968899, "dur":537, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.Primitives.dll" }}
,{ "pid":12345, "tid":11, "ts":1755290514969436, "dur":573, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.EventBasedAsync.dll" }}
,{ "pid":12345, "tid":11, "ts":1755290514970009, "dur":556, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.dll" }}
,{ "pid":12345, "tid":11, "ts":1755290514970565, "dur":642, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.DataAnnotations.dll" }}
,{ "pid":12345, "tid":11, "ts":1755290514971207, "dur":633, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.Annotations.dll" }}
,{ "pid":12345, "tid":11, "ts":1755290514971840, "dur":659, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Specialized.dll" }}
,{ "pid":12345, "tid":11, "ts":1755290514972500, "dur":637, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.NonGeneric.dll" }}
,{ "pid":12345, "tid":11, "ts":1755290514973138, "dur":653, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Immutable.dll" }}
,{ "pid":12345, "tid":11, "ts":1755290514973791, "dur":982, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.dll" }}
,{ "pid":12345, "tid":11, "ts":1755290514974773, "dur":558, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Concurrent.dll" }}
,{ "pid":12345, "tid":11, "ts":1755290514975331, "dur":553, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Buffers.dll" }}
,{ "pid":12345, "tid":11, "ts":1755290514975884, "dur":647, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.AppContext.dll" }}
,{ "pid":12345, "tid":11, "ts":1755290514976531, "dur":706, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\SharpYaml.dll" }}
,{ "pid":12345, "tid":11, "ts":1755290514977238, "dur":530, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\NiceIO.dll" }}
,{ "pid":12345, "tid":11, "ts":1755290514977768, "dur":765, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Newtonsoft.Json.dll" }}
,{ "pid":12345, "tid":11, "ts":1755290514966037, "dur":12497, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290514978534, "dur":3319, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290514981853, "dur":5172, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290514987854, "dur":534, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.MultiplayerModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1755290514987026, "dur":5249, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290514992275, "dur":2158, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290514994452, "dur":628, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290514995090, "dur":609, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290514995703, "dur":732, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290514996441, "dur":523, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290514996974, "dur":623, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290514997603, "dur":635, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290514998246, "dur":494, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290514998747, "dur":510, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290514999266, "dur":743, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515000017, "dur":673, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515000698, "dur":276, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515000979, "dur":494, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515001479, "dur":595, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515002086, "dur":230, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Mathematics-FeaturesChecked.txt_rnku.info" }}
,{ "pid":12345, "tid":11, "ts":1755290515002317, "dur":520, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515002843, "dur":584, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515003433, "dur":441, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/browscap.ini" }}
,{ "pid":12345, "tid":11, "ts":1755290515003874, "dur":204, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515004082, "dur":134, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":11, "ts":1755290515004217, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515004343, "dur":189, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/DirectML.dll" }}
,{ "pid":12345, "tid":11, "ts":1755290515004533, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515004658, "dur":391, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515005055, "dur":539, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515005598, "dur":8589, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515014188, "dur":584, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AMDModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1755290515014773, "dur":537, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515015310, "dur":73, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AMDModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1755290515016381, "dur":338, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AssetBundleModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1755290515016720, "dur":582, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515018438, "dur":543, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UIElementsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1755290515018981, "dur":421, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515020449, "dur":216, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UnityConnectModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1755290515020666, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515020796, "dur":413, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":11, "ts":1755290515021210, "dur":152, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515021401, "dur":605, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515023033, "dur":546, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515023615, "dur":393, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515025196, "dur":255, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.VRModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1755290515025455, "dur":264, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515025758, "dur":622, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515027622, "dur":533, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515029389, "dur":173, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515029573, "dur":146, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515029725, "dur":241, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515030017, "dur":320, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515030347, "dur":550, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515030904, "dur":449, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515031397, "dur":570, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515033298, "dur":393, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515033698, "dur":221, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515033926, "dur":304, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515034236, "dur":290, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515034562, "dur":624, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515036355, "dur":546, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515037775, "dur":395, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515038182, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515038350, "dur":804, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755290515040373, "dur":1016, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290514949998, "dur":16035, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290514966034, "dur":597, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\netstandard.dll" }}
,{ "pid":12345, "tid":12, "ts":1755290514966632, "dur":517, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\msquic.dll" }}
,{ "pid":12345, "tid":12, "ts":1755290514967149, "dur":562, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscorrc.dll" }}
,{ "pid":12345, "tid":12, "ts":1755290514967711, "dur":655, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscorlib.dll" }}
,{ "pid":12345, "tid":12, "ts":1755290514968366, "dur":611, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordbi.dll" }}
,{ "pid":12345, "tid":12, "ts":1755290514968978, "dur":522, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordaccore_amd64_amd64_8.0.424.16909.dll" }}
,{ "pid":12345, "tid":12, "ts":1755290514969500, "dur":596, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordaccore.dll" }}
,{ "pid":12345, "tid":12, "ts":1755290514970096, "dur":608, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\monolinker.dll" }}
,{ "pid":12345, "tid":12, "ts":1755290514970704, "dur":664, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Rocks.dll" }}
,{ "pid":12345, "tid":12, "ts":1755290514971369, "dur":589, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Pdb.dll" }}
,{ "pid":12345, "tid":12, "ts":1755290514971958, "dur":629, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Mdb.dll" }}
,{ "pid":12345, "tid":12, "ts":1755290514972588, "dur":661, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.dll" }}
,{ "pid":12345, "tid":12, "ts":1755290514973249, "dur":687, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Win32.Registry.dll" }}
,{ "pid":12345, "tid":12, "ts":1755290514973936, "dur":900, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Win32.Primitives.dll" }}
,{ "pid":12345, "tid":12, "ts":1755290514974836, "dur":589, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.VisualBasic.dll" }}
,{ "pid":12345, "tid":12, "ts":1755290514975426, "dur":637, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.VisualBasic.Core.dll" }}
,{ "pid":12345, "tid":12, "ts":1755290514976064, "dur":681, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.DiaSymReader.Native.amd64.dll" }}
,{ "pid":12345, "tid":12, "ts":1755290514976745, "dur":629, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.CSharp.dll" }}
,{ "pid":12345, "tid":12, "ts":1755290514977374, "dur":622, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Bcl.HashCode.dll" }}
,{ "pid":12345, "tid":12, "ts":1755290514977996, "dur":627, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp.exe" }}
,{ "pid":12345, "tid":12, "ts":1755290514966034, "dur":12590, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290514980146, "dur":540, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Debugger.dll" }}
,{ "pid":12345, "tid":12, "ts":1755290514983793, "dur":580, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Management.dll" }}
,{ "pid":12345, "tid":12, "ts":1755290514984373, "dur":582, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Json.dll" }}
,{ "pid":12345, "tid":12, "ts":1755290514984955, "dur":571, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Json.Microsoft.dll" }}
,{ "pid":12345, "tid":12, "ts":1755290514978624, "dur":8255, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290514986879, "dur":4259, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290514991138, "dur":3283, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290514994441, "dur":588, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290514995038, "dur":625, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290514995670, "dur":917, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290514996595, "dur":604, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290514997223, "dur":559, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290514997788, "dur":591, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290514998384, "dur":532, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290514998921, "dur":657, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290514999585, "dur":656, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515000245, "dur":679, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515000940, "dur":572, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515001537, "dur":584, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515002135, "dur":660, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515002830, "dur":578, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515003413, "dur":349, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/mconfig/config.xml" }}
,{ "pid":12345, "tid":12, "ts":1755290515003762, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515003916, "dur":144, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":12, "ts":1755290515004061, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515004206, "dur":184, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/EmbedRuntime/mono-2.0-bdwgc.dll" }}
,{ "pid":12345, "tid":12, "ts":1755290515004391, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515004524, "dur":241, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Plugins/x86_64/AppUINativePlugin.dll" }}
,{ "pid":12345, "tid":12, "ts":1755290515004765, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515004900, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515005022, "dur":564, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515005589, "dur":8557, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515014151, "dur":792, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TLSModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1755290515014944, "dur":427, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515016312, "dur":290, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.SpriteShapeModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1755290515016603, "dur":493, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515018191, "dur":193, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.CoreModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1755290515018384, "dur":205, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515018968, "dur":146, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515019123, "dur":218, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515019344, "dur":204, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1755290515019548, "dur":485, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515020951, "dur":440, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.VisualScripting.Antlr3.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1755290515021395, "dur":551, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515023008, "dur":157, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515023368, "dur":356, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515023757, "dur":576, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515025424, "dur":502, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515027356, "dur":606, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515029260, "dur":560, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515030489, "dur":589, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515031113, "dur":381, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515032791, "dur":61, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.Core.Runtime.Shared-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1755290515032852, "dur":531, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515033858, "dur":365, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515035290, "dur":557, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515036985, "dur":227, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515037219, "dur":195, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515037421, "dur":228, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515037659, "dur":106, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515037771, "dur":399, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515038210, "dur":830, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515040296, "dur":450, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755290515041339, "dur":70, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755290515054268, "dur":3701, "ph":"X", "name": "ProfilerWriteOutput" }
,