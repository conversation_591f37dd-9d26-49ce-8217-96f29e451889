[Licensing::Module] Trying to connect to existing licensing client channel...
Built from '6000.2/respin/6000.2.0f1-517f89d850d1' branch; Version is '6000.2.0f1 (eed1c594c913) revision 15651269'; Using compiler version '194234433'; Build Type 'Release'
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-ilias" at "2025-08-15T20:32:23.9241437Z"
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 65462 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-08-15T20:32:23Z

COMMAND LINE ARGUMENTS:
C:\Unity\Editors\6000.2.0f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
C:/Unity/BLAME/BLAME
-logFile
Logs/AssetImportWorker2.log
-srvPort
55728
-licensingIpc
LicenseClient-ilias
-job-worker-count
5
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Unity/BLAME/BLAME
C:/Unity/BLAME/BLAME
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [265984]  Target information:

Player connection [265984]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1256253388 [EditorId] 1256253388 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [265984]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1256253388 [EditorId] 1256253388 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [265984]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1256253388 [EditorId] 1256253388 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [265984]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1256253388 [EditorId] 1256253388 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [265984] Host joined multi-casting on [***********:54997]...
Player connection [265984] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 5
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 18644, path: "C:/Program Files/Unity Hub/UnityLicensingClient_V1/Unity.Licensing.Client.exe")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Error: HandshakeResponse reported an error:
	ResponseCode: 505
	ResponseStatus: Unsupported protocol version '1.17.1'.
[Licensing::Module] Error: Failed to handshake to channel: "LicenseClient-ilias"
[Licensing::IpcConnector] LicenseClient-ilias channel disconnected successfully.
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-ilias-6000.2.0" at "2025-08-15T20:32:24.0238884Z"
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 290512, path: "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/Licensing/Client/Unity.Licensing.Client.exe")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.17.1+ae67fbc
  Session Id:              1c31e312aba445c18a1f40744c88aefa
  Correlation Id:          5f91f71ccf6c9acad3d19ab3e1610c0c
  External correlation Id: 2970768556059990955
  Machine Id:              /GaE4A2aXaDfTh/RbExLPR8H29M=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-ilias-6000.2.0" (connect: 0.00s, validation: 0.01s, handshake: 0.02s)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-ilias-6000.2.0-notifications" at "2025-08-15T20:32:24.0464662Z"
[Licensing::Module] Licensing Background thread has ended after 0.12s
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Active build profile path: `Assets/Settings/Build Profiles/New Windows Profile.asset`
Got setting from build profile YAML, name `m_BuildTarget`, value `19`
Got setting from build profile YAML, name `m_Subtarget`, value `2`
Refreshing native plugins compatible for Editor in 384.18 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 1.30 ms.
Initialize engine version: 6000.2.0f1 (eed1c594c913)
[Subsystems] Discovering subsystems at path C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Unity/BLAME/BLAME/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3060 Ti (ID=0x2489)
    Vendor:   NVIDIA
    VRAM:     8024 MB
    Driver:   32.0.15.8088
Initialize mono
Mono path[0] = 'C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed'
Mono path[1] = 'C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56604
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.005778 seconds.
- Loaded All Assemblies, in  0.549 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.371 seconds
Domain Reload Profiling: 909ms
	BeginReloadAssembly (236ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (44ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (69ms)
	LoadAllAssembliesAndSetupDomain (173ms)
		LoadAssemblies (221ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (169ms)
			TypeCache.Refresh (167ms)
				TypeCache.ScanAssembly (153ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (372ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (315ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (59ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (71ms)
			ProcessInitializeOnLoadAttributes (128ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Got setting from build profile YAML, name `m_Development`, value `0`
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.072 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 22.62 ms, found 35 plugins.
Native extension for WindowsStandalone target not found
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <6460d52f11fa4eaeabfca1cf2e9eca8f>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-Persistent Object
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.912 seconds
Domain Reload Profiling: 3944ms
	BeginReloadAssembly (346ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (40ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (44ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (72ms)
	LoadAllAssembliesAndSetupDomain (1554ms)
		LoadAssemblies (1111ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (603ms)
			TypeCache.Refresh (485ms)
				TypeCache.ScanAssembly (461ms)
			BuildScriptInfoCaches (96ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1913ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1712ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (342ms)
			ProcessInitializeOnLoadAttributes (616ms)
			ProcessInitializeOnLoadMethodAttributes (716ms)
			AfterProcessingInitializeOnLoad (28ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (32ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 29.93 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.19 ms.
Unloading 64 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10570 unused Assets / (19.5 MB). Loaded Objects now: 11636.
Memory consumption went from 350.6 MB to 331.1 MB.
Total: 24.281400 ms (FindLiveObjects: 2.020800 ms CreateObjectMapping: 2.015700 ms MarkObjects: 10.953200 ms  DeleteObjects: 9.281800 ms)

========================================================================
Received Import Request.
  Time since last request: 509845.554355 seconds.
  path: Assets/_Game/Scripts/unity-performance-diagnostic.cs
  artifactKey: Guid(2202a040ca663de4aab5349a46ca9993) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/unity-performance-diagnostic.cs using Guid(2202a040ca663de4aab5349a46ca9993) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '06a00a7c76348d3cbbac7163cbde259d') in 0.0039151 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.271899 seconds.
  path: Assets/_Game/Scripts/unity-startup-manager.cs
  artifactKey: Guid(58f19517d2905e6499ccd75a542f8b6c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/unity-startup-manager.cs using Guid(58f19517d2905e6499ccd75a542f8b6c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9bb9a6fe1c2d496a5c8792333a129feb') in 0.0006235 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.301 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 22.16 ms, found 35 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <6460d52f11fa4eaeabfca1cf2e9eca8f>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.586 seconds
Domain Reload Profiling: 3856ms
	BeginReloadAssembly (614ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (56ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (97ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (63ms)
	LoadAllAssembliesAndSetupDomain (1535ms)
		LoadAssemblies (1303ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (500ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (457ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1586ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1279ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (366ms)
			ProcessInitializeOnLoadAttributes (598ms)
			ProcessInitializeOnLoadMethodAttributes (274ms)
			AfterProcessingInitializeOnLoad (29ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (40ms)
Refreshing native plugins compatible for Editor in 24.35 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.13 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10567 unused Assets / (19.8 MB). Loaded Objects now: 11664.
Memory consumption went from 359.8 MB to 340.0 MB.
Total: 15.103900 ms (FindLiveObjects: 0.933100 ms CreateObjectMapping: 0.539000 ms MarkObjects: 8.442200 ms  DeleteObjects: 5.188700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.512 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 28.84 ms, found 35 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <6460d52f11fa4eaeabfca1cf2e9eca8f>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.749 seconds
Domain Reload Profiling: 6229ms
	BeginReloadAssembly (883ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (66ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (156ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (81ms)
	LoadAllAssembliesAndSetupDomain (3429ms)
		LoadAssemblies (2920ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (946ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (897ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1750ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1403ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (546ms)
			ProcessInitializeOnLoadAttributes (554ms)
			ProcessInitializeOnLoadMethodAttributes (267ms)
			AfterProcessingInitializeOnLoad (26ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (42ms)
Refreshing native plugins compatible for Editor in 20.17 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.19 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10565 unused Assets / (19.8 MB). Loaded Objects now: 11665.
Memory consumption went from 359.9 MB to 340.0 MB.
Total: 15.721100 ms (FindLiveObjects: 1.530800 ms CreateObjectMapping: 1.364800 ms MarkObjects: 6.609800 ms  DeleteObjects: 6.214400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 554.493167 seconds.
  path: Assets/_Game/Models/Area-1/Cube_006.fbx
  artifactKey: Guid(1959c39d96dc4ae43998a694bf51860f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_006.fbx using Guid(1959c39d96dc4ae43998a694bf51860f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2450af67d79492c7c9dbd223f90bf3ec') in 1.8274125 seconds
Number of updated asset objects reloaded before import = 1Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/_Game/Models/Area-1/Cube_014.fbx
  artifactKey: Guid(ce142f227d26bbc488147b1cb155517d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_014.fbx using Guid(ce142f227d26bbc488147b1cb155517d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4f9109a70600b3ae4b9629f1d762d220') in 0.0384616 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/_Game/Models/Area-1/F1.fbx
  artifactKey: Guid(6fb1a43daac9fc44faecefc8d60a4af1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/F1.fbx using Guid(6fb1a43daac9fc44faecefc8d60a4af1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a12cc9da950488a948e10fff7bb931dc') in 0.0522387 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/_Game/Models/Area-1/Cube_012.fbx
  artifactKey: Guid(0a3f69fa6bf1f9b4b94bea8c2ef1c9c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_012.fbx using Guid(0a3f69fa6bf1f9b4b94bea8c2ef1c9c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6e22d63d109c483988f5b22bac06707c') in 0.030304 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000095 seconds.
  path: Assets/_Game/Models/Area-1/Debrey.fbx
  artifactKey: Guid(6a3599c84381b8340943ff1fffa6d661) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Debrey.fbx using Guid(6a3599c84381b8340943ff1fffa6d661) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6f734db324750d30c017bbe4bdddef84') in 0.0302826 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/_Game/Models/Area-1/Cylinder.fbx
  artifactKey: Guid(dcd41fe5ce99dfd47b37bc2df75c5388) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cylinder.fbx using Guid(dcd41fe5ce99dfd47b37bc2df75c5388) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1dc16e3cc0a872d84554776c4fade33a') in 0.0338413 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/_Game/Models/Area-1/Cube_017.fbx
  artifactKey: Guid(d05bae6ffab699e448101f163b4eb4eb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_017.fbx using Guid(d05bae6ffab699e448101f163b4eb4eb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '25004d1079d2d46db7cca7da442cc433') in 0.043164 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/_Game/Models/Area-1/Cube_001.fbx
  artifactKey: Guid(64ef877d8cd62b44b92b5ee2b02cc197) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_001.fbx using Guid(64ef877d8cd62b44b92b5ee2b02cc197) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'acd82bf8bb166dba8ad842eb7d687b8d') in 0.0368386 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/_Game/Models/Area-1/Cube_004.fbx
  artifactKey: Guid(f1a3c542d8cb8ea46a1bf6d929b49e4a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_004.fbx using Guid(f1a3c542d8cb8ea46a1bf6d929b49e4a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2f03a7129100590d29f33d87a94091ef') in 0.0276579 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/_Game/Models/Area-1/Cube_008.fbx
  artifactKey: Guid(0fd8a9e74b05b0e49a0e9e977acbc033) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_008.fbx using Guid(0fd8a9e74b05b0e49a0e9e977acbc033) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'af264e24fb5dbd256b08de56b5ec2a77') in 0.0310716 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/_Game/Models/Area-1/Cube_013.fbx
  artifactKey: Guid(22673c4a03585f44980bb59588e4154d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_013.fbx using Guid(22673c4a03585f44980bb59588e4154d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cda201c5befcc901f55cbd66b6eed80b') in 0.0289492 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000085 seconds.
  path: Assets/_Game/Models/Area-1/Cube_016.fbx
  artifactKey: Guid(8a4f6b05db1d05a46b279ccfa95c67ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_016.fbx using Guid(8a4f6b05db1d05a46b279ccfa95c67ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a17b9edb324e9208b0ee1b4a52fb708d') in 0.0417483 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.009214 seconds.
  path: Assets/_Game/Models/Area-1/FirstGround.fbx
  artifactKey: Guid(4dad607ce984cbe42bfc390ae86afa2d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/FirstGround.fbx using Guid(4dad607ce984cbe42bfc390ae86afa2d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f6b683b7f4561eedb7d5e4aecf82732a') in 0.0273602 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/_Game/Models/Area-1/Morror.fbx
  artifactKey: Guid(926cacaca291bb241b3f3564380e1a53) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Morror.fbx using Guid(926cacaca291bb241b3f3564380e1a53) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'caf6caf25f816a1728a968876b1c2bd7') in 0.0673005 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/_Game/Models/Area-1/cable.fbx
  artifactKey: Guid(90541e5e119c8ad4a95deff4d22c21f5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/cable.fbx using Guid(90541e5e119c8ad4a95deff4d22c21f5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd893c46eb3e379fa9863eb0ac53f1938') in 0.0389005 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/_Game/Models/Area-1/Tube_001.fbx
  artifactKey: Guid(c9662ff4c0b8d5842b2452c3aa11f576) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Tube_001.fbx using Guid(c9662ff4c0b8d5842b2452c3aa11f576) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c14ce7ce6c49bb8469a70dc77d9218f2') in 0.0334428 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/_Game/Models/Area-1/Blockout_Stairs002.fbx
  artifactKey: Guid(b98dff1e2a644974cb037ada140f913d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Blockout_Stairs002.fbx using Guid(b98dff1e2a644974cb037ada140f913d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f190ed6f33542d82149a99035bb0d8aa') in 0.027225 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/_Game/Models/Area-1/Mega.fbx
  artifactKey: Guid(999922037ce911f4f8f73bb8def097ce) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Mega.fbx using Guid(999922037ce911f4f8f73bb8def097ce) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '43d8ecff6aba038be2d4596f768965a7') in 0.0291931 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/_Game/Models/Area-1/Cable_Mass.fbx
  artifactKey: Guid(abcf7677e55d2f646b8a38c519fed07b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cable_Mass.fbx using Guid(abcf7677e55d2f646b8a38c519fed07b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5c561924e55c9e381b724ac9c0abfd5d') in 0.0390345 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/_Game/Models/Area-1/AElevatorBase.fbx
  artifactKey: Guid(a25978aa579745d44acd5f0be40c50c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/AElevatorBase.fbx using Guid(a25978aa579745d44acd5f0be40c50c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e7438676217f0bb0cfd0a23fd2dd08fe') in 0.0384132 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/_Game/Models/Area-1/test22.fbx
  artifactKey: Guid(85516922031f3fc48baa0339d79658f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/test22.fbx using Guid(85516922031f3fc48baa0339d79658f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7f4d7c53c3d5aaf78abc9cc98640ccbe') in 0.5977397 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 150

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/_Game/Models/Area-1/ACube_001.fbx
  artifactKey: Guid(f709a0b29578aa84895199dbaa4b1a1f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/ACube_001.fbx using Guid(f709a0b29578aa84895199dbaa4b1a1f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '476891c9bc625c29847e83bd58a39a63') in 0.0325719 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/_Game/Models/Area-1/Rocket.fbx
  artifactKey: Guid(61bee926499a7f548a23a040c5ee90f4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Rocket.fbx using Guid(61bee926499a7f548a23a040c5ee90f4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1d81a98000da8994b64c3bb78bf77600') in 0.0423561 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/_Game/Models/Area-1/Blockout.fbx
  artifactKey: Guid(703287888e46a9e40a5df79ed40eec4a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Blockout.fbx using Guid(703287888e46a9e40a5df79ed40eec4a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2e84f455b90ddca26e5e17045642e46c') in 0.3947364 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000085 seconds.
  path: Assets/_Game/Models/Area-1/Bestagon.fbx
  artifactKey: Guid(28fda9469ec335d4080e82ce3431740b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Bestagon.fbx using Guid(28fda9469ec335d4080e82ce3431740b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bf3724c0fa34eab2028e9086bc2902ee') in 0.031208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/_Game/Models/Area-1/cable_002.fbx
  artifactKey: Guid(48fec707ddbb29d49bac044cb602e54e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/cable_002.fbx using Guid(48fec707ddbb29d49bac044cb602e54e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8fa7084ede92d8783576147fb75da7cf') in 0.0387595 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

