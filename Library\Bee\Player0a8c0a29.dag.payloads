Payload for "WriteText Library/Bee/artifacts/csharpactions/boot.config_tyr4.info"

{"System.Object":null,"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.PlayerBuildProgramBase","methodName":"WriteBootConfigAction","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/boot.config"],"inputs":["Library/PlayerDataCache/Win642/Data/boot.config","Library/PlayerDataCache/Win642/Data/boot.config","Library/PlayerDataCache/Win642/Data/globalgamemanagers","Library/PlayerDataCache/Win642/Data/globalgamemanagers.assets","Library/PlayerDataCache/Win642/Data/globalgamemanagers.assets.resS","Library/PlayerDataCache/Win642/Data/level0","Library/PlayerDataCache/Win642/Data/level1","Library/PlayerDataCache/Win642/Data/level1.resS","Library/PlayerDataCache/Win642/Data/resources.assets","Library/PlayerDataCache/Win642/Data/resources.assets.resS","Library/PlayerDataCache/Win642/Data/resources.resource","Library/PlayerDataCache/Win642/Data/Resources/unity_builtin_extra","Library/PlayerDataCache/Win642/Data/RuntimeInitializeOnLoads.json","Library/PlayerDataCache/Win642/Data/ScriptingAssemblies.json","Library/PlayerDataCache/Win642/Data/sharedassets0.assets","Library/PlayerDataCache/Win642/Data/sharedassets0.assets.resS","Library/PlayerDataCache/Win642/Data/sharedassets0.resource","Library/PlayerDataCache/Win642/Data/sharedassets1.assets","Library/PlayerDataCache/Win642/Data/sharedassets1.assets.resS","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AIModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AMDModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ARModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AccessibilityModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AndroidJNIModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AnimationModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AssetBundleModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AudioModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClothModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClusterInputModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClusterRendererModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ContentLoadModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.CoreModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.CrashReportingModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.DSPGraphModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.DirectorModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GIModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GameCenterModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GraphicsStateCollectionSerializerModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GridModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.HierarchyCoreModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.HotReloadModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.IMGUIModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.IdentifiersModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ImageConversionModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputForUIModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputLegacyModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InsightsModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.JSONSerializeModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.LocalizationModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.MarshallingModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.MultiplayerModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.NVIDIAModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ParticleSystemModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PerformanceReportingModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.Physics2DModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PhysicsModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PropertiesModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ScreenCaptureModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ShaderVariantAnalyticsModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SharedInternalsModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SpriteMaskModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SpriteShapeModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.StreamingModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SubstanceModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SubsystemsModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TLSModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TerrainModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TerrainPhysicsModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextCoreFontEngineModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextCoreTextEngineModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextRenderingModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TilemapModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UIElementsModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UIModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UmbraModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityAnalyticsCommonModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityAnalyticsModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityConnectModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityConsentModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityCurlModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityTestProtocolModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestAudioModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestTextureModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestWWWModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VFXModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VRModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VehiclesModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VideoModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VirtualTexturingModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.WindModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.XRModule.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Autodesk.Fbx.BuildTestAssets.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Autodesk.Fbx.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/BakeryRuntimeAssembly.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Domain_Reload.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Tayx.Graphy.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.2D.Common.Runtime.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.InternalAPIBridge.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.MVVM.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.Navigation.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.Redux.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.Undo.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Burst.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Collections.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Formats.Fbx.Runtime.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.InferenceEngine.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.InternalAPIEngineBridge.001.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Csg.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.KdTree.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Poly2Tri.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Stl.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProGrids.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Recorder.Base.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Recorder.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Samples.Runtime.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Runtime.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Serialization.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.VisualEffectGraph.Runtime.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll","C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll","C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll","C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.IO.Hashing/System.IO.Hashing.dll","C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll","C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.burst@6aff1dd08a0c/Unity.Burst.Unsafe.dll","C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/AOT/Newtonsoft.Json.dll","C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Accessibility.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Commons.Xml.Relaxng.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/CustomMarshalers.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/Microsoft.Win32.Primitives.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/Microsoft.Win32.Registry.AccessControl.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/Microsoft.Win32.Registry.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.AppContext.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Buffers.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Collections.Concurrent.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Collections.NonGeneric.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Collections.Specialized.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Collections.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ComponentModel.Annotations.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ComponentModel.EventBasedAsync.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ComponentModel.Primitives.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ComponentModel.TypeConverter.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ComponentModel.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Console.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Data.Common.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Data.SqlClient.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.Contracts.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.Debug.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.FileVersionInfo.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.Process.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.StackTrace.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.TextWriterTraceListener.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.Tools.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.TraceEvent.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.TraceSource.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.Tracing.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Drawing.Primitives.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Dynamic.Runtime.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Globalization.Calendars.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Globalization.Extensions.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Globalization.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.Compression.ZipFile.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.FileSystem.AccessControl.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.FileSystem.DriveInfo.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.FileSystem.Primitives.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.FileSystem.Watcher.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.FileSystem.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.IsolatedStorage.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.MemoryMappedFiles.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.Pipes.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.UnmanagedMemoryStream.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Linq.Expressions.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Linq.Parallel.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Linq.Queryable.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Linq.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Memory.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.AuthenticationManager.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Cache.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Http.Rtc.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.HttpListener.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Mail.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.NameResolution.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.NetworkInformation.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Ping.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Primitives.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Requests.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Security.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.ServicePoint.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Sockets.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Utilities.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.WebHeaderCollection.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.WebSockets.Client.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.WebSockets.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ObjectModel.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.DispatchProxy.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.Emit.ILGeneration.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.Emit.Lightweight.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.Emit.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.Extensions.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.Primitives.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.TypeExtensions.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Resources.Reader.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Resources.ReaderWriter.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Resources.ResourceManager.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Resources.Writer.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.CompilerServices.VisualC.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Extensions.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Handles.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.InteropServices.RuntimeInformation.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.InteropServices.WindowsRuntime.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.InteropServices.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Loader.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Numerics.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Serialization.Formatters.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Serialization.Json.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Serialization.Primitives.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Serialization.Xml.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.AccessControl.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Claims.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Algorithms.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Cng.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Csp.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.DeriveBytes.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Encoding.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Encryption.Aes.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Encryption.ECDiffieHellman.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Encryption.ECDsa.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Encryption.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Hashing.Algorithms.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Hashing.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.OpenSsl.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Pkcs.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Primitives.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.ProtectedData.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.RSA.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.RandomNumberGenerator.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.X509Certificates.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Principal.Windows.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Principal.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.SecureString.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ServiceModel.Duplex.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ServiceModel.Http.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ServiceModel.NetTcp.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ServiceModel.Primitives.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ServiceModel.Security.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ServiceProcess.ServiceController.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Text.Encoding.CodePages.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Text.Encoding.Extensions.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Text.Encoding.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Text.RegularExpressions.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.AccessControl.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.Overlapped.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.Tasks.Extensions.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.Tasks.Parallel.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.Tasks.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.Thread.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.ThreadPool.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.Timer.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ValueTuple.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.ReaderWriter.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.XDocument.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.XPath.XDocument.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.XPath.XmlDocument.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.XPath.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.XmlDocument.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.XmlSerializer.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.Xsl.Primitives.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/netstandard.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/I18N.CJK.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/I18N.MidEast.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/I18N.Other.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/I18N.Rare.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/I18N.West.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/I18N.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/IBM.Data.DB2.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/ICSharpCode.SharpZipLib.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.Build.Engine.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.Build.Framework.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.Build.Tasks.v4.0.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.Build.Utilities.v4.0.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.Build.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.CSharp.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.VisualC.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.Web.Infrastructure.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Btls.Interface.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.C5.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.CSharp.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Cairo.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.CompilerServices.SymbolWriter.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Data.Sqlite.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Data.Tds.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Http.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Management.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Messaging.RabbitMQ.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Messaging.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Options.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Parallel.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Posix.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Profiler.Log.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Security.Win32.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Security.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Simd.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Tasklets.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.WebBrowser.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.XBuild.Tasks.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Novell.Directory.Ldap.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/PEAPI.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/RabbitMQ.Client.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/SMDiagnostics.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ComponentModel.Composition.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ComponentModel.DataAnnotations.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Configuration.Install.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Configuration.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Core.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.DataSetExtensions.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.Entity.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.Linq.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.OracleClient.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.Services.Client.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.Services.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Deployment.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Design.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.DirectoryServices.Protocols.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.DirectoryServices.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Drawing.Design.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Drawing.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Dynamic.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.EnterpriseServices.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.IO.Compression.FileSystem.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.IO.Compression.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.IdentityModel.Selectors.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.IdentityModel.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Json.Microsoft.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Json.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Management.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Messaging.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Net.Http.Formatting.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Net.Http.WebRequest.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Net.Http.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Net.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Numerics.Vectors.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Numerics.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Core.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Debugger.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Experimental.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Interfaces.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Linq.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Observable.Aliases.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.PlatformServices.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Providers.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Runtime.Remoting.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Windows.Forms.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Windows.Threading.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reflection.Context.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Runtime.Caching.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Runtime.DurableInstancing.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Runtime.Remoting.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Runtime.Serialization.Formatters.Soap.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Runtime.Serialization.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Security.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.Activation.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.Discovery.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.Internals.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.Routing.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.Web.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceProcess.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Threading.Tasks.Dataflow.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Transactions.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Abstractions.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.ApplicationServices.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.DynamicData.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Extensions.Design.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Extensions.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Http.SelfHost.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Http.WebHost.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Http.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Mobile.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Mvc.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Razor.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.RegularExpressions.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Routing.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Services.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.WebPages.Deployment.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.WebPages.Razor.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.WebPages.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Windows.Forms.DataVisualization.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Windows.Forms.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Windows.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Workflow.Activities.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Workflow.ComponentModel.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Workflow.Runtime.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Xaml.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Xml.Linq.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Xml.Serialization.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Xml.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/SystemWebTestShim.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/WebMatrix.Data.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/WindowsBase.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/cscompmgd.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/monodoc.dll","C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/mscorlib.dll","Library/Bee/Player0a8c0a29-inputdata.json"],"targetDirectories":[]}}    

Payload for "WriteResponseFile Library/Bee/artifacts/rsp/7893594715672875865.rsp"

--allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AIModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AMDModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ARModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AccessibilityModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AndroidJNIModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AnimationModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AssetBundleModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AudioModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClothModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClusterInputModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClusterRendererModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ContentLoadModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.CoreModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.CrashReportingModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.DSPGraphModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.DirectorModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GIModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GameCenterModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GraphicsStateCollectionSerializerModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GridModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.HierarchyCoreModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.HotReloadModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.IMGUIModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.IdentifiersModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ImageConversionModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputForUIModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputLegacyModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InsightsModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.JSONSerializeModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.LocalizationModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.MarshallingModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.MultiplayerModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.NVIDIAModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ParticleSystemModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PerformanceReportingModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.Physics2DModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PhysicsModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PropertiesModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ScreenCaptureModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ShaderVariantAnalyticsModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SharedInternalsModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SpriteMaskModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SpriteShapeModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.StreamingModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SubstanceModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SubsystemsModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TLSModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TerrainModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TerrainPhysicsModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextCoreFontEngineModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextCoreTextEngineModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextRenderingModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TilemapModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UIElementsModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UIModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UmbraModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityAnalyticsCommonModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityAnalyticsModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityConnectModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityConsentModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityCurlModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityTestProtocolModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestAudioModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestTextureModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestWWWModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VFXModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VRModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VehiclesModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VideoModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VirtualTexturingModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.WindModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.XRModule.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Autodesk.Fbx.BuildTestAssets.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Autodesk.Fbx.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/BakeryRuntimeAssembly.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Domain_Reload.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Tayx.Graphy.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.2D.Common.Runtime.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.InternalAPIBridge.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.MVVM.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.Navigation.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.Redux.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.Undo.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Burst.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Collections.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Formats.Fbx.Runtime.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.InferenceEngine.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.InternalAPIEngineBridge.001.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Csg.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.KdTree.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Poly2Tri.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Stl.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProGrids.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Recorder.Base.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Recorder.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Samples.Runtime.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Runtime.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Serialization.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.VisualEffectGraph.Runtime.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.IO.Hashing/System.IO.Hashing.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.burst@6aff1dd08a0c/Unity.Burst.Unsafe.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/AOT/Newtonsoft.Json.dll" --allowed-assembly="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Accessibility.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Commons.Xml.Relaxng.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/CustomMarshalers.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/Microsoft.Win32.Primitives.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/Microsoft.Win32.Registry.AccessControl.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/Microsoft.Win32.Registry.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.AppContext.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Buffers.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Collections.Concurrent.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Collections.NonGeneric.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Collections.Specialized.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Collections.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ComponentModel.Annotations.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ComponentModel.EventBasedAsync.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ComponentModel.Primitives.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ComponentModel.TypeConverter.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ComponentModel.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Console.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Data.Common.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Data.SqlClient.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.Contracts.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.Debug.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.FileVersionInfo.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.Process.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.StackTrace.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.TextWriterTraceListener.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.Tools.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.TraceEvent.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.TraceSource.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Diagnostics.Tracing.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Drawing.Primitives.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Dynamic.Runtime.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Globalization.Calendars.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Globalization.Extensions.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Globalization.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.Compression.ZipFile.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.FileSystem.AccessControl.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.FileSystem.DriveInfo.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.FileSystem.Primitives.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.FileSystem.Watcher.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.FileSystem.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.IsolatedStorage.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.MemoryMappedFiles.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.Pipes.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.UnmanagedMemoryStream.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.IO.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Linq.Expressions.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Linq.Parallel.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Linq.Queryable.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Linq.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Memory.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.AuthenticationManager.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Cache.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Http.Rtc.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.HttpListener.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Mail.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.NameResolution.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.NetworkInformation.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Ping.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Primitives.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Requests.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Security.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.ServicePoint.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Sockets.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.Utilities.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.WebHeaderCollection.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.WebSockets.Client.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Net.WebSockets.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ObjectModel.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.DispatchProxy.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.Emit.ILGeneration.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.Emit.Lightweight.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.Emit.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.Extensions.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.Primitives.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.TypeExtensions.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Reflection.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Resources.Reader.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Resources.ReaderWriter.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Resources.ResourceManager.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Resources.Writer.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.CompilerServices.VisualC.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Extensions.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Handles.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.InteropServices.RuntimeInformation.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.InteropServices.WindowsRuntime.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.InteropServices.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Loader.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Numerics.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Serialization.Formatters.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Serialization.Json.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Serialization.Primitives.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.Serialization.Xml.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Runtime.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.AccessControl.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Claims.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Algorithms.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Cng.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Csp.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.DeriveBytes.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Encoding.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Encryption.Aes.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Encryption.ECDiffieHellman.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Encryption.ECDsa.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Encryption.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Hashing.Algorithms.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Hashing.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.OpenSsl.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Pkcs.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.Primitives.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.ProtectedData.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.RSA.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.RandomNumberGenerator.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Cryptography.X509Certificates.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Principal.Windows.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.Principal.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Security.SecureString.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ServiceModel.Duplex.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ServiceModel.Http.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ServiceModel.NetTcp.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ServiceModel.Primitives.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ServiceModel.Security.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ServiceProcess.ServiceController.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Text.Encoding.CodePages.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Text.Encoding.Extensions.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Text.Encoding.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Text.RegularExpressions.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.AccessControl.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.Overlapped.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.Tasks.Extensions.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.Tasks.Parallel.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.Tasks.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.Thread.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.ThreadPool.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.Timer.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Threading.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.ValueTuple.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.ReaderWriter.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.XDocument.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.XPath.XDocument.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.XPath.XmlDocument.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.XPath.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.XmlDocument.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.XmlSerializer.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/System.Xml.Xsl.Primitives.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades/netstandard.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/I18N.CJK.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/I18N.MidEast.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/I18N.Other.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/I18N.Rare.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/I18N.West.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/I18N.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/IBM.Data.DB2.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/ICSharpCode.SharpZipLib.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.Build.Engine.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.Build.Framework.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.Build.Tasks.v4.0.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.Build.Utilities.v4.0.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.Build.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.CSharp.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.VisualC.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Microsoft.Web.Infrastructure.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Btls.Interface.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.C5.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.CSharp.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Cairo.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.CompilerServices.SymbolWriter.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Data.Sqlite.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Data.Tds.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Http.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Management.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Messaging.RabbitMQ.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Messaging.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Options.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Parallel.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Posix.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Profiler.Log.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Security.Win32.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Security.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Simd.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.Tasklets.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.WebBrowser.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Mono.XBuild.Tasks.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Novell.Directory.Ldap.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/PEAPI.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/RabbitMQ.Client.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/SMDiagnostics.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ComponentModel.Composition.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ComponentModel.DataAnnotations.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Configuration.Install.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Configuration.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Core.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.DataSetExtensions.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.Entity.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.Linq.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.OracleClient.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.Services.Client.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.Services.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Data.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Deployment.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Design.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.DirectoryServices.Protocols.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.DirectoryServices.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Drawing.Design.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Drawing.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Dynamic.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.EnterpriseServices.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.IO.Compression.FileSystem.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.IO.Compression.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.IdentityModel.Selectors.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.IdentityModel.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Json.Microsoft.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Json.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Management.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Messaging.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Net.Http.Formatting.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Net.Http.WebRequest.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Net.Http.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Net.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Numerics.Vectors.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Numerics.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Core.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Debugger.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Experimental.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Interfaces.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Linq.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Observable.Aliases.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.PlatformServices.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Providers.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Runtime.Remoting.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Windows.Forms.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reactive.Windows.Threading.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Reflection.Context.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Runtime.Caching.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Runtime.DurableInstancing.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Runtime.Remoting.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Runtime.Serialization.Formatters.Soap.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Runtime.Serialization.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Security.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.Activation.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.Discovery.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.Internals.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.Routing.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.Web.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceModel.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.ServiceProcess.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Threading.Tasks.Dataflow.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Transactions.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Abstractions.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.ApplicationServices.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.DynamicData.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Extensions.Design.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Extensions.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Http.SelfHost.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Http.WebHost.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Http.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Mobile.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Mvc.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Razor.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.RegularExpressions.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Routing.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.Services.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.WebPages.Deployment.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.WebPages.Razor.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.WebPages.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Web.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Windows.Forms.DataVisualization.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Windows.Forms.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Windows.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Workflow.Activities.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Workflow.ComponentModel.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Workflow.Runtime.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Xaml.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Xml.Linq.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Xml.Serialization.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.Xml.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/System.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/SystemWebTestShim.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/WebMatrix.Data.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/WindowsBase.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/cscompmgd.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/monodoc.dll" --allowed-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/mscorlib.dll" --out="Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped" --include-link-xml="C:/Unity/BLAME/BLAME/Library/Bee/artifacts/UnityLinkerInputs/MethodsToPreserve.xml" --include-link-xml="C:/Unity/BLAME/BLAME/Library/Bee/artifacts/UnityLinkerInputs/TypesInScenes.xml" --include-link-xml="C:/Unity/BLAME/BLAME/Library/Bee/artifacts/UnityLinkerInputs/SerializedTypes.xml" --include-directory="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed" --include-directory="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies" --include-directory="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.LowLevel.ILSupport" --include-directory="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.IO.Hashing" --include-directory="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Runtime/VisualScripting.Flow/Dependencies/NCalc" --include-directory="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.burst@6aff1dd08a0c" --include-directory="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/AOT" --include-directory="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe" --include-directory="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32" --include-directory="C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32/Facades" --rule-set=Copy --profiler-report --profiler-output-file="C:/Unity/BLAME/BLAME/Library/Bee/artifacts/unitylinker_dwek.traceevents" --dotnetprofile=unityaot-win32 --dotnetruntime=Mono --platform=WindowsDesktop --use-editor-options --engine-modules-asset-file="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/modules.asset" --editor-data-file="C:/Unity/BLAME/BLAME/Library/Bee/artifacts/UnityLinkerInputs/EditorToUnityLinkerData.json" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AIModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AMDModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ARModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AccessibilityModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AndroidJNIModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AnimationModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AssetBundleModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.AudioModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClothModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClusterInputModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ClusterRendererModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ContentLoadModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.CoreModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.CrashReportingModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.DSPGraphModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.DirectorModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GIModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GameCenterModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GraphicsStateCollectionSerializerModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.GridModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.HierarchyCoreModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.HotReloadModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.IMGUIModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.IdentifiersModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ImageConversionModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputForUIModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputLegacyModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InputModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.InsightsModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.JSONSerializeModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.LocalizationModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.MarshallingModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.MultiplayerModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.NVIDIAModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ParticleSystemModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PerformanceReportingModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.Physics2DModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PhysicsModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.PropertiesModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ScreenCaptureModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.ShaderVariantAnalyticsModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SharedInternalsModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SpriteMaskModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SpriteShapeModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.StreamingModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SubstanceModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.SubsystemsModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TLSModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TerrainModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TerrainPhysicsModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextCoreFontEngineModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextCoreTextEngineModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TextRenderingModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.TilemapModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UIElementsModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UIModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UmbraModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityAnalyticsCommonModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityAnalyticsModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityConnectModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityConsentModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityCurlModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityTestProtocolModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestAudioModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestTextureModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.UnityWebRequestWWWModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VFXModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VRModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VehiclesModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VideoModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.VirtualTexturingModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.WindModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.XRModule.dll" --include-unity-root-assembly="C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/mono/Managed/UnityEngine.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Autodesk.Fbx.BuildTestAssets.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Autodesk.Fbx.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/BakeryRuntimeAssembly.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Domain_Reload.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Tayx.Graphy.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.2D.Common.Runtime.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.InternalAPIBridge.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.MVVM.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.Navigation.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.Redux.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.AppUI.Undo.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Burst.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Collections.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Formats.Fbx.Runtime.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.InferenceEngine.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.InternalAPIEngineBridge.001.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Csg.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.KdTree.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Poly2Tri.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Stl.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.ProGrids.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Recorder.Base.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Recorder.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Samples.Runtime.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Runtime.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Serialization.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.VisualEffectGraph.Runtime.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.IO.Hashing/System.IO.Hashing.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.burst@6aff1dd08a0c/Unity.Burst.Unsafe.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/AOT/Newtonsoft.Json.dll" --include-unity-root-assembly="C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll" --print-command-line --enable-analytics    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Assembly-CSharp-FeaturesChecked.txt_w20p.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Assembly-CSharp-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Assembly-CSharp.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Autodesk.Fbx.BuildTestAssets-FeaturesChecked.txt_rucs.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Autodesk.Fbx.BuildTestAssets-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Autodesk.Fbx.BuildTestAssets.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Autodesk.Fbx-FeaturesChecked.txt_2i1n.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Autodesk.Fbx-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Autodesk.Fbx.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/BakeryRuntimeAssembly-FeaturesChecked.txt_gew5.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/BakeryRuntimeAssembly-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/BakeryRuntimeAssembly.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Domain_Reload-FeaturesChecked.txt_q57l.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Domain_Reload-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Domain_Reload.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Mono.Security-FeaturesChecked.txt_dyiy.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Mono.Security-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Mono.Security.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/mscorlib-FeaturesChecked.txt_9kc9.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/mscorlib-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/mscorlib.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/netstandard-FeaturesChecked.txt_0cit.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/netstandard-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/netstandard.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Newtonsoft.Json-FeaturesChecked.txt_zgd9.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Newtonsoft.Json-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Newtonsoft.Json.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.ComponentModel.Composition-FeaturesChecked.txt_91q2.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.ComponentModel.Composition-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.ComponentModel.Composition.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.Configuration-FeaturesChecked.txt_nqsf.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Configuration-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Configuration.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.Core-FeaturesChecked.txt_och0.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Core-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Core.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.Data.DataSetExtensions-FeaturesChecked.txt_pams.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Data.DataSetExtensions-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Data.DataSetExtensions.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.Data-FeaturesChecked.txt_vhp1.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Data-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Data.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System-FeaturesChecked.txt_vic3.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/System-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.Drawing-FeaturesChecked.txt_vy29.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Drawing-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Drawing.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.EnterpriseServices-FeaturesChecked.txt_kt5q.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.EnterpriseServices-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.EnterpriseServices.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.IO.Compression-FeaturesChecked.txt_f62v.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.IO.Compression-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.IO.Compression.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.IO.Compression.FileSystem-FeaturesChecked.txt_caef.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.IO.Compression.FileSystem-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.IO.Compression.FileSystem.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.IO.Hashing-FeaturesChecked.txt_lgof.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.IO.Hashing-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.IO.Hashing.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.Memory-FeaturesChecked.txt_mhrh.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Memory-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Memory.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.Net.Http-FeaturesChecked.txt_iku7.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Net.Http-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Net.Http.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.Numerics-FeaturesChecked.txt_bd3w.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Numerics-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Numerics.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.Runtime.CompilerServices.Unsafe-FeaturesChecked.txt_mtva.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Runtime.CompilerServices.Unsafe-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Runtime.CompilerServices.Unsafe.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.Runtime-FeaturesChecked.txt_hkre.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Runtime-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Runtime.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.Runtime.Serialization-FeaturesChecked.txt_37nq.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Runtime.Serialization-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Runtime.Serialization.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.Security-FeaturesChecked.txt_1xux.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Security-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Security.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.ServiceModel.Internals-FeaturesChecked.txt_hhne.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.ServiceModel.Internals-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.ServiceModel.Internals.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.Transactions-FeaturesChecked.txt_ugsj.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Transactions-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Transactions.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.Xml-FeaturesChecked.txt_wcjl.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Xml-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Xml.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.Xml.Linq-FeaturesChecked.txt_s1tc.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Xml.Linq-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Xml.Linq.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Tayx.Graphy-FeaturesChecked.txt_fqli.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Tayx.Graphy-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Tayx.Graphy.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.2D.Common.Runtime-FeaturesChecked.txt_2jk0.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.2D.Common.Runtime-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.2D.Common.Runtime.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.AppUI-FeaturesChecked.txt_ylae.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.AppUI-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.AppUI.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.AppUI.InternalAPIBridge-FeaturesChecked.txt_26vz.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.AppUI.InternalAPIBridge-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.AppUI.InternalAPIBridge.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.AppUI.MVVM-FeaturesChecked.txt_endm.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.AppUI.MVVM-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.AppUI.MVVM.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.AppUI.Navigation-FeaturesChecked.txt_a94g.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.AppUI.Navigation-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.AppUI.Navigation.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.AppUI.Redux-FeaturesChecked.txt_4dma.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.AppUI.Redux-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.AppUI.Redux.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.AppUI.Undo-FeaturesChecked.txt_q1x6.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.AppUI.Undo-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.AppUI.Undo.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.Burst-FeaturesChecked.txt_9564.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Burst-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.Burst.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.Burst.Unsafe-FeaturesChecked.txt_1w2c.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Burst.Unsafe-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.Burst.Unsafe.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.Collections-FeaturesChecked.txt_yda5.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Collections-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.Collections.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.Collections.LowLevel.ILSupport-FeaturesChecked.txt_ocjd.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Collections.LowLevel.ILSupport-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.Collections.LowLevel.ILSupport.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.Formats.Fbx.Runtime-FeaturesChecked.txt_5zb2.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Formats.Fbx.Runtime-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.Formats.Fbx.Runtime.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.InferenceEngine-FeaturesChecked.txt_wnax.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.InferenceEngine-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.InferenceEngine.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.InternalAPIEngineBridge.001-FeaturesChecked.txt_ke8c.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.InternalAPIEngineBridge.001-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.InternalAPIEngineBridge.001.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.Mathematics-FeaturesChecked.txt_rnku.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Mathematics-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.Mathematics.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.Multiplayer.Center.Common-FeaturesChecked.txt_h48p.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Multiplayer.Center.Common-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.Multiplayer.Center.Common.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.ProBuilder.Csg-FeaturesChecked.txt_cnoa.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.ProBuilder.Csg-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.ProBuilder.Csg.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.ProBuilder-FeaturesChecked.txt_7p2l.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.ProBuilder-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.ProBuilder.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.ProBuilder.KdTree-FeaturesChecked.txt_wy30.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.ProBuilder.KdTree-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.ProBuilder.KdTree.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.ProBuilder.Poly2Tri-FeaturesChecked.txt_yxcm.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.ProBuilder.Poly2Tri-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.ProBuilder.Poly2Tri.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.ProBuilder.Stl-FeaturesChecked.txt_8cz9.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.ProBuilder.Stl-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.ProBuilder.Stl.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.ProGrids-FeaturesChecked.txt_1j1e.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.ProGrids-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.ProGrids.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.Recorder.Base-FeaturesChecked.txt_23aq.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Recorder.Base-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.Recorder.Base.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.Recorder-FeaturesChecked.txt_rlwz.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Recorder-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.Recorder.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.Rendering.LightTransport.Runtime-FeaturesChecked.txt_4asl.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Rendering.LightTransport.Runtime-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.Rendering.LightTransport.Runtime.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt_p13n.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.RenderPipelines.Core.Runtime.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.RenderPipelines.Core.Runtime.Shared-FeaturesChecked.txt_09x5.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.Core.Runtime.Shared-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.RenderPipelines.Core.Runtime.Shared.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.RenderPipelines.Core.Samples.Runtime-FeaturesChecked.txt_05lp.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.Core.Samples.Runtime-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.RenderPipelines.Core.Samples.Runtime.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.RenderPipelines.Core.ShaderLibrary-FeaturesChecked.txt_7gs3.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.Core.ShaderLibrary-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.RenderPipelines.Core.ShaderLibrary.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.RenderPipelines.GPUDriven.Runtime-FeaturesChecked.txt_snbh.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.GPUDriven.Runtime-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.RenderPipelines.GPUDriven.Runtime.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.RenderPipelines.HighDefinition.Config.Runtime-FeaturesChecked.txt_76rt.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.HighDefinition.Config.Runtime-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.RenderPipelines.HighDefinition.Runtime-FeaturesChecked.txt_v1r7.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.HighDefinition.Runtime-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.RenderPipelines.HighDefinition.Runtime.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime-FeaturesChecked.txt_tdqh.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary-FeaturesChecked.txt_ze5s.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.Serialization-FeaturesChecked.txt_d5sk.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Serialization-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.Serialization.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.TextMeshPro-FeaturesChecked.txt_syok.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.TextMeshPro-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.TextMeshPro.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.Timeline-FeaturesChecked.txt_ygu9.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Timeline-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.Timeline.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.VisualEffectGraph.Runtime-FeaturesChecked.txt_k1ac.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.VisualEffectGraph.Runtime-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.VisualEffectGraph.Runtime.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.VisualScripting.Antlr3.Runtime-FeaturesChecked.txt_o8bf.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.VisualScripting.Antlr3.Runtime-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.VisualScripting.Antlr3.Runtime.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.VisualScripting.Core-FeaturesChecked.txt_bnmf.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.VisualScripting.Core-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.VisualScripting.Core.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.VisualScripting.Flow-FeaturesChecked.txt_m429.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.VisualScripting.Flow-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.VisualScripting.Flow.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.VisualScripting.State-FeaturesChecked.txt_70xz.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.VisualScripting.State-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.VisualScripting.State.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AccessibilityModule-FeaturesChecked.txt_tops.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AccessibilityModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.AccessibilityModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AIModule-FeaturesChecked.txt_b24d.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AIModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.AIModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AMDModule-FeaturesChecked.txt_v240.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AMDModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.AMDModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AndroidJNIModule-FeaturesChecked.txt_p2rr.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AndroidJNIModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.AndroidJNIModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AnimationModule-FeaturesChecked.txt_ryhl.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AnimationModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.AnimationModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.ARModule-FeaturesChecked.txt_41lh.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ARModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.ARModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AssetBundleModule-FeaturesChecked.txt_1jdn.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AssetBundleModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.AssetBundleModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AudioModule-FeaturesChecked.txt_dmkb.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AudioModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.AudioModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.ClothModule-FeaturesChecked.txt_df2e.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ClothModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.ClothModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.ClusterInputModule-FeaturesChecked.txt_frb1.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ClusterInputModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.ClusterInputModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.ClusterRendererModule-FeaturesChecked.txt_ssow.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ClusterRendererModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.ClusterRendererModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.ContentLoadModule-FeaturesChecked.txt_iz9q.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ContentLoadModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.ContentLoadModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.CoreModule-FeaturesChecked.txt_ezkd.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.CoreModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.CoreModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.CrashReportingModule-FeaturesChecked.txt_2398.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.CrashReportingModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.CrashReportingModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.DirectorModule-FeaturesChecked.txt_rfay.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.DirectorModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.DirectorModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine-FeaturesChecked.txt_1bd3.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.DSPGraphModule-FeaturesChecked.txt_8ndh.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.DSPGraphModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.DSPGraphModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.GameCenterModule-FeaturesChecked.txt_uhhl.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.GameCenterModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.GameCenterModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.GIModule-FeaturesChecked.txt_p171.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.GIModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.GIModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.GraphicsStateCollectionSerializerModule-FeaturesChecked.txt_teqg.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.GraphicsStateCollectionSerializerModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.GraphicsStateCollectionSerializerModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.GridModule-FeaturesChecked.txt_p67v.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.GridModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.GridModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.HierarchyCoreModule-FeaturesChecked.txt_rufr.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.HierarchyCoreModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.HierarchyCoreModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.HotReloadModule-FeaturesChecked.txt_p7t3.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.HotReloadModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.HotReloadModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.IdentifiersModule-FeaturesChecked.txt_l52h.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.IdentifiersModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.IdentifiersModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.ImageConversionModule-FeaturesChecked.txt_kvxg.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ImageConversionModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.ImageConversionModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.IMGUIModule-FeaturesChecked.txt_eqp3.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.IMGUIModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.IMGUIModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.InputForUIModule-FeaturesChecked.txt_inc2.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.InputForUIModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.InputForUIModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.InputLegacyModule-FeaturesChecked.txt_y8g4.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.InputLegacyModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.InputLegacyModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.InputModule-FeaturesChecked.txt_hmqk.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.InputModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.InputModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.InsightsModule-FeaturesChecked.txt_ei8i.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.InsightsModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.InsightsModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.JSONSerializeModule-FeaturesChecked.txt_fm7j.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.JSONSerializeModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.JSONSerializeModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.LocalizationModule-FeaturesChecked.txt_gh92.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.LocalizationModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.LocalizationModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.MarshallingModule-FeaturesChecked.txt_ru2c.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.MarshallingModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.MarshallingModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.MultiplayerModule-FeaturesChecked.txt_d35b.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.MultiplayerModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.MultiplayerModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.NVIDIAModule-FeaturesChecked.txt_eiz4.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.NVIDIAModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.NVIDIAModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.ParticleSystemModule-FeaturesChecked.txt_8t4d.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ParticleSystemModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.ParticleSystemModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.PerformanceReportingModule-FeaturesChecked.txt_drqr.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.PerformanceReportingModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.PerformanceReportingModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.Physics2DModule-FeaturesChecked.txt_cj5q.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.Physics2DModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.Physics2DModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.PhysicsModule-FeaturesChecked.txt_2kc2.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.PhysicsModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.PhysicsModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.PropertiesModule-FeaturesChecked.txt_e859.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.PropertiesModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.PropertiesModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule-FeaturesChecked.txt_5e3c.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.ScreenCaptureModule-FeaturesChecked.txt_dnv7.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ScreenCaptureModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.ScreenCaptureModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.ShaderVariantAnalyticsModule-FeaturesChecked.txt_lj2r.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ShaderVariantAnalyticsModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.ShaderVariantAnalyticsModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.SharedInternalsModule-FeaturesChecked.txt_0vls.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.SharedInternalsModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.SharedInternalsModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.SpriteMaskModule-FeaturesChecked.txt_80xg.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.SpriteMaskModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.SpriteMaskModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.SpriteShapeModule-FeaturesChecked.txt_fn2f.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.SpriteShapeModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.SpriteShapeModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.StreamingModule-FeaturesChecked.txt_pydr.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.StreamingModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.StreamingModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.SubstanceModule-FeaturesChecked.txt_rpif.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.SubstanceModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.SubstanceModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.SubsystemsModule-FeaturesChecked.txt_nywe.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.SubsystemsModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.SubsystemsModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TerrainModule-FeaturesChecked.txt_wxmw.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TerrainModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.TerrainModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TerrainPhysicsModule-FeaturesChecked.txt_5hsl.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TerrainPhysicsModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.TerrainPhysicsModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt_k50w.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.TextCoreFontEngineModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt_uyte.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.TextCoreTextEngineModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TextRenderingModule-FeaturesChecked.txt_ibdi.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TextRenderingModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.TextRenderingModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TilemapModule-FeaturesChecked.txt_xb4c.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TilemapModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.TilemapModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TLSModule-FeaturesChecked.txt_yqwi.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TLSModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.TLSModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UI-FeaturesChecked.txt_buxz.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UI-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UI.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UIElementsModule-FeaturesChecked.txt_28gs.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UIElementsModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UIElementsModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UIModule-FeaturesChecked.txt_rlwj.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UIModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UIModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UmbraModule-FeaturesChecked.txt_cv7q.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UmbraModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UmbraModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt_nc1m.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UnityAnalyticsCommonModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityAnalyticsModule-FeaturesChecked.txt_gvtj.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityAnalyticsModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UnityAnalyticsModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityConnectModule-FeaturesChecked.txt_wobe.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityConnectModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UnityConnectModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityConsentModule-FeaturesChecked.txt_s5qy.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityConsentModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UnityConsentModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityCurlModule-FeaturesChecked.txt_sheb.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityCurlModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UnityCurlModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityTestProtocolModule-FeaturesChecked.txt_6hwq.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityTestProtocolModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UnityTestProtocolModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityWebRequestAssetBundleModule-FeaturesChecked.txt_5xwn.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityWebRequestAssetBundleModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UnityWebRequestAssetBundleModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityWebRequestAudioModule-FeaturesChecked.txt_5ujt.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityWebRequestAudioModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UnityWebRequestAudioModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt_b1wr.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UnityWebRequestModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityWebRequestTextureModule-FeaturesChecked.txt_y9fk.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityWebRequestTextureModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UnityWebRequestTextureModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityWebRequestWWWModule-FeaturesChecked.txt_u1d6.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityWebRequestWWWModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UnityWebRequestWWWModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.VehiclesModule-FeaturesChecked.txt_qeat.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.VehiclesModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.VehiclesModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.VFXModule-FeaturesChecked.txt_7vv1.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.VFXModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.VFXModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.VideoModule-FeaturesChecked.txt_mt2t.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.VideoModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.VideoModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.VirtualTexturingModule-FeaturesChecked.txt_s30u.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.VirtualTexturingModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.VirtualTexturingModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.VRModule-FeaturesChecked.txt_fad4.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.VRModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.VRModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.WindModule-FeaturesChecked.txt_3z9k.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.WindModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.WindModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.XRModule-FeaturesChecked.txt_pvv9.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":false,"featuresToCheck":[{"referenceName":"UnityEngine.Rendering.MachineLearningContext"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.XRModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.XRModule.dll"],"targetDirectories":[]}}    

Payload for "GenerateNativePluginsForAssemblies Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker"

{"PlayerBuildProgramLibrary.Data.GenerateNativePluginsForAssembliesArgs":{"PluginOutputFolder":"Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker","SymbolOutputFolder":"Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinkerSymbols","Assemblies":["Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Assembly-CSharp.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Autodesk.Fbx.BuildTestAssets.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Autodesk.Fbx.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/BakeryRuntimeAssembly.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Domain_Reload.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Mono.Security.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/mscorlib.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/netstandard.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Newtonsoft.Json.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.ComponentModel.Composition.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Configuration.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Core.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Data.DataSetExtensions.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Data.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Drawing.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.EnterpriseServices.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.IO.Compression.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.IO.Compression.FileSystem.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.IO.Hashing.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Memory.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Net.Http.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Numerics.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Runtime.CompilerServices.Unsafe.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Runtime.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Runtime.Serialization.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Security.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.ServiceModel.Internals.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Transactions.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Xml.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/System.Xml.Linq.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Tayx.Graphy.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.2D.Common.Runtime.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.AppUI.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.AppUI.InternalAPIBridge.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.AppUI.MVVM.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.AppUI.Navigation.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.AppUI.Redux.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.AppUI.Undo.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.Burst.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.Burst.Unsafe.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.Collections.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.Collections.LowLevel.ILSupport.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.Formats.Fbx.Runtime.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.InferenceEngine.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.InternalAPIEngineBridge.001.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.Mathematics.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.Multiplayer.Center.Common.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.ProBuilder.Csg.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.ProBuilder.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.ProBuilder.KdTree.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.ProBuilder.Poly2Tri.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.ProBuilder.Stl.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.ProGrids.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.Recorder.Base.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.Recorder.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.Rendering.LightTransport.Runtime.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.RenderPipelines.Core.Runtime.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.RenderPipelines.Core.Runtime.Shared.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.RenderPipelines.Core.Samples.Runtime.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.RenderPipelines.Core.ShaderLibrary.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.RenderPipelines.GPUDriven.Runtime.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.RenderPipelines.HighDefinition.Runtime.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.Serialization.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.TextMeshPro.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.Timeline.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.VisualEffectGraph.Runtime.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.VisualScripting.Antlr3.Runtime.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.VisualScripting.Core.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.VisualScripting.Flow.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/Unity.VisualScripting.State.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.AccessibilityModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.AIModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.AMDModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.AndroidJNIModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.AnimationModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.ARModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.AssetBundleModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.AudioModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.ClothModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.ClusterInputModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.ClusterRendererModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.ContentLoadModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.CoreModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.CrashReportingModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.DirectorModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.DSPGraphModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.GameCenterModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.GIModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.GraphicsStateCollectionSerializerModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.GridModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.HierarchyCoreModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.HotReloadModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.IdentifiersModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.ImageConversionModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.IMGUIModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.InputForUIModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.InputLegacyModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.InputModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.InsightsModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.JSONSerializeModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.LocalizationModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.MarshallingModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.MultiplayerModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.NVIDIAModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.ParticleSystemModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.PerformanceReportingModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.Physics2DModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.PhysicsModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.PropertiesModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.ScreenCaptureModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.ShaderVariantAnalyticsModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.SharedInternalsModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.SpriteMaskModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.SpriteShapeModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.StreamingModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.SubstanceModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.SubsystemsModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.TerrainModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.TerrainPhysicsModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.TextCoreFontEngineModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.TextCoreTextEngineModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.TextRenderingModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.TilemapModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.TLSModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UI.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UIElementsModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UIModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UmbraModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UnityAnalyticsCommonModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UnityAnalyticsModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UnityConnectModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UnityConsentModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UnityCurlModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UnityTestProtocolModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UnityWebRequestAssetBundleModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UnityWebRequestAudioModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UnityWebRequestModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UnityWebRequestTextureModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.UnityWebRequestWWWModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.VehiclesModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.VFXModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.VideoModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.VirtualTexturingModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.VRModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.WindModule.dll","Library/Bee/artifacts/WinPlayerBuildProgram/ManagedStripped/UnityEngine.XRModule.dll"]}}    

Payload for "WriteText Library/Bee/artifacts/WinPlayerBuildProgram/Data/app.info"

Gridbased
Persistent Object    

